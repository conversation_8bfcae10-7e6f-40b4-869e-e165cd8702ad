#!/usr/bin/bash
#Deploy redis in current directory
#Wriiten by cuiyi01(<EMAIL>)

function get_mysql_conf() {
    local idc=`hostname | awk -F'-' '{print $1}'`
    if [ x$idc == "xcq02" ]; then
        MYSQL_PORT=6202
        MYSQL_HOST=***********
        MYSQL_USER=bce_scs_w
        MYSQL_PASSWD=3gj2OxM1OrfoR1bm
    fi
}

get_mysql_conf

function get_old_master_redis_info() {
    mysql -P ${MYSQL_PORT} -h ${MYSQL_HOST} -u ${MYSQL_USER} -p${MYSQL_PASSWD} -e "use bce_scs;select floating_ip, port, cache_instance_type from cache_instance where cluster_id in (select id from cache_cluster where status != 10 and cluster_show_id = \"${1}\") and cache_instance_type = 2;" | grep 10. > old_master_redis.txt
}

function get_new_proxy_info() {
    mysql -P ${MYSQL_PORT} -h ${MYSQL_HOST} -u ${MYSQL_USER} -p${MYSQL_PASSWD} -e "use bce_scs;select floating_ip, port, cache_instance_type from cache_instance where cluster_id in (select id from cache_cluster where status != 10 and cluster_show_id = \"${1}\") and cache_instance_type = 0;" | grep 10. > new_proxy.txt
}

function merge_sql_result() {
    get_old_master_redis_info $1
    get_new_proxy_info $2
    python merge_sql_result.py ./old_master_redis.txt ./new_proxy.txt $3
    cat ./merge_reuslt.txt
}

merge_sql_result $1 $2 $3