#!/usr/bin/bash
#Deploy redis in current directory
#Wriiten by cuiyi01(<EMAIL>)

REDIS_BASE_CONF="./redis_base.conf"
REDIS_SERVER="./redis-server"
REDIS_CLI="./redis-cli"
REDIS_LOG_PATH="./log"
REDIS_DATA_PATH="./data"
REDIS_CONF_PATH="./conf"
DUMP_FILE="./params_dump"
REDIS_PASSWD="7DVng4luQz3iZ0Cz5MyoHmZ6I0LO4pJIwJPrI5H600H5SheG9PiTfCnHHRxglUqMDBNZaBIs46sUnbbCFo4UkQvp09gANdEpkJg6klRvbsZPmyg4pZQsRsZTGxjYozti"

function deploy_redis()
{
    PORT_START=$1
    PORT_END=$2

    if [ -z $PORT_START ] || [ -z $PORT_END ]; then
        echo "sh ./deploy_redis.sh (deploy_redis) PORT_START PORT_END"
    fi

    if [ $PORT_START -lt 10000 ] || [ $PORT_START -ge $PORT_END ]; then
        echo "Port params Invalid; Exit"
        exit 1
    fi

    for port in {${PORT_START}..${PORT_END}}; do
        if [ $(netstat -nultp 2>/dev/null | grep ":${port}" | wc -l) != 0 ]; then 
            echo "Port:${port} are occupied. Exit"
            exit 1 
        fi
    done

    [[ -d $REDIS_LOG_PATH ]] && rm -rf $REDIS_LOG_PATH
    mkdir -p $REDIS_LOG_PATH
    [[ -d $REDIS_DATA_PATH ]] && rm -rf $REDIS_DATA_PATH
    mkdir -p $REDIS_DATA_PATH
    [[ -d $REDIS_CONF_PATH ]] && rm -rf $REDIS_CONF_PATH
    mkdir -p $REDIS_CONF_PATH

    echo "${PORT_START} ${PORT_END}" > $DUMP_FILE
    for port in `seq $PORT_START $PORT_END`; do

        cp $REDIS_BASE_CONF $REDIS_CONF_PATH/redis_${port}.conf
        mkdir ${REDIS_DATA_PATH}/redis_${port}
        sed -i -e "s/8100/${port}/g"  $REDIS_CONF_PATH/redis_${port}.conf
        echo "Redis ${port} deployed."
    done
}

function start_redis() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        if [ $(netstat -nultp 2>/dev/null | grep ":${port}" | wc -l) != 0 ]; then 
            echo "Port:${port} are occupied. Exit"
            exit 1 
        fi
    done
    for port in `seq $PORT_START $PORT_END`; do
        $REDIS_SERVER $REDIS_CONF_PATH/redis_${port}.conf
        echo "Start ${port} redis"
    done
}

function stop_redis() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        redis_pid_file="${REDIS_DATA_PATH}/redis_${port}/redis_${port}.pid"
        if [ -e $redis_pid_file ]; then
            echo "Kill ${port} redis"
            kill -9 `cat $redis_pid_file`
        fi
    done
}

function show_redis() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        if [ $(netstat -nultp 2>/dev/null | grep ":${port}" | wc -l) != 0 ]; then 
            echo "Port:${port} redis running"
        fi
    done
}

function mount_as_slaves() {
    if [ ! -f ./merge_reuslt.txt ]; then
        echo "need sql reuslt"
        exit 1
    fi
    cat ./merge_reuslt.txt | while read line; do
        old_master_ip=`echo $line | awk '{print $1}'`
        old_master_port=`echo $line | awk '{print $2}'`
        local_port=`echo $line | awk '{print $5}'`
        echo "Mounting local redis $local_port to ${old_master_ip}:${old_master_port} as slave."
        $REDIS_CLI -p $local_port -a $REDIS_PASSWD config set appendonly yes
        $REDIS_CLI -p $local_port -a $REDIS_PASSWD config set auto-aof-rewrite-percentage 0
        $REDIS_CLI -p $local_port -a $REDIS_PASSWD slaveof $old_master_ip $old_master_port
        $REDIS_CLI -p $local_port -a $REDIS_PASSWD info | grep -A 7 Replication
    done
}

function aof_replay() {
    if [ ! -f ./merge_reuslt.txt ]; then
        echo "need sql reuslt" 
        exit 1
    fi
    cat ./merge_reuslt.txt | while read line; do
        new_proxy_ip=`echo $line | awk '{print $3}'`
        new_proxy_port=`echo $line | awk '{print $4}'`
        local_port=`echo $line | awk '{print $5}'`
        echo "aof replay $local_port to ${new_proxy_ip}:${new_proxy_ip} as slave."
        nohup ./redis-replay-aof --file data/redis_${local_port}/appendonly.aof --dest ${new_proxy_ip}:${new_proxy_port} --pipe_cmds > AOF_FILE_REPLAY_LOG.${local_port} 2>&1 &
    done
}

function check_redis_replication() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        $REDIS_CLI -p $port -a $REDIS_PASSWD info | grep -A 7 Replication
    done
}

function check_env() {
    if [ ! -f $REDIS_BASE_CONF ] || [ ! -f $REDIS_SERVER ] || [ ! -f $REDIS_CLI ]; then
        echo "Cannot find redis_base.conf redis-server redis-cli."
        exit 1
    fi
}

function usage() {
    echo "sh ./deploy_redis.sh (deploy_redis) PORT_START PORT_END"
    echo "sh ./deploy_redis.sh (start_redis|stop_redis|show_redis|slot_stat|show_relication_info|merge_slot_info|mount_as_slaves|bgrwriteaof|slaveofnoone|dbsize|aof_replay)"
}

function get_slot_info() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        nohup python ./slot_stat.py get_redis_slot_size 127.0.0.1 ${port} ./slot_info.${port} $REDIS_PASSWD &
    done
}

function merge_slot_info {
    python slot_stat.py merge_result ./ slot_info merged_slot_info
}

function show_relication_info() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        $REDIS_CLI -p $port -a $REDIS_PASSWD info Replication
    done
}

function bgrwriteaof() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        $REDIS_CLI -p $port -a $REDIS_PASSWD BGREWRITEAOF
    done
}

function slaveofnoone() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        $REDIS_CLI -p $port -a $REDIS_PASSWD SLAVEOF no one
    done
}

function dbsize() {
    PORT_START=$1
    PORT_END=$2
    for port in `seq $PORT_START $PORT_END`; do
        $REDIS_CLI -p $port -a $REDIS_PASSWD dbsize
    done
}

check_env

if [ x$1 == "xdeploy_redis" ]; then
    deploy_redis $2 $3
    exit 0
fi

if [ x$1 == "xstart_redis" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    start_redis $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xstop_redis" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    stop_redis $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xshow_redis" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    show_redis $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xslot_stat" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    get_slot_info $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xshow_relication_info" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    show_relication_info $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xbgrwriteaof" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    bgrwriteaof $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xslaveofnoone" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    slaveofnoone $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xdbsize" ]; then
    if [ ! -e $DUMP_FILE ]; then
        echo "You must deploy redis first."
        exit 1
    fi
    PORT_START=`cat $DUMP_FILE | awk '{print $1}'`
    PORT_END=`cat $DUMP_FILE | awk '{print $2}'`
    dbsize $PORT_START $PORT_END
    exit 0
fi

if [ x$1 == "xmerge_slot_info" ]; then
    merge_slot_info
    exit 0
fi

if [ x$1 == "xmount_as_slaves" ]; then
    mount_as_slaves
    exit 0
fi

if [ x$1 == "xaof_replay" ]; then
    aof_replay
    exit 0
fi

usage