#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# TODO File description.

作者: cuiyi01(<EMAIL>)
日期: 2018年6月21日 下午2:24:21
"""


import sys


def merget_sql_result(old_master_file, new_proxy_file, port_base):
    """merge sql result
    """
    with open(old_master_file, "rt") as f_in:
        old_master_lines = [line.split() for line in f_in.readlines()]
    with open(new_proxy_file, "rt") as f_in:
        new_proxy_lines = [line.split() for line in f_in.readlines()]

    new_proxy_num = len(new_proxy_lines)

    result = []

    for i, old_master_line in enumerate(old_master_lines):
        result.append("\t".join([
            old_master_line[0],
            old_master_line[1],
            new_proxy_lines[i % new_proxy_num][0],
            new_proxy_lines[i % new_proxy_num][1],
            str(port_base),
        ]))
        port_base += 1

    with open("./merge_reuslt.txt", "wt") as f_out:
        f_out.write("\n".join(result))


if __name__ == "__main__":
    merget_sql_result(sys.argv[1], sys.argv[2], int(sys.argv[3]))