#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
#对Redis的slot信息和metaserver的slot信息进行对账，解决slot信息不对的问题.

作者: cuiyi01(<EMAIL>)
日期: May 17, 2019 at 10:51:37 AM
"""


import logging
import redis
import sys
import time


def split_ms_ip(ms_ip):
    """split metaserver ip
    """
    sp_ip = ms_ip.strip().split(",")
    if len(sp_ip) == 1:
        return sp_ip[0], sp_ip[0]
    else:
        return sp_ip[0], sp_ip[1]


class MSHandler(object):
    """Deal with Metaserver query
    """

    def __init__(self, ip, port, passwd, retry=2, timeout=2):
        """init
        """
        self._ip = ip
        self._port = port
        self._password = passwd
        self._retry = retry
        self._timeout = timeout
        self._conn = redis.Redis(self._ip, self._port, password=self._password,
                                 socket_connect_timeout=self._timeout, socket_timeout=self._timeout)

    def execute_cmd(self, cmd, *args):
        """Execute metaserver cmd
        """
        retry_times = 0
        while retry_times < self._retry:
            retry_times += 1
            try:
                resp = self._conn.execute_command(cmd, *args)
                if isinstance(resp, list) and len(resp) % 2 == 0 and len(resp) > 0:
                    res = {}
                    for idx, resp_item in enumerate(resp):
                        if idx % 2 == 0:
                            res[resp_item] = resp[idx + 1]
                    return res
                else:
                    return resp

            except Exception as e:
                logging.exception("Send %s cmd to metaserver failed; Errmsg %s"
                                  % (cmd, e.message))
                continue
            finally:
                self._conn.connection_pool.disconnect()

    def get_instances(self, cluster_id):
        """get all instances info of the cluster
        """
        # Get all redis instances
        instances = []
        cluster_info = self.execute_cmd("mcget", cluster_id)
        if cluster_info:
            shards = cluster_info["shard"].strip().split(",")
            for shard_id in shards:
                shard = self.execute_cmd("msget", int(shard_id))
                if shard:
                    shard_inst_list = [(int(shard["master"]),
                                        self.execute_cmd("mriget", int(shard["master"])))]
                    slaves = shard["slaves"].strip().split(",")
                    for slave_id in slaves:
                        shard_inst_list.append((int(slave_id),
                                                self.execute_cmd("mriget", int(slave_id))))
                    for inst_id, inst in shard_inst_list:
                        if inst:
                            floating_ip, fix_ip = split_ms_ip(inst["ip"])
                            cache_instance_type = 3 if inst["type"] == "1" else 2
                            port = inst["port"]
                            instances.append({
                                "id": inst_id,
                                "cluster_id": cluster_id,
                                "shard_id": int(shard_id),
                                "floating_ip": floating_ip,
                                "fix_ip": fix_ip,
                                "port": int(port),
                                "cache_instance_type": cache_instance_type
                            })
        # Get all proxy instances
        cluster_proxy_info = self.execute_cmd("mpget", cluster_id)
        if cluster_proxy_info:
            proxys = cluster_proxy_info["proxy_inst"].strip().split(",")
            for proxy_id in proxys:
                proxy = self.execute_cmd("mpiget", int(proxy_id))
                if proxy:
                    floating_ip, fix_ip = split_ms_ip(proxy["ip"])
                    instances.append({
                        "id": int(proxy_id),
                        "cluster_id": cluster_id,
                        "shard_id": cluster_id,
                        "floating_ip": floating_ip,
                        "fix_ip": fix_ip,
                        "port": int(cluster_info["pool_port"]),
                        "cache_instance_type": 0
                    })
        return instances

    def get_shard_slots(self, cluster_id):
        """get shard_id and slots
        """
        ret = dict()
        cluster_info = self.execute_cmd("mcget", cluster_id)
        if cluster_info:
            slot_list = cluster_info["slot"].strip().split(",")
            for slot_id, shard_id in enumerate(slot_list):
                ret.setdefault(int(shard_id), []).append(slot_id)
        return ret


def do_audit():
    """
    do audit
    """
    meta_ip = sys.argv[1]
    meta_port = int(sys.argv[2])
    cluster_id = int(sys.argv[3])

    ret_cmdlist = []
    ms_handler = MSHandler(meta_ip, meta_port, None)
    shard_slot_map = ms_handler.get_shard_slots(cluster_id)
    for inst in ms_handler.get_instances(cluster_id):
        if inst["cache_instance_type"] == 3:
            conn = redis.Redis(inst["floating_ip"], inst["port"])
            for slot_id in shard_slot_map[inst["shard_id"]]:
                time.sleep(0.005)
                slot_flag = conn.execute_command("slotidget", slot_id)
                if slot_flag.strip() != "1":
                    ret_cmdlist.append("./redis-cli -h %s -p %d slotidset %d 1"
                                       % (inst["ip"], inst["port"], slot_id))
    for cmd in ret_cmdlist:
        print cmd


if __name__ == "__main__":
    do_audit()