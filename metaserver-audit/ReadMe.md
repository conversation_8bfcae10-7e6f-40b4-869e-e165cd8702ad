数据库与Metaserver对账工具
==========================

依赖
--------------------------
1. pymysql或MySqldb库
2. redis库

使用方式
--------------------------
将metaserver_audit.py拷贝到对应机器，执行：

    python metaserver_audit.py
    
这里假设metaserver缺失2200集群，则返回如下结果：
    
    ------------2200------------
    mpset 2200
    mcset 2200 2200 6379 1 1 1
    maset 2200 2200
    mpiset 9092 2200 10.107.233.189,192.168.0.29 bj
    mpiset 9093 2200 10.107.233.184,192.168.0.27 bj
    msset 9090 2200
    mriset 9090 9090 10.107.233.196,192.168.0.30 8181 1 1 bj
    mriset 9091 9090 10.107.233.164,192.168.0.28 8181 2 1 bj
    
也可以增加命令前缀方便操作

    python metaserver_audit.py './redis-cli -h 10.107.41.43 -p 7701'
    
返回结果如下：

    ------------2200------------
    ./redis-cli -h 10.107.41.43 -p 7701 mpset 2200
    ./redis-cli -h 10.107.41.43 -p 7701 mcset 2200 2200 6379 1 1 1
    ./redis-cli -h 10.107.41.43 -p 7701 maset 2200 2200
    ./redis-cli -h 10.107.41.43 -p 7701 mpiset 9092 2200 10.107.233.189,192.168.0.29 bj
    ./redis-cli -h 10.107.41.43 -p 7701 mpiset 9093 2200 10.107.233.184,192.168.0.27 bj
    ./redis-cli -h 10.107.41.43 -p 7701 msset 9090 2200
    ./redis-cli -h 10.107.41.43 -p 7701 mriset 9090 9090 10.107.233.196,192.168.0.30 8181 1 1 bj
    ./redis-cli -h 10.107.41.43 -p 7701 mriset 9091 9090 10.107.233.164,192.168.0.28 8181 2 1 bj