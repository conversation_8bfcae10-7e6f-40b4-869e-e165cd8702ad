#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
此脚本进行metaserver与mysql的对账；给出建议的修复命令.

作者: cuiyi01(<EMAIL>)
日期: Apr 25, 2019 at 2:03:46 PM
"""

import commands
import logging
try:
    import pymysql
except ImportError:
    import MySQLdb as pymysql
import redis
import sys


g_idc_config = {
    "cq02": {
        "host": "***********",
        "port": 6202,
        "user": "bce_scs_w",
        "password": "3gj2OxM1OrfoR1bm",
        "db": "bce_scs",
        "meta_host_list": ["scsmetaserver.bj.baidubce.com"],
        "meta_port_list": [80],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f",
        "region": "bj"
    },
    "gzns": {
        "host": "*************",
        "port": 6001,
        "user": "bce_scs_gzns_w",
        "password": "PVrvns97CC7iQtKy",
        "db": "bce_scs",
        "meta_host_list": ["scsmetaserver.gz.baidubce.com"],
        "meta_port_list": [80],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f",
        "region": "gz"
    },
    "szth": {
        "host": "*************",
        "port": 6300,
        "user": "bce_scs_w",
        "password": "f_UJYZzr5du4iOsj",
        "db": "bce_scs",
        "meta_host_list": [],
        "meta_port_list": [],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f",
        "region": "su"
    },
    "wh": {
        "host": "db.paas.whlocal.com",
        "port": 6203,
        "user": "paas_scs_w",
        "password": "paas_scs_w_pwd",
        "db": "paas_scs",
        "meta_host_list": [],
        "meta_port_list": [],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    },
    "bdbl": {
        "host": "*************",
        "port": 5254,
        "user": "bce_scs_w",
        "password": "IlUcU4hjCtbyOqOV4",
        "db": "bce_scs",
        "meta_host_list": ["scsmetaserver.bd.baidubce.com"],
        "meta_port_list": [80],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    }
}


def get_idc_config():
    """get db config according to idc
    """
    global g_idc_config
    status, hostname = commands.getstatusoutput("hostname")
    if status != 0:
        logging.error("Cannot obtain hostname")
        sys.exit(1)
    idc = hostname.split("-")[0]
    if idc not in g_idc_config:
        logging.error("db config of idc %s not exists" % idc)
        sys.exit(1)
    return g_idc_config[idc]


def split_ms_ip(ms_ip):
    """split metaserver ip
    """
    sp_ip = ms_ip.strip().split(",")
    return sp_ip[0], sp_ip[1]


class DbHandler(object):
    """处理数据库请求
    """

    def __init__(self, host, port, user, password, db):
        """init
        """
        self._db_conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            passwd=password,
            db=db
        )

    def get_cluster_ids(self):
        """
        get all cluster ids
        """
        cluster_ids = []
        sql = ("select id from cache_cluster where status not in (10, 12, 8) and version = 5001")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql)
                for row in cursor.fetchall():
                    cluster_ids.append(int(row[0]))
        except Exception as e:
            logging.exception("Query db failure; Errmsg: %s" % e.message)
        return cluster_ids

    def get_instances(self, cluster_id):
        """
        get all instances of the cluster
        """
        insts = []
        sql = ("select id, cluster_id, shard_id, fix_ip, floating_ip, cache_instance_type, port "
               "from cache_instance where cluster_id = %s")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql, (cluster_id, ))
                for row in cursor.fetchall():
                    insts.append({
                        "id": int(row[0]),
                        "cluster_id": int(row[1]),
                        "shard_id": int(row[2]),
                        "fix_ip": row[3],
                        "floating_ip": row[4],
                        "cache_instance_type": row[5],
                        "port": row[6]
                    })
        except Exception as e:
            logging.exception("Query db failure; Errmsg: %s" % e.message)
        return insts


class MSHandler(object):
    """Deal with Metaserver query
    """

    def __init__(self, ip, port, passwd, retry=2, timeout=2):
        """init
        """
        self._ip = ip
        self._port = port
        self._password = passwd
        self._retry = retry
        self._timeout = timeout
        self._conn = redis.Redis(self._ip, self._port, password=self._password,
                                 socket_connect_timeout=self._timeout, socket_timeout=self._timeout)

    def execute_cmd(self, cmd, *args):
        """Execute metaserver cmd
        """
        retry_times = 0
        while retry_times < self._retry:
            retry_times += 1
            try:
                resp = self._conn.execute_command(cmd, *args)
                if isinstance(resp, list) and len(resp) % 2 == 0 and len(resp) > 0:
                    res = {}
                    for idx, resp_item in enumerate(resp):
                        if idx % 2 == 0:
                            res[resp_item] = resp[idx + 1]
                    return res
                else:
                    return resp

            except Exception as e:
                logging.exception("Send %s cmd to metaserver failed; Errmsg %s"
                                  % (cmd, e.message))
                continue
            finally:
                self._conn.connection_pool.disconnect()

    def get_instances(self, cluster_id):
        """get all instances info of the cluster
        """
        # Get all redis instances
        instances = []
        cluster_info = self.execute_cmd("mcget", cluster_id)
        if cluster_info:
            shards = cluster_info["shard"].strip().split(",")
            for shard_id in shards:
                shard = self.execute_cmd("msget", int(shard_id))
                if shard:
                    shard_inst_list = [(int(shard["master"]),
                                        self.execute_cmd("mriget", int(shard["master"])))]
                    slaves = shard["slaves"].strip().split(",")
                    for slave_id in slaves:
                        shard_inst_list.append((int(slave_id),
                                                self.execute_cmd("mriget", int(slave_id))))
                    for inst_id, inst in shard_inst_list:
                        if inst:
                            floating_ip, fix_ip = split_ms_ip(inst["ip"])
                            cache_instance_type = 3 if inst["type"] == "1" else 2
                            instances.append({
                                "id": inst_id,
                                "cluster_id": cluster_id,
                                "shard_id": int(shard_id),
                                "floating_ip": floating_ip,
                                "fix_ip": fix_ip,
                                "cache_instance_type": cache_instance_type
                            })
        # Get all proxy instances
        cluster_proxy_info = self.execute_cmd("mpget", cluster_id)
        if cluster_proxy_info:
            proxys = cluster_proxy_info["proxy_inst"].strip().split(",")
            for proxy_id in proxys:
                proxy = self.execute_cmd("mpiget", int(proxy_id))
                if proxy:
                    floating_ip, fix_ip = split_ms_ip(proxy["ip"])
                    instances.append({
                        "id": int(proxy_id),
                        "cluster_id": cluster_id,
                        "shard_id": cluster_id,
                        "floating_ip": floating_ip,
                        "fix_ip": fix_ip,
                        "cache_instance_type": 0
                    })
        return instances


class AuditHandler(object):
    """Audit Handler
    """

    def __init__(self, db_handler, ms_handler, region):
        """init
        """
        self._db_handler = db_handler
        self._ms_handler = ms_handler
        self._db_cluster_map = dict()
        self._ms_cluster_map = dict()
        self._db_instance_map = dict()
        self._ms_instance_map = dict()
        self._region = region

    def fill_map(self):
        """fill db and ms map
        """
        for cluster_id in self._db_handler.get_cluster_ids():
            db_instances = self._db_handler.get_instances(cluster_id)
            if not db_instances:
                continue
            self._db_cluster_map[cluster_id] = {"proxy": set(), "redis": dict()}
            for inst in db_instances:
                self._db_instance_map[inst["id"]] = inst
                if inst["cache_instance_type"] == 0:
                    self._db_cluster_map[cluster_id]["proxy"].add(inst["id"])
                else:
                    self._db_cluster_map[cluster_id]["redis"].setdefault(
                        inst["shard_id"], set()).add(inst["id"])
            ms_instances = self._ms_handler.get_instances(cluster_id)
            if not ms_instances:
                continue
            self._ms_cluster_map[cluster_id] = {"proxy": set(), "redis": dict()}
            for inst in ms_instances:
                self._ms_instance_map[inst["id"]] = inst
                if inst["cache_instance_type"] == 0:
                    self._ms_cluster_map[cluster_id]["proxy"].add(inst["id"])
                else:
                    self._ms_cluster_map[cluster_id]["redis"].setdefault(
                        inst["shard_id"], set()).add(inst["id"])

    def get_create_cluster_cmd_list(self, cluster_id):
        """
        Get cmd list for create a cluster
        """
        return ["mpset %d" % cluster_id,
                "mcset %d %d 6379 1 1 1" % (cluster_id, cluster_id),
                "maset %d %d" % (cluster_id, cluster_id)]

    def get_proxy_diff(self, db_proxy_set, ms_proxy_set):
        """
        Get proxy diff commands
        """
        cmd_list = []
        for db_proxy_id in db_proxy_set:
            if db_proxy_id not in ms_proxy_set:
                inst = self._db_instance_map[db_proxy_id]
                cmd_list.append("mpiset %d %d %s %s" % (inst["id"], inst["cluster_id"],
                                                        ",".join([inst["floating_ip"], inst["fix_ip"]]),
                                                        self._region))
        for ms_proxy_id in ms_proxy_set:
            if ms_proxy_id not in db_proxy_set:
                inst = self._ms_instance_map[ms_proxy_id]
                cmd_list.append("mpidel %d" % inst["id"])
        return cmd_list

    def get_redis_diff(self, db_redis_set, ms_redis_set):
        """
        Get redis diff commands
        """
        cmd_list = []
        master_id = -1
        for db_redis_id in db_redis_set:
            db_inst = self._db_instance_map[db_redis_id]
            if db_redis_id in ms_redis_set:
                ms_inst = self._ms_instance_map[db_redis_id]
                if db_inst["cache_instance_type"] == 3 and ms_inst["cache_instance_type"] != 3:
                    master_id = db_redis_id
            else:
                if db_inst["cache_instance_type"] == 3 and not ms_redis_set:
                    cache_instance_type = 1
                else:
                    cache_instance_type = 2
                cmd_list.append("mriset %d %d %s %d %d 1 %s"
                                % (db_redis_id, db_inst["shard_id"],
                                   ",".join([db_inst["floating_ip"], db_inst["fix_ip"]]),
                                   db_inst["port"], cache_instance_type, self._region))

        if master_id >= 0:
            cmd_list.append("# Need to check all slaves online and blb configs before execute this command !!!")
            cmd_list.append("msmasterchange %d %d"
                            % (self._db_instance_map[master_id]["shard_id"], master_id))

        for ms_redis_id in ms_redis_set:
            if ms_redis_id not in db_redis_set:
                cmd_list.append("mridel %d" % self._ms_instance_map[ms_redis_id]["id"])

        return cmd_list

    def get_shard_diff(self, db_shard_map, ms_shard_map, cluster_id):
        """
        Get redis shard diff command
        """
        cmd_list = []
        for db_shard_id in db_shard_map.keys():
            if db_shard_id not in ms_shard_map:
                if ms_shard_map:
                    cmd_list.append("# Cluster Expansion info lost; Be careful!!!")
                cmd_list.append("msset %d %d" % (db_shard_id, cluster_id))
                cmd_list.extend(self.get_redis_diff(db_shard_map[db_shard_id], set()))
            else:
                cmd_list.extend(self.get_redis_diff(db_shard_map[db_shard_id],
                                                    ms_shard_map[db_shard_id]))
        return cmd_list

    def do_audit(self):
        """do audit and return cmd lists
        """
        result = dict()
        self.fill_map()
        for cluster_id, cluster_info in self._db_cluster_map.items():
            cmd_list = []
            if cluster_id not in self._ms_cluster_map:
                cmd_list.extend(self.get_create_cluster_cmd_list(cluster_id))
                cmd_list.extend(self.get_proxy_diff(cluster_info["proxy"], set()))
                cmd_list.extend(self.get_shard_diff(cluster_info["redis"], dict(), cluster_id))
            else:
                cmd_list.extend(self.get_proxy_diff(cluster_info["proxy"],
                                                    self._ms_cluster_map[cluster_id]["proxy"]))
                cmd_list.extend(self.get_shard_diff(cluster_info["redis"],
                                                    self._ms_cluster_map[cluster_id]["redis"],
                                                    cluster_id))
            result[cluster_id] = cmd_list

        for cluster_id in self._ms_cluster_map.keys():
            if cluster_id not in self._db_cluster_map:
                result[cluster_id] = ["mcdel %d" % cluster_id]

        return result


def main():
    """main
    """
    idc_config = get_idc_config()
    db_handler = DbHandler(host=idc_config["host"], port=idc_config["port"],
                           user=idc_config["user"], password=idc_config["password"],
                           db=idc_config["db"])
    ms_handler = MSHandler(idc_config["meta_host_list"][0],
                           idc_config["meta_port_list"][0], idc_config["meta_auth"])
    audit_handler = AuditHandler(db_handler, ms_handler, idc_config["region"])
    ret = audit_handler.do_audit()
    prefix = ""
    if len(sys.argv) > 1:
        prefix = sys.argv[1]
    for cluster_id, cmd_list in ret.items():
        print "------------%d------------" % cluster_id
        for cmd in cmd_list:
            print prefix + cmd


if __name__ == "__main__":
    main()
