today=`date +"%Y-%m-%d" -d "-1 days"`

path=/home/<USER>/scs/scs-tools/res_statistics_tools

echo today:$today > $path/$today.delete.cron.log

ls -lrt --color=never $path | grep finded_disclose_bcc | grep "csv$" | grep -v log | awk '{print$NF}' | grep $today > $path/filename_delete_$today.tmp

count=`cat $path/filename_delete_$today.tmp | wc -l`

if [ $count -eq 1 ];then
filename=`cat $path/filename_delete_$today.tmp | tr -d '\r'`
echo filename:$filename >> $path/$today.delete.cron.log
bcc_num=`cat $path/$filename | wc -l`
if [ $bcc_num -gt 100 ];then
echo "bcc nums is $bcc_num, too much." >> $path/$today.delete.cron.log
exit 0
fi

cd /home/<USER>/scs/octopus

cat $path/$filename | while read uuid iam_user_id
do 
echo $iam_user_id $uuid
#./octopus delete_vm $uuid $iam_user_id
echo
echo
done > $path/$filename.delete.log

else
echo "filename is not unique" >> $path/$today.delete.cron.log
exit 0
fi
