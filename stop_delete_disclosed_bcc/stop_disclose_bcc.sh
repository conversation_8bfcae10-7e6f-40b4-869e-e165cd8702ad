today=`date +"%Y-%m-%d" -d "-1 days"`

path=/home/<USER>/scs/scs-tools/res_statistics_tools

echo today:$today > $path/$today.stop.cron.log 

ls -lrt --color=never $path | grep finded_disclose_bcc | grep "csv$" | grep -v log | awk '{print$NF}' | grep $today > $path/filename_stop_$today.tmp

count=`cat $path/filename_stop_$today.tmp | wc -l`

if [ $count -eq 1 ];then
filename=`cat $path/filename_stop_$today.tmp | tr -d '\r'`

echo filename:$filename >> $path/$today.stop.cron.log

bcc_num=`cat $path/$filename | wc -l`
if [ $bcc_num -gt 100 ];then
echo "bcc nums is $bcc_num, too much." >> $path/$today.stop.cron.log
exit 0
fi

cd /home/<USER>/scs/octopus/cur_tool

cat $path/$filename | while read uuid iam_user_id
do
echo $iam_user_id $uuid
#sh /home/<USER>/scs/octopus/cur_tool/bcc_tool.sh stop $iam_user_id $uuid
echo
echo
done > $path/$filename.stop.log 

else
echo "filename is not unique" >> $path/$today.stop.cron.log
exit 0
fi
