#!/usr/bin/env python
# coding=utf-8

"""
    Author:       mengzicheng
    Date:         2022/03/14
    Description:  print log
"""

import logging
import os
import time
import random

LOG_DIR = os.path.dirname(os.path.realpath(__file__)) + '/log'
LOG_LEVEL = logging.DEBUG
CUR_TIME = int(time.time())
LOG_AVAI_TIME = CUR_TIME - 86400 * 7


class LogInfo(object):
    """
    print log
    """

    def __init__(self, filename):
        file_name_date = time.strftime("%Y%m%d", time.localtime())
        self.filename = '%s/%s.%s' % (LOG_DIR, filename, file_name_date)
        self.format = '%(asctime)s [%(levelname)s][%(filename)s][line:%(lineno)d] %(message)s'
        self.datefmt = '%Y-%m-%d %H:%M:%S'
        # 使用一个随机数，如果在一个进程中，创建了两个同名的logger，会多次打印日志
        self.logger = logging.getLogger(str(random.randint(0, 100000)))  # 创建日志记录器
        self.level = LOG_LEVEL

    def get_log(self):
        """
        返回类
        """
        # 创建目录
        if not os.path.exists(LOG_DIR):
            os.mkdir(LOG_DIR)
        self.logger.setLevel(self.level)
        format = logging.Formatter(self.format, datefmt=self.datefmt)
        # 日志输出到文件
        file_handler = logging.FileHandler(self.filename)  # 创建文件处理器
        file_handler.setLevel(self.level)
        file_handler.setFormatter(format)
        # 使用StreamHandler输出到屏幕
        console = logging.StreamHandler()  # 创建日志处理器
        console.setLevel(self.level)
        console.setFormatter(format)
        # 不打控制台了
        self.logger.addHandler(file_handler)
        # self.logger.addHandler(console)
        return self.logger

    @classmethod
    def rm_log(cls, prefix):
        """clear log"""
        for root, dirs, files in os.walk(LOG_DIR):
            for file_name in files:
                if not file_name.startswith(prefix):
                    continue
                tmp_list = file_name.split('.')
                if len(tmp_list) == 2:
                    try:
                        log_time = int(time.mktime(time.strptime(tmp_list[1], "%Y%m%d")))
                        if log_time < LOG_AVAI_TIME:
                            os.remove('%s/%s' % (LOG_DIR, file_name))
                    except Exception as e:
                        return False

if __name__ == "__main__":
    log_info = LogInfo("monitor-log")
    logger = log_info.get_log()
    logger.info("test")
    LogInfo.rm_log("monitor-log")