#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Description: bcm point count save for scs
Authors: <AUTHORS>
Date:    2022/05/17
"""

import time
import datetime
import os
import json
import urllib
import hmac
import hashlib
import urlparse
import requests
import pymysql
import sys
import argparse

import ConfigParser
import sts_service

from debug_info import LogInfo
logger = None

SIGNING_PREFIX = 'bce-auth-v1'
SIGNING_HEADER_INCLUDE = ['host', 'content-md5']
SIGNING_HEADER_EXCLUDE = ['x-bce-request-id', 'x-bce-security-token']
MONITOR_CONF = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'conf/monitor.conf')
RETRY_TIME = 3

def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret

def get_all_shards(user_id):
    """get all shards
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    with sql_conn.cursor() as cursor:
        cursor.execute("select hash_name, cluster_show_id, iam_user_id, version, cache_cluster.create_time"
                       " from cache_cluster,cache_instance "
                       "where cache_cluster.status not in (0, 10, 12, 58) and "
                       "cache_cluster.id=cache_instance.cluster_id"
                       " and cache_instance.cache_instance_type=3 and version <> 1001 "
                       "and iam_user_id='{}'".format(user_id))
        for row in cursor.fetchall():
            rows.append({
                "hash_name": row[0],
                "cluster_show_id": row[1],
                "iam_user_id": row[2],
                "version": row[3],
                "create_time": row[4]
            })
    sql_conn.close()

    ret = {}
    for row in rows:
        # 因为历史元数据存在hash_name不一致的情况,csagent做了兼容,7001版本统一加0
        if row['version'] == 7001:
            suffix = "0"
        else:
            suffix = row["hash_name"].split('_')[-1]
        if row["cluster_show_id"] in ret:
            ret[row["cluster_show_id"]]["shard_ids"].append(row["cluster_show_id"] + "-" + suffix)
        else:
            ret[row["cluster_show_id"]] = {
                "create_time": row['create_time'],
                "shard_ids": [row["cluster_show_id"] + "-" + suffix, ]
            }
    return ret

def insert_history_data(rows):
    """insert into history data
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    with sql_conn.cursor() as cursor:
        for row in rows:
            cursor.execute("insert ignore into bce_scs_x1_task.port_alive_count(app_id, cluster_id, user_id, "
                           "day, start_time, end_time, total_count, success_count, fail_count, lost_count, illegal_utc_time) "
                           "values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
                           (row['app_id'], row['cluster_id'], row['user_id'], row['day'], row['start_time'],
                            row['end_time'], row['total_count'], row['success_count'], row['fail_count'],
                            row['lost_count'], row['illegal_utc_time']))
    sql_conn.close()

def get_time_slice_sequence(cluster_create_time, day):
    """按照1个小时切分时间片
    """
    start_time = day - datetime.timedelta(hours=8)
    end_time = day + datetime.timedelta(hours=16)
    sequence = []
    # 15分钟展示判断为实例创建成功的时间
    if cluster_create_time + datetime.timedelta(minutes=15) > end_time:
        return sequence
    if cluster_create_time + datetime.timedelta(minutes=15) > start_time:
        start_time = cluster_create_time + datetime.timedelta(minutes=15)
    # 把秒数归0,防止查询到无数据的时间点,测试有效果
    start_time -= datetime.timedelta(seconds=start_time.second, microseconds=start_time.microsecond)
    delta_hour = datetime.timedelta(hours=1)
    while (start_time + delta_hour) < end_time:
        sequence.append([start_time, start_time + delta_hour])
        start_time += delta_hour + datetime.timedelta(minutes=1)
    sequence.append([start_time, end_time])
    return sequence


def bcm_data_query(region, url, scope, user_id, statistics, cluster_show_id,
                   node_name, start_time, end_time, iam_token):
    """bcm_data_query"""
    metric_names = "RedisLocalCli"
    dimensions = "ClusterId:{};NodeId:{}".format(cluster_show_id, node_name)

    url = '{endpoint}/json-api/v1/metricdata/{userId}/{scope}/{metricName}' \
          '?region={region}&statistics[]={statistics}&periodInSecond=60&'\
          'startTime={startTime}&endTime={endTime}&dimensions={dimensions}'.format(
            endpoint=url, userId=user_id, scope=scope, metricName=metric_names, statistics=statistics,
            region=region, startTime=start_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
            endTime=end_time.strftime('%Y-%m-%dT%H:%M:%SZ'), dimensions=dimensions)

    headers = {'accept': 'application/json'}

    get_authorization('GET', url, headers, iam_token['access_key'], iam_token['secret_key'],
                      iam_token['sign_token'])
    try_time = 0
    while try_time < RETRY_TIME:
        try_time += 1
        try:
            res = requests.get(url, headers=headers)
            ret = res.text
            if res.status_code / 100 != 2:
                logger.error('[url:%s]Request send monitor data failed: code[%s], return_data[%s]' % \
                      (url, res.status_code, ret))
                logger.info('send monitor bcm retry time:%s' % try_time)
                continue
            logger.info('send bcm monitor data request success: header[%s], url:%s return_data[%s]' % \
                  (headers, url, ret))
            data_points = json.loads(ret).get("dataPoints", [])
            return data_points
        except Exception as e:
            logger.error('[url:%s][receive:%s]Send monitor info fail: %s' % (url, ret, e))
            logger.info('send monitor bcm retry time:%s' % try_time)
    return []

def get_authorization(method, url, header, ak, sk, sign_token, duration="1800"):
    """Get an authorization signature for current request."""
    url = urlparse.urlparse(url)
    utctime = datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
    logger.info('Sign with access key: %s.', ak)
    logger.info('Sign with secret key: %s.', sk)
    header['x-bce-date'] = utctime
    header.setdefault('host', url.netloc)
    if sign_token:
        header['x-bce-security-token'] = encoded(sign_token)
    # signing key
    values = '/'.join([SIGNING_PREFIX, ak, utctime, duration])
    signing_key = hmac.new(encoded(sk), encoded(values), hashlib.sha256)
    signing_key = signing_key.hexdigest()
    logger.debug('Signature will be generated by key: %s.', signing_key)
    # signing body
    header_keys, header_values = format_headers(header)
    signing_body = '\n'.join([
        method,
        url.path,
        '&'.join(format_querystring(url.query)),
        '\n'.join(header_values),
    ])
    logger.debug('Signature will be generated by body: %s.', signing_body)
    signature = hmac.new(encoded(signing_key), encoded(signing_body), hashlib.sha256).hexdigest()
    logger.debug('Signature generated is: %s.', signature)
    header['authorization'] = '/'.join([
        SIGNING_PREFIX,
        ak,
        utctime,
        duration,
        ';'.join(header_keys),
        signature,
    ])


def format_querystring(querystring):
    """Sort and encode the keys and values in query string."""
    result = []
    for part in querystring.split('&'):
        if part:
            pos = part.find('=')
            if pos != -1:
                key = urllib.unquote(part[:pos])
                value = urllib.unquote(part[pos + 1:])
            else:
                key = urllib.unquote(part)
                value = ''
            result.append(urllib.quote(key) + '=' + urllib.quote(value))
    return sorted(result)


def encoded(value):
    """Return the `value` in utf-8 encoding."""
    if isinstance(value, unicode):
        return value.encode('utf-8')
    elif isinstance(value, str):
        return value
    return str(value)


def format_headers(header):
    """Sort and encode the keys and values in request header."""
    header_keys = []
    header_values = []
    for key, value in header.items():
        key = key.lower()
        if key in SIGNING_HEADER_EXCLUDE:
            pass
        elif value and (key in SIGNING_HEADER_INCLUDE or key.startswith('x-bce-')):
            header_keys.append(key)
            header_values.append('%s:%s' % (key, urllib.quote(value)))
    return sorted(header_keys), sorted(header_values)


class BcmPointCountSaver:
    """BcmPointCountSaver"""

    def __init__(self, deploy_conf, bcm_conf, sts_conf):
        self.iam_service = sts_service.StsService(sts_conf['scs_auth_iam_url'],
                                                  sts_conf['scs_auth_ak'],
                                                  sts_conf['scs_auth_sk'],
                                                  sts_conf['scs_auth_role_name'], logger)
        self.bcm_conf = bcm_conf
        self.deploy_conf = deploy_conf

    def acquire_iam_token(self, user_id):
        """use lump acquire iam token"""
        result = {
            'access_key': None,
            'secret_key': None,
            'sign_token': None,
            'user_token': None,
            'urls': {}
        }

        auth_info = self.iam_service.get_service_authorization(user_id)
        if auth_info is None:
            return None

        if 'accessKeyId' in auth_info:
            result['access_key'] = auth_info['accessKeyId']
        if 'secretAccessKey' in auth_info:
            result['secret_key'] = auth_info['secretAccessKey']
        if 'sessionToken' in auth_info:
            result['sign_token'] = auth_info['sessionToken']
        if 'token' in auth_info and 'id' in auth_info['token']:
            result['user_token'] = auth_info['token']['id']

        if 'token' not in auth_info or 'catalog' not in auth_info['token']:
            return None

        if 'access_key' in result and 'secret_key' in result and 'sign_token' in result and 'user_token' in result:
            logger.info('Acquire iam token for user %s success.' % user_id)
            return result
        else:
            logger.error('Acquire iam token for user %s fail.' % user_id)
            return None

    def get_result_for_one_shard(self, user_id, cluster_show_id,
                                 node_name, iam_token, time_slice_sequence):
        """get_result"""
        illegal_utc_time_list = []
        result = {"success_count": 0,
                  "fail_count": 0,
                  "lost_count": 0,
                  "total_count": 0,
                  "illegal_utc_time": ""}
        # 如果1个时间点有多个数据,则通过最大值判断
        statistics = "maximum"
        last_data_point_illegal = False
        for time_slice in time_slice_sequence:
            data_points = bcm_data_query(self.deploy_conf["region"], self.bcm_conf['url'], self.bcm_conf['scope'],
                                         user_id, statistics, cluster_show_id,
                                         node_name, time_slice[0], time_slice[1], iam_token)
            if not data_points:
                logger.warning('Get no data points')
                continue
            result['total_count'] += len(data_points)
            for data_point in data_points:
                if "maximum" not in data_point:
                    if last_data_point_illegal:
                        result["lost_count"] += 1
                        illegal_utc_time_list.append(data_point["timestamp"])
                    last_data_point_illegal = True
                    continue
                if int(data_point["maximum"]) != 100:
                    if last_data_point_illegal:
                        result["fail_count"] += 1
                        illegal_utc_time_list.append(data_point["timestamp"])
                    last_data_point_illegal = True
                    continue
                result["success_count"] += 1
                last_data_point_illegal = False
        # 最多保留100条数据
        result['illegal_utc_time'] = ",".join(illegal_utc_time_list[:100])
        return result

    def run(self, day, user_id, save):
        """run"""
        for i in range(RETRY_TIME):
            try:
                result = []
                shards = get_all_shards(user_id)
                logger.info("Get shards for %s success, shard count: %d" % (user_id, len(shards)))
                iam_token = self.acquire_iam_token(user_id)
                if not iam_token:
                    logger.error("acquire_iam_token fail")
                    continue
                for cluster_show_id, cluster_info in shards.items():
                    time_slice_sequence = get_time_slice_sequence(cluster_info["create_time"], day)
                    if not time_slice_sequence:
                        continue
                    for shard in cluster_info['shard_ids']:
                        result_for_one_shard = self.get_result_for_one_shard(user_id, cluster_show_id, shard,
                                                               iam_token, time_slice_sequence)
                        result_for_one_shard['user_id'] = user_id
                        result_for_one_shard['day'] = day.strftime('%Y-%m-%d')
                        result_for_one_shard['app_id'] = cluster_show_id
                        result_for_one_shard['cluster_id'] = shard
                        result_for_one_shard['start_time'] = \
                            time_slice_sequence[0][0].strftime('%Y-%m-%d %H:%M:%S')
                        result_for_one_shard['end_time'] = \
                            time_slice_sequence[-1][-1].strftime('%Y-%m-%d %H:%M:%S')
                        result.append(result_for_one_shard)
                if save == 'yes':
                    insert_history_data(result)
                else:
                    for row in result:
                        print row
                        logger.info(str(row))
                break
            except Exception as e:
                logger.error("Get data for %s failed, retry time: %d" % (user_id, i), exc_info=True)
                continue
        else:
            return False
        return True

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='Scs collect sla tools.')
    parser.add_argument('-m', '--run_mode', choices=['default', 'manual'], required=True)
    parser.add_argument('-s', '--save', choices=['yes', 'no'], required=True)
    parser.add_argument('-d', '--day', required=False, help="eg.1970-01-01")
    parser.add_argument('-u', '--user_id', required=False)
    args = parser.parse_args()

    LogInfo.rm_log("bcm_point_count_save")
    logger = LogInfo('bcm_point_count_save').get_log()

    if args.run_mode == "manual" and (not args.day or not args.user_id):
        print("parameters need day and user_id where run mode in manual")
        sys.exit(2)
    if args.run_mode == "manual":
        try:
            datetime.datetime.strptime(args.day, "%Y-%m-%d")
        except ValueError:
            print("parameters day in wrong format, eg:1971-01-01")
            sys.exit(2)

    monitor_conf = ConfigParser.RawConfigParser()
    bcm_conf = {}
    sts_conf = {}
    deploy_conf = {}
    try:
        monitor_conf.read(MONITOR_CONF)
        if monitor_conf.has_section('monitor_bcm'):
            bcm_conf['scope'] = monitor_conf.get('monitor_bcm', 'scope')
            bcm_conf['url'] = monitor_conf.get('monitor_bcm', 'url')

        if monitor_conf.has_section('sts'):
            sts_conf['scs_auth_role_name'] = monitor_conf.get('sts', 'scs_auth_role_name')
            sts_conf['scs_auth_iam_url'] = monitor_conf.get('sts', 'scs_auth_iam_url')
            sts_conf['scs_auth_ak'] = monitor_conf.get('sts', 'scs_auth_ak')
            sts_conf['scs_auth_sk'] = monitor_conf.get('sts', 'scs_auth_sk')

        if monitor_conf.has_section('deploy'):
            deploy_conf['region'] = monitor_conf.get('deploy', 'region')

    except:
        print("get conf fail")
        logger.error("get conf fail", exc_info=True)
        sys.exit(2)


    if args.run_mode == "default":
        day = datetime.date.today() - datetime.timedelta(days=1)
        now = datetime.datetime.now()
        day = now - datetime.timedelta(days=1, hours=now.hour, minutes=now.minute,
                                       seconds=now.second, microseconds=now.microsecond)
    else:
        day = datetime.datetime.strptime(args.day, "%Y-%m-%d")
    bcm_point_count_saver = BcmPointCountSaver(deploy_conf, bcm_conf, sts_conf)
    if args.user_id:
        bcm_point_count_saver.run(day, args.user_id, args.save)
    else:
        for user_id in ["f7ac4b5b395846389a889f7b89e9f030", "b9c676e017c84b0ab1a56e51fa9b1381"]:
            bcm_point_count_saver.run(day, user_id, args.save)