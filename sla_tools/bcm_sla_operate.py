#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Description: bcm sla operate
Authors: <AUTHORS>
Date:    2023/12/17
"""

import datetime
import pymysql
import argparse

from debug_info import LogInfo
logger = None

def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_all_count_data(start_time, end_time):
    """get all count data
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    with sql_conn.cursor() as cursor:
        cursor.execute("select user_id, app_id, cluster_id, fail_count, lost_count "
                       "from bce_scs_x1_task.port_alive_count where "
                       "start_time >= '{}' "
                       "and end_time <='{}'".format(start_time, end_time))
        for row in cursor.fetchall():
            rows.append({
                "user_id": row[0],
                "app_id": row[1],
                "cluster_id": row[2],
                "fail_count": row[3],
                "lost_count": row[4]
            })
    sql_conn.close()

    ret = {}
    for row in rows:
        if row['app_id'] not in ret:
            ret[row['app_id']] = {"user_id": row['user_id'], "cluster_ids": [row['cluster_id'],], 'illegal_count': 0}
        else:
            if row['cluster_id'] not in ret[row['app_id']]['cluster_ids']:
                ret[row['app_id']]['cluster_ids'].append(row['cluster_id'])
        ret[row['app_id']]['illegal_count'] += row['fail_count'] + row['lost_count']
    return ret

def calculate_ratio(count_data, start_time, end_time):
    """calculate ratio"""
    rows = []
    total_count = int((end_time - start_time).total_seconds() / 60)
    for app_id, info in count_data.items():
        ratio = round(float(total_count - info['illegal_count']) / total_count * 100, 3)
        rows.append({"app_id": app_id, "user_id": info['user_id'], "ratio": ratio})
    return rows

def insert_port_alive_ratio(rows):
    """insert into port alive ratio
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    with sql_conn.cursor() as cursor:
        cursor.execute("begin")
        for row in rows:
            cursor.execute("insert into bce_scs_x1_task.port_alive_ratio(app_id, user_id, status, "
                           "ratio, create_at) values(%s, %s, %s, %s, %s)",
                           (row['app_id'], row['user_id'], 'new', row['ratio'],
                            datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        cursor.execute("commit")
    sql_conn.close()

def calculate_main():
    """calculate_main
    """
    try:
        now = datetime.datetime.now()
        end_time = now - datetime.timedelta(hours=now.hour + 8, minutes=now.minute,
                                   seconds=now.second, microseconds=now.microsecond)
        start_time = end_time - datetime.timedelta(days=7)
        count_data = get_all_count_data(start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                        end_time.strftime('%Y-%m-%d %H:%M:%S'))
        insert_port_alive_ratio(calculate_ratio(count_data, start_time, end_time))
        return True
    except Exception as e:
        logger.error('Calculate ratio fail.', exc_info=True)
        return False

def change_ratio_view():
    """change ratio view
    """
    try:
        sql_conn = pymysql.connect(**get_mysql_config())
        with sql_conn.cursor() as cursor:
            cursor.execute("begin")
            cursor.execute("update bce_scs_x1_task.port_alive_ratio set status='deleted' where status='inuse'")
            cursor.execute("update bce_scs_x1_task.port_alive_ratio set status='inuse' where status='new'")
            cursor.execute("commit")
        sql_conn.close()
        return True
    except Exception as e:
        logger.error('Change ratio view fail.', exc_info=True)
        return False

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='Scs sla tools.')
    parser.add_argument('-a', '--action', choices=['calculate', 'change_view'], required=True)
    args = parser.parse_args()
    LogInfo.rm_log("bcm_sla_operate")
    logger = LogInfo('bcm_sla_operate').get_log()
    if args.action == "change_view":
        if change_ratio_view():
            logger.info("Change view success")
        else:
            logger.info("Change view fail")
    if args.action == "calculate":
        if calculate_main():
            logger.info("Calculate success")
        else:
            logger.info("Calculate fail")