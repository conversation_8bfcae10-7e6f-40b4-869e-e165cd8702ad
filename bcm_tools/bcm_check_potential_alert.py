#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
check potential alert
"""
import HTMLParser
import argparse
import datetime
import sys
import time

import requests
import os
import logging
import logging.handlers
import ConfigParser
import pymysql

import lib

g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")

TYPES = ["Instance", "Cluster"]
MONITOR_CONF = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'conf/monitor.conf')

def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret

def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)

def get_all_clusters(user_id):
    """get all clusters
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    with sql_conn.cursor() as cursor:
        cursor.execute("select cluster_show_id, status from userinfo, cache_cluster where "
                       "userinfo.id=cache_cluster.user_id and status not in (10, 12) and iam_user_id=%s", user_id)
        for row in cursor.fetchall():
            rows.append(row[0])
    sql_conn.close()
    return rows

def http_get(url, params):
    """
    Args:
        url: url
        params: params
    Returns: data or {}

    """
    try:
        response = requests.get(url, params=params)
        logging.info("Request url status:%s text:%s" % (response.status_code, response.text))
        if response.status_code == 200:
            return response.json()
        else:
            return {}
    except requests.exceptions.RequestException:
        logging.error("Request url exception:", exc_info=True)
        return {}


def query_mon_items(user_id, config_url):
    """

    Args:
        user_id: user iam id
        config_url: bcm config url
    Returns:

    """
    mon_configs = []
    for type_name in TYPES:
        data = http_get(config_url, dict(pageNo=1, pageSize=1, order="desc", typeName=type_name,
                                             userId=user_id, scope="BCE_SCS"))
        if data:
            if data['totalCount'] == 0:
                continue
            for index in range(1, (data['totalCount'] / 10) + 2):
                data = http_get(config_url, dict(pageNo=index, pageSize=10, order="desc", typeName=type_name,
                                                     userId=user_id, scope="BCE_SCS"))
                for item in data['result']:
                    tmp = {"src_name": item["srcName"],
                           "names": item["monitorObject"]["names"],
                           "type": item["monitorObject"]["type"],
                           "type_name": item["monitorObject"]["typeName"],
                           "region": item["region"],
                           "rules": []}
                    for rule in item["rules"]:
                        for r in rule:
                            tmp["rules"].append({"metric": r["metric"],
                                                 "comparison_operator": r["comparisonOperator"],
                                                 "threshold": r["threshold"]})
                    mon_configs.append(tmp)
    return mon_configs


def query_instance_group_members(user_id, region, type_name, group_id, group_members_url):
    """

    Args:
        user_id: user iam id
        region: region
        type_name: eg.Instance,Cluster
        group_id: id of group
        group_members_url:
    Returns:

    """
    names = []
    data = http_get(group_members_url.format(user_id=user_id),
                    dict(pageNo=1, pageSize=1, order="desc", typeName=type_name,
                         region=region, userId=user_id, serviceName="BCE_SCS", id=group_id,
                         viewType="DETAIL_VIEW"))
    if data:
        if data['totalCount'] == 0:
            return names
        for index in range(1, (data['totalCount'] / 10) + 2):
            data = http_get(group_members_url.format(user_id=user_id),
                            dict(pageNo=index, pageSize=10, order="desc", typeName=type_name,
                                 region=region, userId=user_id, serviceName="BCE_SCS", id=group_id,
                                 viewType="DETAIL_VIEW"))
            for item in data['result']:
                name = []
                for pair in item:
                    if pair["itemDimension"] and pair["itemValue"]:
                        if type_name == "Instance" and "___" in pair["itemValue"]:
                            cluster_id, node_id = pair["itemValue"].split("___")
                            name.append("ClusterId:{}".format(cluster_id))
                            name.append("NodeId:{}".format(node_id))
                        else:
                            name.append("{}:{}".format(pair["itemName"], pair["itemValue"]))
                names.append(";".join(name))
    return names


def get_items_to_compare(user_id, bcm_conf, deploy_conf):
    """
    Args:
        user_id:
        :

    Returns:
        Examples:[{'threshold': u'-1', 'metric': u'MemUsedBytes',
            'name': u'ClusterId:scs-bj-vstcprsvifjd', 'comparison_operator': u'>'},]
    """
    compare_items = []
    items = query_mon_items(user_id, bcm_conf["config_url"])
    for item in items:
        if item['region'] != deploy_conf["region"]:
            continue
        if item['type'] == "APP":
            logging.info("User:%s has app level mon config, not support, exit")
            return []
        if item['type'] == "INSTANCE":
            for rule in item['rules']:
                for name in item['names']:
                    compare_items.append({
                        "name": name,
                        "metric": rule["metric"],
                        "comparison_operator": rule["comparison_operator"],
                        "threshold": rule["threshold"]
                    })
        if item['type'] == "SERVICE":
            for rule in item['rules']:
                for name in query_instance_group_members(user_id,
                                                         item['region'],
                                                         item['type_name'],
                                                         item['src_name'],
                                                         bcm_conf['group_members_url']):
                    compare_items.append({
                        "name": name,
                        "metric": rule["metric"],
                        "comparison_operator": rule["comparison_operator"],
                        "threshold": rule["threshold"]
                    })
    return compare_items


def main(user_id, bcm_conf, sts_conf, deploy_conf, cluster_show_id=""):
    """main"""
    init_log(os.path.join(g_script_path, "log/check_potential_alert"))
    online_cluster_show_ids = get_all_clusters(user_id)
    check_fail = []
    potential_alert = []
    compare_items = get_items_to_compare(user_id, bcm_conf, deploy_conf)
    if not compare_items:
        logging.warning("User:%s has no mon")
        return
    queryer = lib.BcmDataQueryer(bcm_conf['url'], bcm_conf['scope'],
                                 sts_conf['scs_auth_iam_url'], sts_conf['scs_auth_ak'],
                                 sts_conf['scs_auth_sk'], sts_conf['scs_auth_role_name'],
                                 logging)
    end_time = datetime.datetime.utcfromtimestamp(int(time.time()) - 120).strftime("%Y-%m-%dT%H:%M:%SZ")
    start_time = datetime.datetime.utcfromtimestamp(int(time.time()) - 600).strftime("%Y-%m-%dT%H:%M:%SZ")
    for compare_item in compare_items:
        if cluster_show_id:
            if cluster_show_id not in compare_item["name"]:
                continue
        for online_cluster_show_id in online_cluster_show_ids:
            if online_cluster_show_id in compare_item["name"]:
                data_points = queryer.do(deploy_conf['region'], user_id, compare_item["name"],
                                         compare_item["metric"], start_time, end_time)
                if not data_points:
                    check_fail.append(compare_item)
                    continue
                else:
                    for data_point in data_points:
                        if "average" not in data_point:
                            continue
                        if eval(HTMLParser.HTMLParser().unescape(str(data_point['average']) +
                                                                 compare_item['comparison_operator'] +
                                                                 compare_item["threshold"])):
                            potential_alert.append(compare_item)
                            break
                break
        else:
            logging.info("Cluster:%s is offline " % compare_item["name"])
    print "PotentialAlert:"
    for compare_item in potential_alert:
        print compare_item
    print "CheckFail:"
    for compare_item in check_fail:
        print compare_item
    return

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='Scs tools for check potentail bcm alert.')
    parser.add_argument('-u', '--user_id', required=True)
    parser.add_argument('-s', '--cluster_show_id', required=False)
    args = parser.parse_args()
    monitor_conf = ConfigParser.RawConfigParser()
    bcm_conf = {}
    sts_conf = {}
    deploy_conf = {}
    try:
        monitor_conf.read(MONITOR_CONF)
        if monitor_conf.has_section('monitor_bcm'):
            bcm_conf['scope'] = monitor_conf.get('monitor_bcm', 'scope')
            bcm_conf['url'] = monitor_conf.get('monitor_bcm', 'url')
            bcm_conf['config_url'] = monitor_conf.get('monitor_bcm', 'config_url')
            bcm_conf['group_members_url'] = monitor_conf.get('monitor_bcm', 'group_members_url')
        if monitor_conf.has_section('sts'):
            sts_conf['scs_auth_role_name'] = monitor_conf.get('sts', 'scs_auth_role_name')
            sts_conf['scs_auth_iam_url'] = monitor_conf.get('sts', 'scs_auth_iam_url')
            sts_conf['scs_auth_ak'] = monitor_conf.get('sts', 'scs_auth_ak')
            sts_conf['scs_auth_sk'] = monitor_conf.get('sts', 'scs_auth_sk')

        if monitor_conf.has_section('deploy'):
            deploy_conf['region'] = monitor_conf.get('deploy', 'region')

    except:
        print("get conf fail")
        sys.exit(1)
    if args.cluster_show_id:
        main(args.user_id, bcm_conf, sts_conf, deploy_conf, args.cluster_show_id)
    else:
        main(args.user_id, bcm_conf, sts_conf, deploy_conf)