#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Description: bcm err check for scs
Authors: <AUTHORS>
Date:    2022/05/17
"""

import time
import datetime
import os
import logging
import json
import requests
import pymysql
import sys
import commands
import ConfigParser

from multiprocessing import Pool
from multiprocessing.dummy import Pool as ThreadPool

from debug_info import LogInfo
from bns_service import BNSService

MONITOR_CONF = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'conf/bcc_noah_monitor.conf')

BOT_TOKEN = 'df9470555ca9af0e119aa71b23e8a1dfb'

logger = None


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_all_vm_units():
    """get all clusters
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    with sql_conn.cursor() as cursor:
        cursor.execute("select id, floating_ip from vm_manager.vm_unit where status in ('SERVING', 'ERROR',"
                       "'TRASH', 'FREEZE')")
        for row in cursor.fetchall():
            rows.append({
                "id": row[0],
                "floating_ip": row[1]
            })
    sql_conn.close()
    vm_units = {}
    for row in rows:
        vm_units[row["id"]] = row["floating_ip"]
    return vm_units


class MonTool(object):
    """ monitor tool """

    def __init__(self):
        """ init """
        pass

    @staticmethod
    def block(bns, time, token):
        """block"""
        status, _ = commands.getstatusoutput("montool -b %s -d %s --token %s" % (bns, time, token))
        if status == 0:
            return True
        return False


class Monquery(object):
    """Monquery
    """

    def __init__(self):
        """init
        """
        pass

    @staticmethod
    def do(parameter):
        """do
        """
        no_data_metrics = []
        for metric in parameter['metrics']:
            cmd = " monquery -x -n {} -t instance -i {} -s {}".format(parameter['bns'], metric, parameter['timestamp'])
            status, output = commands.getstatusoutput(cmd)
            if status == 0 and output and "ERROR" not in output:
                continue
            no_data_metrics.append(metric)
        return parameter['bns'], no_data_metrics


class BccNoahChecker(object):
    """BccNoahChecker"""

    def __init__(self, bns_conf):
        self.conf = bns_conf
        self.monquery = Monquery()
        self.bns_service = BNSService(bns_conf['endpoint'], bns_conf['token'], "root", "")
        self.bns_service_to_add = {}
        self.bns_service_to_delete = {}

    def get_all_online_instance_from_bns(self):
        """get_all_online_instance_from_bns"""
        instances = {}
        for i in xrange(10):
            service_name = self.conf['service_name_prefix'] + str(i) + self.conf['service_name_suffix']
            success, instances_of_one_service = self.bns_service.list_instances(service_name)
            if not success:
                logger.warning("get instance fail for :" % service_name)
                return False, instances
            instances[service_name] = instances_of_one_service
        return True, instances

    def get_all_online_vm_unit_from_db(self):
        """get_all_online_vm_unit_from_db"""
        vm_units = get_all_vm_units()
        logger.info("get all vm_units %s", str(vm_units))
        return vm_units

    def add_instance_to_bns(self, vm_units, instances):
        """add_instance_to_bns"""
        operation_times = 0
        for vm_unit_id, vm_unit_floating_ip in vm_units.items():
            if operation_times >= self.conf['max_operation_once']:
                break
            service_name = self.conf['service_name_prefix'] + \
                           vm_unit_floating_ip[-1] + self.conf['service_name_suffix']
            for instance in instances[service_name]:
                if vm_unit_id == int(instance['instanceId']):
                    break
            else:
                operation_times += 1
                instance_to_add = dict(instancePath=self.conf['deploy_path'] + "/" + str(vm_unit_id),
                                       hostName=vm_unit_floating_ip,
                                       instanceId=vm_unit_id,
                                       port=self.conf["xagent_port"],
                                       healthCheckCmd=self.conf["healthcheck_cmd"],
                                       containerId="",
                                       )
                if self.bns_service.add_instance(service_name, instance_to_add):
                    self.bns_service_to_add[str(vm_unit_id) + "." + service_name] = True
                    logger.info("add instance to bns success: %s %s", service_name, instance_to_add)
                else:
                    self.bns_service_to_add[str(vm_unit_id) + "." + service_name] = False
                    logger.warning("add instance to bns fail: %s %s", service_name, instance_to_add)

    def delete_instance_from_bns(self, vm_units, instances):
        """delete_instance_from_bns"""
        operation_times = 0
        for service_name, instances_of_one_service in instances.items():
            for instance in instances_of_one_service:
                if operation_times >= self.conf['max_operation_once']:
                    break
                if int(instance["instanceId"]) in vm_units:
                    continue
                operation_times += 1
                if MonTool.block(str(instance['instanceId']) + "." + service_name,
                                 self.conf['block_time'], self.conf['token']) and \
                        self.bns_service.delete_instance(service_name, instance):
                    self.bns_service_to_delete[str(instance['instanceId']) + "." + service_name] = True
                    logger.info("delete instance success: %s %s ", service_name, instance)
                else:
                    self.bns_service_to_delete[str(instance['instanceId']) + "." + service_name] = False
                    logger.warning("delete instance fail: %s %s ", service_name, instance)

    def instance_operate(self):
        """ instance operate """
        logger.info("instance_operate start ")
        success, instances = self.get_all_online_instance_from_bns()
        if not success:
            return False
        vm_units = self.get_all_online_vm_unit_from_db()
        self.delete_instance_from_bns(vm_units, instances)
        self.add_instance_to_bns(vm_units, instances)
        result = []
        for service_unit_name, success in self.bns_service_to_add.items():
            result.append("bns add %s result:%s" % (service_unit_name, str(success)))
        for service_unit_name, success in self.bns_service_to_delete.items():
            result.append("bns delete %s result:%s" % (service_unit_name, str(success)))
        if result:
            call_bot("BNS AUTO OPERATION", result)

    def mon_data_check(self):
        """ mon data check """
        logger.info("mon_data_check start ")
        vm_units = self.get_all_online_vm_unit_from_db()
        parameters = []
        timestamp = (datetime.datetime.now() - datetime.timedelta(minutes=3)).strftime('%Y%m%d%H%M%S')
        for vm_unit_id, vm_unit_floating_ip in vm_units.items():
            service_unit_name = str(vm_unit_id) + "." + self.conf['service_name_prefix'] + \
                                vm_unit_floating_ip[-1] + self.conf['service_name_suffix']
            parameters.append(dict(bns=service_unit_name, metrics=self.conf['metrics'], timestamp=timestamp))
        pool = ThreadPool(8)
        results = pool.map(Monquery.do, parameters)
        pool.close()
        pool.join()
        text = []
        for result in results:
            if result[1]:
                text.append("{}:{}".format(result[0], ",".join(result[1])))
        if text:
            call_bot("BNS NODATA ALERT", text)


def call_bot(header, result):
    """call_bot"""
    a = time.time()
    logging.info("call bot %s" % str(a))
    url = 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=%s' % BOT_TOKEN
    headers = {
        'Accept': 'application/json'
    }
    alarm_data = {
        "message": {
            "body": [
                {
                    "type": "MD",
                    "content": "#### SCS_BCC_NOAH_ALERT \n%s\n"
                               "%s"
                               % (header, "\n".join(result))
                }
            ]
        }
    }

    try:
        res = requests.post(url, data=json.dumps(alarm_data), headers=headers)
        logger.info("Send msg to hi ret: %s %s", str(res.status_code), str(res.text))
        return 0
    except Exception as _:
        logger.error("[ERROR] Send msg to hirebot failed", exc_info=True)
    return -1


if __name__ == '__main__':

    if len(sys.argv) != 2:
        sys.exit(-1)
    else:
        region = sys.argv[1]
    monitor_conf = ConfigParser.RawConfigParser()
    conf = {}
    LogInfo.rm_log()
    logger = LogInfo('bcc_noah_check').get_log()
    try:
        monitor_conf.read(MONITOR_CONF)
        if monitor_conf.has_section(region):
            conf["region"] = monitor_conf.get(region, "region")
            conf["xagent_port"] = monitor_conf.get(region, "xagent_port")
            conf["deploy_path"] = monitor_conf.get(region, "deploy_path")
            conf["deploy_path"] = monitor_conf.get(region, "deploy_path")
            conf["healthcheck_cmd"] = monitor_conf.get(region, "healthcheck_cmd")
            conf["healthcheck_type"] = monitor_conf.get(region, "healthcheck_type")
            conf["token"] = monitor_conf.get(region, "token")
            conf["service_name_prefix"] = monitor_conf.get(region, "service_name_prefix")
            conf["service_name_suffix"] = monitor_conf.get(region, "service_name_suffix")
            conf["endpoint"] = monitor_conf.get(region, "endpoint")
            conf["block_time"] = monitor_conf.get(region, "block_time")
            conf["metrics"] = json.loads(monitor_conf.get(region, "metrics"))
            conf["max_operation_once"] = int(monitor_conf.get(region, "max_operation_once"))

        noah_check = BccNoahChecker(conf)
        noah_check.instance_operate()
        noah_check.mon_data_check()
    except Exception as e:
        logger.error("bcc noach check run fail", exc_info=True)
        call_bot("BCC NOAH CHECK RUN FAIL", "region:{}".format(conf.get("region", "")))


