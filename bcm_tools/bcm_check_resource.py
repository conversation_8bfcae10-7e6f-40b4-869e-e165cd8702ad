#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Description: bcm err check for scs
Authors: <AUTHORS>
Date:    2022/05/17
"""

import json
import pymysql
import datetime
import copy
import os
import logging
import logging.handlers
import urlparse
import sys
import argparse
import time

import bce_signer as bcesigner
import ConfigParser

g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")


CUR_TIMESTAMP = datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
SIGNING_PREFIX = 'bce-auth-v1'
SIGNING_HEADER_INCLUDE = ['host', 'content-md5']
SIGNING_HEADER_EXCLUDE = ['x-bce-request-id', 'x-bce-security-token']
MONITOR_CONF = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'conf/monitor.conf')
RETRY_TIME = 3

BOT_TOKEN = 'dddaad5d5548ecc1e1a3bd3f84ff88599'

CACHE = {}

logger = logging

class UserDbHandler(object):
    """处理数据库请求
    """

    def __init__(self, host, port, user, password, db):
        """init
        """
        self._db_conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            db=db, charset='utf8'
        )
        self.cursor = self._db_conn.cursor()

    def get_users(self):
        """获取用户下集群
        """
        users = []
        sql = "select user_id, cname, type from bce_user_info "
        try:
            self.cursor.execute(sql)
            for row in self.cursor.fetchall():
                users.append({
                    "user_id": row[0],
                    "cname": row[1],
                    "type": row[2]
                })
            self._db_conn.commit()
            return users
        except Exception as e:
            logging.error("Get user fail", exc_info=True)
            raise
        return []

    def close(self):
        """close
        """
        try:
            self._db_conn.close()
        except Exception as e:
            logging.error("close db error")


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def get_timestamp():
    """return utc time
    """
    return datetime.datetime.utcnow().strftime("%Y-%m-%dT%02H:%M:%SZ")


def get_mysql_config():
    """get mysql config
    """
    #mysql_conf = "/home/<USER>/wg/csmaster/conf/mysql.conf"
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def gen_http_headers(conf, method, uri):
    """Get http headers
    Args:
        user_id(string):
    Returns:
        headers(dict)
    """
    url = urlparse.urlparse(conf["url"])
    bce_host = ':'.join([url.hostname, str(url.port)])
    timestamp = get_timestamp()
    request = {
        'method': method,
        'uri': uri,
        'headers': {
            'x-bce-date': timestamp,
            'Host': bce_host
        }
    }
    logging.info("request:\n%s" % request)
    signer = bcesigner.BceSigner(conf["iam_access_key"], conf["iam_secret_key"], logging.getLogger())
    auth = signer.gen_authorization(request, timestamp)
    headers = {
        'Authorization': auth,
        'x-bce-date': timestamp,
        'Content-Type': "application/json"
    }
    return headers


def get_bcm_resource_by_dimension(conf, user_id, cluster_show_id):
    """get_bcm_resource
    """

    url = urlparse.urlparse(conf["url"])
    get_resource_uri = '/'.join(['/mrm-api', 'v1', 'services', 'BCE_SCS', 'resources',
                                'dimensions', user_id, conf['region'], "ClusterId:{}".format(cluster_show_id)])

    headers = gen_http_headers(conf, "GET", get_resource_uri)

    cmd = ' '.join(['curl -s -g --connect-timeout 3 -m 5 --retry 3 --retry-delay 1',
                    'http://%s:%s%s' % (url.hostname, str(url.port), get_resource_uri),
                    '-H "Host:%s"' % url.netloc,
                    '-H "x-bce-date:%s"' % headers['x-bce-date'],
                    '-H "Content-Type:%s"' % headers['Content-Type'],
                    '-H "Authorization:%s"' % headers['Authorization']])
    ret = os.popen(cmd).read().strip()

    logging.info('cmd:\n%s' % cmd)
    logging.info('monitor response:\n%s' % ret)

    logging.info('get bcm resource success')
    try:
        return json.loads(ret)
    except:
        return {}


def get_bcm_resource(conf, user_id, resource_id):
    """get_bcm_resource
    """

    url = urlparse.urlparse(conf["url"])
    get_resource_uri = '/'.join(['/mrm-api', 'v1', 'services', 'BCE_SCS', 'resources', user_id, conf['region'],
                                 resource_id])
    headers = gen_http_headers(conf, "GET", get_resource_uri)

    cmd = ' '.join(['curl -s -g --connect-timeout 3 -m 5 --retry 3 --retry-delay 1',
                    'http://%s:%s%s' % (url.hostname, str(url.port), get_resource_uri),
                    '-H "Host:%s"' % url.netloc,
                    '-H "x-bce-date:%s"' % headers['x-bce-date'],
                    '-H "Content-Type:%s"' % headers['Content-Type'],
                    '-H "Authorization:%s"' % headers['Authorization']])
    ret = os.popen(cmd).read().strip()

    logging.info('cmd:\n%s' % cmd)
    logging.info('monitor response:\n%s' % ret)

    if "ResourceNotExist" in ret:
        logging.info('get bcm resource fail:%s', resource_id)
        return {}
    logging.info('get bcm resource success')
    try:
        return json.loads(ret)
    except:
        return {}


def delete_bcm_resource(conf, user_id, resource_id):
    """delete_bcm_resource
    """
    url = urlparse.urlparse(conf["url"])
    delete_resource_uri = '/'.join(['/mrm-api', 'v1', 'services', 'BCE_SCS', 'resources', user_id, conf['region'],
                                 resource_id])
    headers = gen_http_headers(conf, "DELETE", delete_resource_uri)

    cmd = ' '.join(['curl -s -g -X DELETE --connect-timeout 3 -m 5 --retry 3 --retry-delay 1',
                    'http://%s:%s%s' % (url.hostname, str(url.port), delete_resource_uri),
                    '-H "Host:%s"' % url.netloc,
                    '-H "x-bce-date:%s"' % headers['x-bce-date'],
                    '-H "Content-Type:%s"' % headers['Content-Type'],
                    '-H "Authorization:%s"' % headers['Authorization']])
    ret = os.popen(cmd).read().strip()

    logging.info('cmd:\n%s' % cmd)
    logging.info('delete bcm resource response:\n%s' % ret)

    if "Exception" in ret:
        logging.info('delete bcm resource fail')
        return False
    logging.info('delete bcm resource success')
    return True


def create_bcm_resource(conf, data_json):
    """create_bcm_resource
    """
    url = urlparse.urlparse(conf["url"])
    create_resource_uri = '/'.join(['/mrm-api', 'v1', 'services', 'BCE_SCS', 'resources'])
    headers = gen_http_headers(conf, "POST", create_resource_uri)

    cmd = ' '.join(['curl -s -g -X POST --connect-timeout 3 -m 5 --retry 3 --retry-delay 1',
                    'http://%s:%s%s' % (url.hostname, str(url.port), create_resource_uri),
                    '-H "Host:%s"' % url.netloc,
                    '-H "x-bce-date:%s"' % headers['x-bce-date'],
                    '-H "Content-Type:%s"' % headers['Content-Type'],
                    '-H "Authorization:%s"' % headers['Authorization'],
                    '-d \'%s\'' % data_json])
    ret = os.popen(cmd).read().strip()

    logging.info('cmd:\n%s' % cmd)
    logging.info('create bcm resource response:\n%s' % ret)

    if "already exists" in ret:
        return False
    elif "Exception" in ret:
        return False
    if json.loads(data_json)['userId'] in ret:
        return True
    return False


def get_all_shards(user_id=''):
    """get all shards
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    if user_id:
        condition = "and iam_user_id='{}'".format(user_id)
    else:
        condition = ''
    with sql_conn.cursor() as cursor:
        cursor.execute("select hash_name, cluster_show_id, iam_user_id, version, hash_id, cluster_id,"
                       "cache_cluster.create_time from cache_cluster,cache_instance "
                       "where cache_cluster.status not in (10, 12, 0) and cache_cluster.id=cache_instance.cluster_id"
                       " and cache_instance.cache_instance_type !=0 {} order by cache_cluster.id".format(condition))
        for row in cursor.fetchall():
            rows.append({
                "hash_name": row[0],
                "cluster_show_id": row[1],
                "iam_user_id": row[2],
                "version": row[3],
                "hash_id": row[4],
                "cluster_id": row[5],
                "create_time": row[6]
            })
    sql_conn.close()

    ret = {}
    shard_ids = []
    for row in rows:
        # 因为历史元数据存在hash_name不一致的情况,csagent做了兼容,7001版本统一加0
        if row['version'] == 7001:
            suffix = "0"
        else:
            suffix = row["hash_name"].split('_')[-1]
        if row["cluster_show_id"] + "-" + suffix in shard_ids:
            continue
        else:
            shard_ids.append(row["cluster_show_id"] + "-" + suffix)

        if row['iam_user_id'] not in ret:
            ret[row['iam_user_id']] = [
                {"cluster_show_id": row["cluster_show_id"], "shard_id": row["cluster_show_id"] + "-" + suffix,
                 "hash_id": row["hash_id"], "cluster_id": row["cluster_id"], "create_time": row['create_time'],
                "iam_user_id": row['iam_user_id']}]
        else:
            ret[row['iam_user_id']].append(
                {"cluster_show_id": row["cluster_show_id"], "shard_id": row["cluster_show_id"] + "-" + suffix,
                 "hash_id": row["hash_id"], "cluster_id": row["cluster_id"], "create_time": row['create_time'],
                 "iam_user_id": row['iam_user_id']})
    return ret


def check_shard_resource(conf, user_id=''):
    """check shard resource"""
    not_exists_resource = {}
    data_incorrect_resource = {}
    shards = get_all_shards(user_id)
    for user, items in shards.items():
        for item in items:
            resource_id = item["cluster_show_id"] + "___" + item["shard_id"]
            resource = get_bcm_resource(conf, user, resource_id)
            if not resource:
                not_exists_resource[resource_id] = {"id": resource_id, "item": item}
                continue
            for property in resource["properties"]:
                #if property['name'] == "NodeName" and property['value'] != str(item['shard_id']):
                if property['name'] == "NodeName" and property['value'] != str(item['shard_id']):
                    data_incorrect_resource[resource_id] = {"resource": resource, "item": item}
                    break
    return not_exists_resource, data_incorrect_resource


def get_all_nodename(user_id=''):
    """get all nodename
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    if user_id:
        condition = "iam_user_id='{}'".format(user_id)
    else:
        condition = '1=1'
    with sql_conn.cursor() as cursor:
        cursor.execute("select hash_name, cluster_show_id, iam_user_id, version, hash_id, cluster_id,"
                       "cache_cluster.create_time, node_show_id,cache_instance_type from cache_cluster,cache_instance "
                       "where cache_cluster.status not in (10, 12, 0) and cache_cluster.id=cache_instance.cluster_id"
                       " and {} order by cache_cluster.id".format(condition))
        for row in cursor.fetchall():
            rows.append({
                "hash_name": row[0],
                "cluster_show_id": row[1],
                "iam_user_id": row[2],
                "version": row[3],
                "hash_id": row[4],
                "cluster_id": row[5],
                "create_time": row[6],
                "node_show_id": row[7],
                "cache_instance_type": row[8]
            })
    sql_conn.close()

    ret = {}
    for row in rows:
        if row['iam_user_id'] not in ret:
            ret[row['iam_user_id']] = {}
        if row['cluster_show_id'] not in ret[row['iam_user_id']]:
            ret[row['iam_user_id']][row['cluster_show_id']] = []
        # 因为历史元数据存在hash_name不一致的情况,csagent做了兼容,7001版本统一加0
        if row['version'] == 7001:
            shard_id = row["cluster_show_id"] + "-" + "0"
            if shard_id not in ret[row['iam_user_id']][row['cluster_show_id']]:
                ret[row['iam_user_id']][row['cluster_show_id']].append(shard_id)
        elif row['cache_instance_type'] != 0:
            suffix = row["hash_name"].split('_')[-1]
            shard_id = row["cluster_show_id"] + "-" + suffix
            if shard_id not in ret[row['iam_user_id']][row['cluster_show_id']]:
                ret[row['iam_user_id']][row['cluster_show_id']].append(shard_id)

        if row['node_show_id']:
            ret[row['iam_user_id']][row['cluster_show_id']].append(row['node_show_id'])

    return ret


def check_bcm_resource_need_delete(conf, user_id):
    """check_bcm_resource_need_delete"""
    with open("%s/conf/user_db_config.json" % g_script_path, "rt") as f:
        user_db_config = json.loads(f.read())['*']
    user_db_handler = UserDbHandler(user_db_config['host'],
                          user_db_config['port'],
                          user_db_config['user'],
                          user_db_config['password'],
                          user_db_config['db'])
    users = user_db_handler.get_users()
    user_db_handler.close()
    user_info = {}
    for user in users:
        user_info[user['user_id']] = user['type'] + " " + user['cname']
    data = get_all_nodename(user_id)
    for user, clusters in data.items():
        for cluster, nodes in clusters.items():
            has_cluster = False
            bcm_data_list = get_bcm_resource_by_dimension(conf, user, cluster)
            for bcm_data in bcm_data_list:
                if len(bcm_data) == 1 and bcm_data[0]['name'] == 'ClusterShowId':
                    has_cluster =True
                if len(bcm_data) == 2 and bcm_data[1]['name'] == 'NodeName':
                   if bcm_data[1]['value'] not in nodes:
                       logging.info("NeedDeleteNode: %s %s %s %s" %
                                    (user, cluster, bcm_data[1]['value'], user_info.get(user, '')))

            if not has_cluster:
                logging.info("NoCluster: %s %s %s " % (user, cluster, user_info.get(user, '')))


def main(conf, action, user_id):
    """main"""
    init_log(os.path.join(g_script_path, "log/check_resource"))
    if action == "check_bcm_resource":
         check_bcm_resource(conf, user_id)
    if action == "fix_bcm_resource":
         if not fix_bcm_resource(conf, user_id):
             print "Fail to fix bcm resource"
         else:
             print "Success to fix bcm resource"
    if action == "check_bcm_resource_need_delete":
        check_bcm_resource_need_delete(conf, user_id)


def check_bcm_resource(conf, user_id):
    """check bcm resource"""
    not_exists_resource, data_incorrect_resource = check_shard_resource(conf, user_id)
    for k, v in not_exists_resource.items():
        logging.info("not exit resource: %s=>%s", k, v)
    for k, v in data_incorrect_resource.items():
        logging.info("data incorrect resource: %s=>%s", k, v)
    print "Not exist: %d. Data incorrect: %d." % (len(not_exists_resource), len(data_incorrect_resource))
    return True


def fix_bcm_resource(conf, user_id):
    """fix bcm resource"""
    not_exists_resource, data_incorrect_resource = check_shard_resource(conf, user_id)
    for k, v in not_exists_resource.items():
        logging.info("not exit resource: %s=>%s", k, v)
    for k, v in data_incorrect_resource.items():
        logging.info("data incorrect resource: %s=>%s", k, v)
    print "Not exist: %d. Data incorrect: %d." % (len(not_exists_resource), len(data_incorrect_resource))
    if data_incorrect_resource:
        for k, v in data_incorrect_resource.items():
            print v['resource']
            logging.info("begin to delete %s", k)
            if not delete_bcm_resource(conf, v['resource']['userId'], k):
                logging.info("delete %s fail", k)
                return False
            else:
                logging.info("delete %s success", k)
            properties = copy.deepcopy(v['resource']['properties'])
            for property in properties:
                if property['name'] == "NodeName":
                    property['value'] = v['item']['shard_id']
                    break
            data = dict(userId=v['resource']['userId'],
                        region=v['resource']['region'],
                        serviceName=v['resource']['serviceName'],
                        typeName=v['resource']['typeName'],
                        resourceId=v['resource']['resourceId'],
                        identifiers=v['resource']['identifiers'],
                        properties=properties,
                        tags=v['resource']['tags'])
            logging.info("begion to create %s", k)
            if not create_bcm_resource(conf, json.dumps(data)):
                logging.info("create %s fail", k)
                return False
            else:
                logging.info("create %s success", k)
            print get_bcm_resource(conf, v['resource']['userId'], v['resource']['resourceId'])
            time.sleep(10)
    return True

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='Scs tools for bcm.')
    parser.add_argument('-a', '--action', choices=['check_bcm_resource', 'fix_bcm_resource',
                                                   'check_bcm_resource_need_delete'], required=True)
    parser.add_argument('-u', '--user_id', default='')
    args = parser.parse_args()
    monitor_conf = ConfigParser.RawConfigParser()
    conf = {}
    try:
         monitor_conf.read(MONITOR_CONF)
         if not monitor_conf.has_section('monitor_bcm') or not monitor_conf.has_section('deploy'):
            print("get conf fail")
            sys.exit(2)
         conf["url"] = monitor_conf.get('monitor_bcm', 'url')
         conf["iam_access_key"] = monitor_conf.get('monitor_bcm', 'iam_ak')
         conf["iam_secret_key"] = monitor_conf.get('monitor_bcm', 'iam_sk')
         conf["region"] = monitor_conf.get('deploy', 'region')
    except:
        print("get conf fail")
        sys.exit(2)

    main(conf, args.action, args.user_id)
