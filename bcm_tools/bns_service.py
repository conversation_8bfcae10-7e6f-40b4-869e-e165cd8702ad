#!/usr/bin/env python
# coding=utf-8
"""
bns lib
"""
import commands
import json
import sys
import urllib


class BNSService:
    """
    bns object with operation
    """

    def __init__(self, bns_endpoint, token, run_user, path):
        self.bns_endpoint = bns_endpoint
        self.token = token
        self.run_user = run_user
        self.path = path

    def request_bns(self, url):
        """ request BNS """
        result_dict = {}
        retry_times = 3
        try_time = 0
        while try_time < retry_times:
            try_time += 1
            try:
                sock = urllib.urlopen(url)
                result = sock.read()
                sock.close()
                result_dict = json.loads(result)
                return result_dict
            except Exception as e:
                print
                "request bns fail url:%s, retry:%s" % (url, try_time)
                continue
        return result_dict

    def exists(self, bns_name):
        """
        if bns exist, return true
        """
        url = ('%s?r=webfoot/GetServiceInfo&serviceName=%s' % (self.bns_endpoint, bns_name))
        result_dict = self.request_bns(url)
        if 'retCode' not in result_dict or result_dict['retCode'] != 0:
            return False
        return True

    def list_instances(self, bns_name):
        """
        list instance of a bns
        """
        url = ('%s?r=webfoot/GetInstanceInfo&serviceName=%s') % (self.bns_endpoint, bns_name)
        result_dict = self.request_bns(url)
        if 'retCode' not in result_dict or result_dict['retCode'] != 0:
            print
            'bns not exists or access %s failed' % url
            return False, []
        instance_list = []
        for instance in result_dict['instanceInfo']:
            port_info = json.loads(instance['port'])
            instance_list.append({
                'instanceId': instance['offset'],
                'instancePort': port_info['main'],
                'hostName': instance['hostName'],
                'instancePath': instance['deployPath'],
                'offset': instance['offset'],
                'status': instance['status'],
            })
        return True, instance_list

    def add_gaiaapp_to_service_tree(self, app_id):
        """add_gaiaapp_to_service_tree"""
        noah_add_info = []
        service_name = app_id
        url = 'http://noah.baidu.com/service-tree/index.php?r=Node/AddNode/addNodeApi' \
              '&parentPath=%s&nodeName=%s&type=%s&token=%s' \
              % (self.path, service_name, "service", self.token)
        result_dict = self.request_bns(url)
        if 'success' in result_dict and result_dict['success'] == 'true':
            return result_dict['data']['nodeId']
        else:
            print(result_dict)
            return None

    def delete_gaiaapp_from_service_tree(self, app_id):
        """delete_gaiaapp_from_service_tree"""
        noah_add_info = []
        service_name = app_id
        url = 'http://noah.baidu.com/service-tree/index.php?r=Node/DelNode/DeleteNodeApi' \
              '&node=%s&token=%s' \
              % (self.path + "_" + app_id, self.token)
        result_dict = self.request_bns(url)
        if 'success' in result_dict and result_dict['success'] == 'true':
            return True
        else:
            print(result_dict)
            return None

    def create_bns(self, bns_name, gaia_app_id=None):
        """
        create bns by name
        """
        bns_path = self.path
        if gaia_app_id is not None:
            bns_path = bns_path + '_' + gaia_app_id
        url = ('%s?r=bns/Create&parentPath=%s&nodeName=%s&runUser=%s&authKey=%s') % \
              (self.bns_endpoint, bns_path, bns_name, self.run_user, self.token)
        result_dict = self.request_bns(url)
        if 'retCode' not in result_dict or result_dict['retCode'] != 0:
            print
            'bns not exists or access %s failed' % url
            print
            result_dict
            return False
        return True

    def delete_bns(self, bns_name):
        """
        delete bns by name
        """
        url = ('%s?r=bns/Delete&serviceName=%s&authKey=%s') % (self.bns_endpoint, bns_name, self.token)
        result_dict = self.request_bns(url)
        if 'retCode' not in result_dict or result_dict['retCode'] != 0:
            print
            'bns not exists or access %s failed' % url
            return False
        return True

    def get_app_path_info(self, app_id):
        """get_path_info"""
        noah_add_info = []
        url = 'http://noah.baidu.com/service-tree/v1/node/path_%s' % (self.path + "_" + app_id)
        result_dict = self.request_bns(url)
        if 'id' in result_dict:
            return result_dict
        else:
            print(result_dict)
            return None

    def add_instance(self, bns_name, instance):
        """
        add instance to one bns
        """
        try:
            url = ('%s?r=bns/AddInstance&runUser=%s&status=0&disable=0&healthCheckType=proc&tag='
                   '&deployPath=%s&serviceName=%s&authKey=%s&hostName=%s'
                   '&healthCheckCmd=%s&port={"main":%s}&containerId=%s&instanceId=%s') % \
                  (self.bns_endpoint, self.run_user, instance['instancePath'], bns_name, self.token,
                   instance['hostName'], instance['healthCheckCmd'], instance['port'],
                   instance['containerId'], instance['instanceId'])
            result_dict = self.request_bns(url)
            if 'retCode' not in result_dict or result_dict['retCode'] != 0:
                print
                'bns not exists or access %s failed' % url
                print
                result_dict
                return False
        except Exception as e:
            return False
        return True

    def delete_instance(self, bns_name, instance):
        """
        delete instance of the bns by instance hostname
        """
        url = ('%s?r=bns/DeleteInstance&serviceName=%s&authKey=%s&hostName=%s&instanceId=%s') % \
              (self.bns_endpoint, bns_name, self.token, instance['hostName'], instance['instanceId'])
        result_dict = self.request_bns(url)
        if 'retCode' not in result_dict or result_dict['retCode'] != 0:
            print
            'bns not exists or access %s failed' % url
            print
            result_dict
            return False
        return True

    def delete_instance_in_machine(self, bns_name, hostname):
        """
        delete instance of the bns by instance hostname
        """
        url = ('%s?r=bns/DeleteInstance&serviceName=%s&authKey=%s&hostName=%s') % \
              (self.bns_endpoint, bns_name, self.token, hostname)
        result_dict = self.request_bns(url)
        if 'retCode' not in result_dict or result_dict['retCode'] != 0:
            print
            'bns not exists or access %s failed' % url
            print
            result_dict
            return False
        return True

    def get_group_info(self, group_name):
        """get bns group info"""
        ret = {'success': 0, 'msg': '', 'data': {}}
        url = ('%s?r=Group/GroupInfo_v2'
               '&groupName=%s') % \
              (self.bns_endpoint, group_name)
        try:
            sock = urllib.urlopen(url)
            result = sock.read()
            sock.close()
        except Exception as e:
            print("Cannot connect url %s", url)
            return ret
        try:
            result_dict = json.loads(result)
            if 'retCode' in result_dict and result_dict['retCode'] == 0:
                ret['success'] = 1
                if 'data' in result_dict and result_dict['data']:
                    ret['data'] = result_dict['data']
            else:
                ret['msg'] = result_dict['msg']
                print('get group %s info failed: %s', group_name, result_dict)
            return ret
        except Exception as e:
            print('[ERROR] Get group info %s: url: %s, error: %s', group_name, url, e)
            return ret

    def create_bns_group(self, node_path, group_name, bns_name=None):
        """create bns group"""
        ret = {'success': 0, 'msg': '', 'data': {}}
        url = ('%s?r=group/SaveGroup&authKey=%s&group_name=%s&node_path=%s') % \
              (self.bns_endpoint, self.token, group_name, node_path)

        if bns_name is not None:
            url = url + ("&service_names=%s" % bns_name)
        else:
            url = url + ("&service_names=")

        try:
            sock = urllib.urlopen(url)
            result = sock.read()
            sock.close()
        except Exception as e:
            print("Cannot connect url %s", url)
            return ret
        try:
            result_dict = json.loads(result)
            if 'retCode' in result_dict and result_dict['retCode'] == 0:
                ret['success'] = 1
                if 'data' in result_dict and result_dict['data']:
                    ret['data'] = result_dict['data']
            else:
                print('create group %s failed: %s', group_name, result_dict)
            return ret
        except Exception as e:
            print('[ERROR] create group %s: url: %s, error: %s', group_name, url, e)
            return ret

    def add_bns_to_group(self, group_name, bns_name):
        """add bns to group"""
        ret = {'success': 0, 'msg': '', 'data': {}}
        url = ('%s?r=group/ModifyServicesOfGroup&groupName=%s&services=%s&authKey=%s&action=add') % \
              (self.bns_endpoint, group_name, bns_name, self.token)

        try:
            sock = urllib.urlopen(url)
            result = sock.read()
            sock.close()
        except Exception as e:
            print("Cannot connect url %s", url)
            return ret
        try:
            result_dict = json.loads(result)
            if 'success' in result_dict and result_dict['success'] == True:
                ret['success'] = 1
            else:
                print('add bns %s to group %s failed: %s' % (bns_name, group_name, result_dict))
            return ret
        except Exception as e:
            print('[ERROR] add bns %s to group %s: url: %s, error: %s' % (bns_name, group_name, url, e))
            return ret

    def remove_bns_from_group(self, group_name, bns_name):
        """remove bns from group"""
        ret = {'success': 0, 'msg': '', 'data': {}}
        url = ('%s?r=group/ModifyServicesOfGroup&groupName=%s&services=%s&authKey=%s&action=remove') % \
              (self.bns_endpoint, group_name, bns_name, self.token)

        try:
            sock = urllib.urlopen(url)
            result = sock.read()
            sock.close()
        except Exception as e:
            print("Cannot connect url %s", url)
            return ret
        try:
            result_dict = json.loads(result)
            if 'success' in result_dict and result_dict['success'] == True:
                ret['success'] = 1
            else:
                print('remove bns %s from group %s failed: %s' % (bns_name, group_name, result_dict))
            return ret
        except Exception as e:
            print('[ERROR] remove bns %s from group %s: url: %s, error: %s' % (bns_name, group_name, url, e))
            return ret