#!/usr/bin/env python
#coding=utf-8
"""
This module provides STS related methods, including get_user_token, get_session_token, get_user_authinfo and get_service_endpoint
"""
import sys
import json
import urlparse
import httplib
import datetime
import logging
import traceback

import bce_signer


class StsService(object):
    """
    STS Service
    """
    HTTP_TIMEOUT_S = 300

    def __init__(self, endpoint, service_ak, service_sk, role_name='RdsDefaultRole', logger=None):
        """
        :type  endpoint String
        :param endpoint iam service endpoint
        :type  service_ak String
        :param service_user rds service ak
        :type  service_sk String
        :param service_sk rds service sk
        """
        self.endpoint = endpoint
        self.service_access_key = service_ak
        self.service_secret_key = service_sk
        self.role_name = role_name
        self.auth_info = None
        """cache data"""
        self.cache_endpoint = {}
        self.debug = 0
        if logger:
            self.logger = logger
        else:
            self.logger = logging.getLogger()

    def get_user_token(self, userid, auth_info=None):
        """
        get openstack token
        """
        if auth_info is None:
            if self.auth_info is not None:
                auth_info = self.auth_info
            else:
                auth_info = self.get_service_authorization(userid)
                if auth_info is None:
                    return None
                self.auth_info = auth_info
        if 'token' in auth_info and 'id' in auth_info['token']:
            return auth_info['token']['id']
        else:
            return None

    def get_session_token(self, userid, auth_info=None):
        """
        get user session token
        """
        if auth_info is None:
            if self.auth_info is not None:
                auth_info = self.auth_info
            else:
                auth_info = self.get_service_authorization(userid)
                if auth_info is None:
                    return None
                self.auth_info = auth_info
        if 'sessionToken' in auth_info:
            return auth_info['sessionToken']
        else:
            return None

    def get_user_authinfo(self, userid, auth_info=None):
        """
        get user's temporary ak/sk
        """
        usesr_authinfo = {'userId': userid, 'accessKeyId': '', 'secretAccessKey': ''}
        if auth_info is None:
            if self.auth_info is not None:
                auth_info = self.auth_info
            else:
                auth_info = self.get_service_authorization(userid)
                if auth_info is None:
                    return None
                self.auth_info = auth_info
        if 'accessKeyId' in auth_info:
            usesr_authinfo['accessKeyId'] = auth_info['accessKeyId']
        if 'secretAccessKey' in auth_info:
            usesr_authinfo['secretAccessKey'] = auth_info['secretAccessKey']
        return usesr_authinfo

    def get_service_authorization(self, userid):
        """
        header value with special char
        """
        try:
            urlobj = urlparse.urlparse(self.endpoint)
            iam_host = urlobj.netloc
            iam_url = urlobj.path
            utc_time = self.get_canonical_time(0)
            request = {
                'method': 'POST',
                'uri': iam_url,
                'params': {
                    'assumeRole': '',
                    'accountId': userid,
                    'roleName': self.role_name,
                    'withToken': ''
                },
                'headers': {
                    'x-bce-date': utc_time,
                    'host': iam_host
                }
            }
            signer = bce_signer.BceSigner(self.service_access_key, self.service_secret_key, self.logger)
            auth = signer.gen_authorization(request, timestamp=utc_time, expire_period=3600)
            headers = {'x-bce-date': utc_time, 'host': iam_host, 'authorization': auth}
            conn = self.__get_http_connection(iam_host)
            url = '%s?assumeRole&accountId=%s&roleName=%s&withToken' % (iam_url, userid, self.role_name)
            conn.request('POST', url, None, headers)
            res = conn.getresponse()
            if (res.status >= 300):
                self.logger.error('call %s failed: %s:%s', self.endpoint, res.status, res.read())
                # sys.stderr.write('call %s fail: %s' % (self.endpoint, res.read()))
                return None
            auth_dict = json.loads(res.read())
            conn.close()
            self.auth_info = auth_dict
            return auth_dict
        except Exception as e:
            self.logger.error('iam_auth call failed, exception[%s]', str(e))
            self.logger.error(traceback.print_exc())
            # sys.stderr.write("iam_auth call fail: %s\n" % (e))
            return None

    def get_service_endpoint(self, userid, service, interface, auth_info=None):
        """
        get endpoint
        :type service: string
        :param service: bce service name
        :type interface: string
        :param interface: string
        =======================
        :return:
            string
        """
        cache_key = userid + '-' + service + '-' + interface
        if auth_info is None:
            if self.auth_info is not None:
                auth_info = self.auth_info
            else:
                auth_info = self.get_service_authorization(userid)
                if auth_info is None:
                    return None
                self.auth_info = auth_info
        if 'token' not in auth_info or 'catalog' not in auth_info['token']:
            return None
        try:
            for item in auth_info['token']['catalog']:
                if (item['type'] != service):
                    continue
                for endpoint in item['endpoints']:
                    if (endpoint['interface'] == interface):
                        self.cache_endpoint[cache_key] = endpoint['url']
                        return endpoint['url']
        except ValueError:
            pass
        except Exception as e:
            self.logger.error('get service[%s] token failed, exception[%s]', service, str(e))
            self.logger.error(traceback.print_exc())
            # sys.stderr.write("get service[%s] token fail: %s\n" % (service, e))
        return None

    def http_request(self, method, url, data, headers):
        """
        send http request
        """
        res = None
        urlobj = urlparse.urlparse(url)
        body = None
        if (method != 'GET' and method != 'DELETE'):
            body = json.dumps(data)
        uri = urlobj.path
        if (urlobj.query):
            uri += '?' + urlobj.query
        conn = self.__get_http_connection(urlobj.netloc)
        conn.request(method, uri, body, headers)
        res = conn.getresponse()
        # conn.close()
        return res

    def __get_http_connection(self, host=None):
        """
        get http connection
        """
        port = 80
        host_port_list = host.split(":")
        if len(host_port_list) == 1:
            host = host_port_list[0].strip()
        elif len(host_port_list) == 2:
            host = host_port_list[0].strip()
            port = int(host_port_list[1].strip())
        if sys.version_info >= (2, 6):
            conn = httplib.HTTPConnection(host=host, port=port, strict=True, timeout=self.HTTP_TIMEOUT_S)
        else:
            conn = httplib.HTTPConnection(host=host, port=port)
        conn.set_debuglevel(self.debug)
        return conn

    def get_canonical_time(self, timestamp=0):
        """
        get canonical time
        """
        if timestamp == 0:
            utctime = datetime.datetime.utcnow()
        else:
            utctime = datetime.datetime.utcfromtimestamp(timestamp)
        return "%04d-%02d-%02dT%02d:%02d:%02dZ" % (utctime.year, utctime.month, utctime.day, utctime.hour,
                                                   utctime.minute, utctime.second)