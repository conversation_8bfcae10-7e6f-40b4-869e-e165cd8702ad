#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Description: bcm err check for scs
Authors: <AUTHORS>
Date:    2022/05/17
"""
import argparse
import time
import datetime
import os
import logging
import json
import urllib
import hmac
import hashlib
import urlparse
import requests
import pymysql
import sys
import itertools

from multiprocessing import Pool
from multiprocessing.dummy import Pool as ThreadPool
import threading
lock = threading.Lock()

import ConfigParser
import sts_service


CUR_TIMESTAMP = datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
SIGNING_PREFIX = 'bce-auth-v1'
SIGNING_HEADER_INCLUDE = ['host', 'content-md5']
SIGNING_HEADER_EXCLUDE = ['x-bce-request-id', 'x-bce-security-token']
MONITOR_CONF = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'conf/monitor.conf')
RETRY_TIME = 3

BOT_TOKEN = 'dddaad5d5548ecc1e1a3bd3f84ff88599'

CACHE = {}

logger = logging

def get_last_create_time(minutes):
    """get_last_create_time"""
    return (datetime.datetime.now() - datetime.timedelta(hours=8) -
            datetime.timedelta(minutes=minutes)).strftime('%Y-%m-%d %H:%M:%S')

def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_all_shards(versions, store_types):
    """get all shards
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    version_format_strings = ','.join(['%s'] * len(versions))
    store_type_format_strings = ','.join(['%s'] * len(store_types))

    with sql_conn.cursor() as cursor:
        cursor.execute("select distinct hash_name, cluster_show_id, iam_user_id, version from "
                       "cache_cluster,cache_instance "
                       "where cache_cluster.status not in (0,10,12,58) and "
                       "cache_cluster.id=cache_instance.cluster_id"
                       " and cache_instance.cache_instance_type in (3,2,4) and "
                       "version in (%s) and store_type in (%s) and cache_cluster.create_time < '%s'" % \
                       (version_format_strings, store_type_format_strings, get_last_create_time(15)),
                       tuple(versions + store_types))
        for row in cursor.fetchall():
            rows.append({
                "hash_name": row[0],
                "cluster_show_id": row[1],
                "iam_user_id": row[2],
                "version": row[3]
            })
    sql_conn.close()

    ret = {}
    for row in rows:
        # 因为历史元数据存在hash_name不一致的情况,csagent做了兼容,7001版本统一加0
        if row['version'] == 7001:
            suffix = "0"
        else:
            suffix = row["hash_name"].split('_')[-1]
        if row['iam_user_id'] not in ret:
            ret[row['iam_user_id']] = [
                {"cluster_show_id": row["cluster_show_id"], "shard_id": row["cluster_show_id"] + "-" + suffix}]
        else:
            ret[row['iam_user_id']].append(
                {"cluster_show_id": row["cluster_show_id"], "shard_id": row["cluster_show_id"] + "-" + suffix})
    return ret


def get_all_clusters(versions, store_types):
    """get all clusters
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    version_format_strings = ','.join(['%s'] * len(versions))
    store_type_format_strings = ','.join(['%s'] * len(store_types))
    with sql_conn.cursor() as cursor:
        cursor.execute("select cluster_show_id, iam_user_id, version from "
                       "cache_cluster, userinfo  "
                       "where cache_cluster.status not in (0,10,12,58) and "
                       "cache_cluster.user_id=userinfo.id and "
                       "version in (%s) and store_type in (%s) and cache_cluster.create_time < '%s'" % \
                       (version_format_strings, store_type_format_strings, get_last_create_time(15)),
                       tuple(versions + store_types))
        for row in cursor.fetchall():
            rows.append({
                "cluster_show_id": row[0],
                "iam_user_id": row[1],
                "version": row[2]
            })
    sql_conn.close()

    ret = {}
    for row in rows:
        if row['iam_user_id'] not in ret:
            ret[row['iam_user_id']] = [{"cluster_show_id": row["cluster_show_id"]}]
        else:
            ret[row['iam_user_id']].append({"cluster_show_id": row["cluster_show_id"]})
    return ret


def get_all_instance(versions, store_types, roles):
    """get all shards
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    version_format_strings = ','.join(['%s'] * len(versions))
    store_type_format_strings = ','.join(['%s'] * len(store_types))
    roles_format_strings = ','.join(['%s'] * len(roles))

    with sql_conn.cursor() as cursor:
        cursor.execute("select node_show_id, cluster_show_id, iam_user_id, version from "
                       "cache_cluster,cache_instance "
                       "where cache_cluster.status not in (0,10,12,58) and "
                       "cache_cluster.id=cache_instance.cluster_id"
                       " and cache_instance.cache_instance_type in (%s) and version <> 1001 and "
                       "version in (%s) and store_type in (%s) and cache_cluster.create_time < '%s'" % \
                       (roles_format_strings, version_format_strings,
                        store_type_format_strings, get_last_create_time(15)),
                       tuple(roles + versions + store_types))
        for row in cursor.fetchall():
            rows.append({
                "node_show_id": row[0],
                "cluster_show_id": row[1],
                "iam_user_id": row[2],
                "version": row[3]
            })
    sql_conn.close()

    ret = {}
    for row in rows:
        if row['iam_user_id'] not in ret:
            ret[row['iam_user_id']] = [{"cluster_show_id": row["cluster_show_id"], "node_show_id": row["node_show_id"]}]
        else:
            ret[row['iam_user_id']].append(
                {"cluster_show_id": row["cluster_show_id"], "node_show_id": row["node_show_id"]})
    return ret


class BcmChecker:
    """BcmChecker"""

    def __init__(self, deploy_conf, bcm_conf, sts_conf):
        self.iam_service = sts_service.StsService(sts_conf['scs_auth_iam_url'],
                                                  sts_conf['scs_auth_ak'],
                                                  sts_conf['scs_auth_sk'],
                                                  sts_conf['scs_auth_role_name'], logger)
        self.bcm_conf = bcm_conf
        self.deploy_conf = deploy_conf
        self.region = self.deploy_conf['region']

    def acquire_iam_token(self, user_id):
        """use lump acquire iam token"""
        result = {
            'access_key': None,
            'secret_key': None,
            'sign_token': None,
            'user_token': None,
            'urls': {}
        }

        auth_info = self.iam_service.get_service_authorization(user_id)
        if auth_info is None:
            return None

        if 'accessKeyId' in auth_info:
            result['access_key'] = auth_info['accessKeyId']
        if 'secretAccessKey' in auth_info:
            result['secret_key'] = auth_info['secretAccessKey']
        if 'sessionToken' in auth_info:
            result['sign_token'] = auth_info['sessionToken']
        if 'token' in auth_info and 'id' in auth_info['token']:
            result['user_token'] = auth_info['token']['id']

        if 'token' not in auth_info or 'catalog' not in auth_info['token']:
            return None

        if 'access_key' in result and 'secret_key' in result and 'sign_token' in result and 'user_token' in result:
            logging.info('Acquire iam token for user %s success.' % user_id)
            return result
        else:
            logging.error('Acquire iam token for user %s fail.' % user_id)
            return None

    def bcm_err_check_wrapper(self, args):
        """wrapper"""
        return self.bcm_err_check(*args)

    def bcm_err_check(self, user_id, metrics, cluster_show_id, node_name=None):
        """bcm_err_check"""
        # 聚合任务有5分钟延迟
        if node_name:
            end_time = datetime.datetime.utcfromtimestamp(int(time.time()) - 300).strftime("%Y-%m-%dT%H:%M:%SZ")
            start_time = datetime.datetime.utcfromtimestamp(int(time.time()) - 480).strftime("%Y-%m-%dT%H:%M:%SZ")
        else:
            end_time = datetime.datetime.utcfromtimestamp(int(time.time()) - 360).strftime("%Y-%m-%dT%H:%M:%SZ")
            start_time = datetime.datetime.utcfromtimestamp(int(time.time()) - 480).strftime("%Y-%m-%dT%H:%M:%SZ")
        global CACHE
        err = ''

        lock.acquire()

        if user_id not in CACHE:
            iam_token = self.acquire_iam_token(user_id)
            CACHE[user_id] = iam_token
        else:
            iam_token = CACHE[user_id]

        lock.release()

        if not iam_token:
            return []
        lost_monitor_data = []
        for metric in metrics:
            if node_name:
                dimensions = "ClusterId:{};NodeId:{}".format(cluster_show_id, node_name)
            else:
                dimensions = "ClusterId:{}".format(cluster_show_id)
            url = '{endpoint}/json-api/v1/metricdata/{userId}/{scope}/{metricName}' \
                  '?region={region}&statistics[]=average&periodInSecond=60&'\
                  'startTime={startTime}&endTime={endTime}&dimensions={dimensions}'.format(
                    endpoint=self.bcm_conf['url'], userId=user_id, scope=self.bcm_conf['scope'], metricName=metric,
                    region=self.region, startTime=start_time, endTime=end_time,
                    dimensions=dimensions)

            headers = {'accept': 'application/json'}

            get_authorization('GET', url, headers, iam_token['access_key'], iam_token['secret_key'],
                              iam_token['sign_token'])
            ret = ''
            try_time = 0
            while try_time < RETRY_TIME:
                try_time += 1
                try:
                    res = requests.get(url, headers=headers)
                    ret = res.text
                    if res.status_code / 100 != 2:
                        logger.error('[url:%s]Request send monitor data failed: code[%s], return_data[%s]' % \
                              (url, res.status_code, ret))
                        logger.info('send monitor bcm retry time:%s' % try_time)
                        continue
                    logger.info('send bcm monitor data request success: header[%s], url:%s, ret: %s' % \
                          (headers, url, ret))
                    #减少下误报概率
                    data_points = json.loads(ret).get("dataPoints", [])
                    if data_points:
                        for data_point in data_points:
                            if 'average' in data_point:
                                break
                except Exception as e:
                    logger.error('[url:%s][receive:%s]Send monitor info fail: %s' % (url, ret, e))
                    logger.info('send monitor bcm retry time:%s' % try_time)

            if node_name:
                name = node_name
            else:
                name = cluster_show_id
            result = json.loads(ret)
            if 'dataPoints' in result:
                points = 0
                for dataPoint in result['dataPoints']:
                    if 'average' in dataPoint:
                        points += 1
                        break
                if points < 1:
                    err += name + '_no_[' + metric + ']'
                    lost_monitor_data.append(name + '_no_[' + metric + ']')
            else:
                err += name + '_' + 'no_dataPoints' + ','
                lost_monitor_data.append(name + '_no_[' + metric + ']')
        logger.info("lost_monitor_data:%s" % lost_monitor_data)
        return lost_monitor_data


def get_authorization(method, url, header, ak, sk, sign_token, duration="1800"):
    """Get an authorization signature for current request."""
    url = urlparse.urlparse(url)
    utctime = datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
    logger.info('Sign with access key: %s.', ak)
    logger.info('Sign with secret key: %s.', sk)
    header['x-bce-date'] = utctime
    header.setdefault('host', url.netloc)
    if sign_token:
        header['x-bce-security-token'] = encoded(sign_token)
    # signing key
    values = '/'.join([SIGNING_PREFIX, ak, utctime, duration])
    signing_key = hmac.new(encoded(sk), encoded(values), hashlib.sha256)
    signing_key = signing_key.hexdigest()
    logger.debug('Signature will be generated by key: %s.', signing_key)
    # signing body
    header_keys, header_values = format_headers(header)
    signing_body = '\n'.join([
        method,
        url.path,
        '&'.join(format_querystring(url.query)),
        '\n'.join(header_values),
    ])
    logger.debug('Signature will be generated by body: %s.', signing_body)
    signature = hmac.new(encoded(signing_key), encoded(signing_body), hashlib.sha256).hexdigest()
    logger.debug('Signature generated is: %s.', signature)
    header['authorization'] = '/'.join([
        SIGNING_PREFIX,
        ak,
        utctime,
        duration,
        ';'.join(header_keys),
        signature,
    ])


def format_querystring(querystring):
    """Sort and encode the keys and values in query string."""
    result = []
    for part in querystring.split('&'):
        if part:
            pos = part.find('=')
            if pos != -1:
                key = urllib.unquote(part[:pos])
                value = urllib.unquote(part[pos + 1:])
            else:
                key = urllib.unquote(part)
                value = ''
            result.append(urllib.quote(key) + '=' + urllib.quote(value))
    return sorted(result)


def encoded(value):
    """Return the `value` in utf-8 encoding."""
    if isinstance(value, unicode):
        return value.encode('utf-8')
    elif isinstance(value, str):
        return value
    return str(value)


def format_headers(header):
    """Sort and encode the keys and values in request header."""
    header_keys = []
    header_values = []
    for key, value in header.items():
        key = key.lower()
        if key in SIGNING_HEADER_EXCLUDE:
            pass
        elif value and (key in SIGNING_HEADER_INCLUDE or key.startswith('x-bce-')):
            header_keys.append(key)
            header_values.append('%s:%s' % (key, urllib.quote(value)))
    return sorted(header_keys), sorted(header_values)


def call_bot(region, all_size, result):
    """call_bot"""
    a = time.time()
    logging.info("call bot %s" % str(a))
    url = 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=%s' % BOT_TOKEN
    headers = {
        'Accept': 'application/json'
    }
    alarm_data = {
        "message": {
            "body": [
                {
                    "type": "MD",
                    "content": "#### SCS_BCM_ALART \nLOST MONITOR DATA <font color=\"red\">%s Instances"
                               "</font>\n**%s region\n> %s"
                               % (all_size, region, "".join(result))
                }
            ]
        }
    }

    try:
        res = requests.post(url, data=json.dumps(alarm_data), headers=headers)
        logger.info("Send msg to hi ret: %s %s", str(res.status_code), str(res.text))
        return 0
    except Exception as e:
        logger.error("[ERROR] Send msg to hirebot failed", exc_info=True)
    return -1

def main(deploy_conf, bcm_conf, sts_conf, versions, store_types, type, roles, metrics):
    """main"""
    logger.info("start to check scs")
    bcm_check = BcmChecker(deploy_conf, bcm_conf, sts_conf)
    lost_monitor_data = {}
    params = []
    if type == "shard":
        all_shards = get_all_shards(versions, store_types)
        for user_id, shards in all_shards.items():
            for shard in shards:
                params.append((user_id,
                               metrics,
                               shard["cluster_show_id"],
                               shard["shard_id"],))
    elif type == "cluster":
        all_clusters = get_all_clusters(versions, store_types)
        for user_id, clusters in all_clusters.items():
            for cluster in clusters:
                params.append((user_id,
                               metrics,
                               cluster["cluster_show_id"]))
    else:
        all_instances = get_all_instance(versions, store_types, roles)
        for user_id, instances in all_instances.items():
            for instance in instances:
                params.append((user_id,
                               metrics,
                               instance["cluster_show_id"],
                               instance["node_show_id"]))
    pool = ThreadPool(4)
    results = pool.map(bcm_check.bcm_err_check_wrapper, params)
    pool.close()
    pool.join()
    logger.info("lost_monitor_data:%s" % lost_monitor_data)
    total = []
    for result in results:
        total.extend(["PrecheckFail:" + i for i in result if i])

    if len(total) > 0:
        message = []
        for v in total:
            message.append('%s\n' % v)

        #for i in range(0, len(message), 10):
        for i in range(0, len(total), 10):
            call_bot(deploy_conf['region'], len(total), message[i:i + 10])

if __name__ == '__main__':

    monitor_conf = ConfigParser.RawConfigParser()
    bcm_conf = {}
    sts_conf = {}
    deploy_conf = {}
    try:
        monitor_conf.read(MONITOR_CONF)
        if monitor_conf.has_section('monitor_bcm'):
            bcm_conf['scope'] = monitor_conf.get('monitor_bcm', 'scope')
            bcm_conf['url'] = monitor_conf.get('monitor_bcm', 'url')

        if monitor_conf.has_section('sts'):
            sts_conf['scs_auth_role_name'] = monitor_conf.get('sts', 'scs_auth_role_name')
            sts_conf['scs_auth_iam_url'] = monitor_conf.get('sts', 'scs_auth_iam_url')
            sts_conf['scs_auth_ak'] = monitor_conf.get('sts', 'scs_auth_ak')
            sts_conf['scs_auth_sk'] = monitor_conf.get('sts', 'scs_auth_sk')

        if monitor_conf.has_section('deploy'):
            deploy_conf['region'] = monitor_conf.get('deploy', 'region')

    except:
        print("get conf fail")
        sys.exit(1)

    parser = argparse.ArgumentParser(description='Scs precheck tools.')
    parser.add_argument('-v', '--versions', required=True)
    parser.add_argument('-s', '--store_types', required=True)
    parser.add_argument('-t', '--type', choices=['cluster', 'shard', 'node'], required=True)
    parser.add_argument('-r', '--roles')
    parser.add_argument('-m', '--metrics', required=True)

    args = parser.parse_args()

    versions = args.versions.split(',')
    store_types = args.store_types.split(',')
    roles = args.roles.split(',') if args.roles else []
    metrics = args.metrics.split(',')

    if args.type == "node" and not roles:
        print ("type node master input roles")
        sys.exit(1)

    main(deploy_conf, bcm_conf, sts_conf, versions, store_types, args.type, roles, metrics)
