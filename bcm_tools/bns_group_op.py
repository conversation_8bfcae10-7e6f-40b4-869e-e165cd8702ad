#!/bin/python
# coding=utf-8
"""
monitor repair script
"""
import argparse
import os
import sys
import json
import socket
import time
import bns_service
import ConfigParser
import pymysql
import logging
import logging.handlers

g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")

MONITOR_CONF = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'conf/noah_monitor.conf')

def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret

def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)

def get_all_clusters():
    """get all clusters
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    rows = []
    with sql_conn.cursor() as cursor:
        cursor.execute("select id, version from cache_cluster where status not in (10, 12, 0)")
        for row in cursor.fetchall():
            rows.append({
                "id": row[0],
                "version": row[1]
            })
    sql_conn.close()

    return rows

class BnsGroupOperation(object):
    """ bns group operation """
    def __init__(self, conf):

        self.conf = conf

    def execute(self, action, mode):
        """
        execute - execute bns operations
        Args:
            mode   - run mode
        Return:
            status   - status code
        """
        all_clusters = get_all_clusters()
        bns_service_ob = bns_service.BNSService(conf['bns_endpoint'], conf['token'], conf['run_user'], "")
        result = bns_service_ob.get_group_info(conf['bns_group'])
        if result['success'] == 0:
            return False
        online_service = result['data']['serviceNames'].split(",")
        expect_service = []
        for cluster in all_clusters:
            if cluster['version'] == 1001:
                expect_service.append("cluster-{}-{}".format(cluster['id'], conf['proxy']))
                expect_service.append("cluster-{}-{}".format(cluster['id'], conf['memcache']))
            elif cluster['version'] == 7001:
                expect_service.append("cluster-{}-{}".format(cluster['id'], conf['redis']))
            elif cluster['version'] == 5001:
                expect_service.append("cluster-{}-{}".format(cluster['id'], conf['proxy']))
                expect_service.append("cluster-{}-{}".format(cluster['id'], conf['redis']))
        if action == "clear_bns_from_group":
            for service in set(online_service).difference(set(expect_service)):
                if mode == "run":
                    print "start remove service {} from {}".format(service, conf['bns_group'])
                    logging.info("start remove service {} from {}".format(service, conf['bns_group']))
                    if bns_service_ob.remove_bns_from_group(conf['bns_group'], service)["success"] == 0:
                        logging.info("remove service {} from {} fail".format(service, conf['bns_group']))
                        return False
                    logging.info("remove service {} from {} success".format(service, conf['bns_group']))
                    time.sleep(10)
                else:
                    print "[dry-run]remove service {} from {}".format(service, conf['bns_group'])
        elif action == "add_bns_to_group":
            for service in set(expect_service).difference(set(online_service)):
                if mode == "run":
                    print "start add service {} to {}".format(service, conf['bns_group'])
                    logging.info("start add service {} to {}".format(service, conf['bns_group']))
                    if bns_service_ob.add_bns_to_group(conf['bns_group'], service)["success"] == 0:
                        logging.info("add service {} to {} fail".format(service, conf['bns_group']))
                        return False
                    logging.info("add service {} to {} success".format(service, conf['bns_group']))
                    time.sleep(10)
                else:
                    print "[dry-run]start add service {} to {}".format(service, conf['bns_group'])
        return True

if __name__ == "__main__":

    init_log(os.path.join(g_script_path, "log/bns_group_op"))
    parser = argparse.ArgumentParser(description='Scs tools for bns.')
    parser.add_argument('-a', '--action', choices=['clear_bns_from_group', 'add_bns_to_group'], required=True)
    parser.add_argument('-r', '--region', required=True)
    parser.add_argument('-m', '--mode', choices=['dry-run', 'run'], required=True)
    args = parser.parse_args()
    monitor_conf = ConfigParser.RawConfigParser()
    conf = {}
    try:
        monitor_conf.read(MONITOR_CONF)
        if monitor_conf.has_section(args.region):
            conf["proxy"] = monitor_conf.get(args.region, "proxy")
            conf["redis"] = monitor_conf.get(args.region, "redis")
            conf["memcache"] = monitor_conf.get(args.region, "memcache")
            conf["bns_endpoint"] = monitor_conf.get(args.region, "bns_endpoint")
            conf["bns_group"] = monitor_conf.get(args.region, "bns_group")
            conf["token"] = monitor_conf.get(args.region, "token")
            conf["run_user"] = monitor_conf.get(args.region, "run_user")
    except:
        print("get conf fail")
        sys.exit(2)
    bgo = BnsGroupOperation(conf)
    if bgo.execute(args.action, args.mode):
        print "Success"
        sys.exit(0)
    else:
        print "Fail"
        sys.exit(1)