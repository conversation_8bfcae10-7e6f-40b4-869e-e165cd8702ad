#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""


作者: jintao01(<EMAIL>)
日期: August 3, 2020
"""

import json
import os
import pymysql
import logging
import logging.handlers
import time
import urllib2
import cookielib
import datetime
import sys
from contextlib import contextmanager


g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"   
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


@contextmanager
def open_w_error(filename, mode):
    """ open a file and check exception 
    """
    try:
        f = open(filename, mode)
    except IOError as err:
        yield None, err
    else:
        try:
            yield f, None
        finally:
            f.close()


def get_uuid_list():
    """get instance'uuid whose status not equal to 10  from database 
    """
    uuid_list = list()
    sql_conn = pymysql.connect(**get_mysql_config())

    with sql_conn.cursor() as cursor:
        sql = "select cache_instance.uuid from cache_cluster, cache_instance " \
            "where cache_cluster.status!=10 and cache_cluster.id=cache_instance.cluster_id;"
        try:
            cursor.execute(sql)
            ret = cursor.fetchall()
        except Exception as err:
            logging.exception("datbase exec sql error: %s" % err)
        for row in ret:
            uuid_list.append(row[0])
    return uuid_list


def obtain_bcc_resource(timepoint):
    """obtain all bcc resource info
    """
    cookie = cookielib.CookieJar()
    handler = urllib2.HTTPCookieProcessor(cookie)
    opener = urllib2.build_opener(handler)
    openapi_url = "http://showx.baidu.com/openapi/login?group=cbubcc&showx_token=cbubcc"
    try:
        response = opener.open(openapi_url)
    except Exception as err:
        logging.exception("send showX request error: %s" % err)

    if response.getcode() != 200:
        logging.error("showX login failed, http_code: %d" % response.getcode()) 
        sys.exit(-1)
    res_data = json.loads(response.read())
    if res_data["status"] != 0:
        logging.error("showX login failed, err: %s" % res_data["data"]) 
        sys.exit(-1)
    
    showX_url = "http://showx.baidu.com/api/group/327/report/72939/chart/211051?" \
	"conditions=[{\"t\":\"daterange\",\"k\":\"date_range\",\"v\":\"%s\"},{\"t\":\"select\",\"k\":\"source\",\"v\":\"scs\"}]"
    try:
        response = opener.open(showX_url % (timepoint.strftime("%Y-%m-%d") + "," + timepoint.strftime("%Y-%m-%d")))
    except Exception as err:
        logging.exception("send getdata request error: %s" % err)

    if response.getcode() != 200:
        logging.error("showX getdata failed, http_code: %d" % response.getcode()) 
        sys.exit(-1)
    res_data = json.loads(response.read())
    if res_data["status"] != 0:
        logging.error("showX getdata failed, err: %d" % res_data["data"]) 
        sys.exit(-1)
    if "rows" not in res_data["data"].keys():
        logging.error("showX getdata failed, err: %s" % res_data["data"]) 
        sys.exit(-1)
    rows = res_data["data"]["rows"]
    return rows


def filter_bcc_resource(rows, region):
    """filter bcc resource info by region
    """
    res_resource = list()
    for item in rows:
        record = dict()
        if item["region"] == region:
            record["uuid"] = item["uuid"]
            record["user_id"] = item["user_id"]
            res_resource.append(record)
    return res_resource


def record_uuid_into_file(res_resource, uuid_list):
    """record all disclose bcc resources' uuid, and write to file
    """
    date_strf = "%Y-%m-%d"
    file_name = "finded_disclose_bcc_%s.csv" % datetime.datetime.today().strftime(date_strf)
    with open_w_error(os.path.join(g_script_path, file_name), 'wt') as (f, err):
        if err:
            logging.error("open file for writting error: %s" % err)
        else:
            for record in res_resource:
                if record["uuid"] not in uuid_list:
                    logging.info("find disclose bcc resource %s" % record["uuid"])
                    f.write(record["uuid"] + '\t' + record["user_id"] + '\n')


def main(region):
    """the main function to coordinate the script execution
    """
    init_log(os.path.join(g_script_path, "log/execute_find_disclose_bcc"))
    start_day = datetime.datetime.today() + datetime.timedelta(-1)
    bcc_resource = obtain_bcc_resource(start_day)
    filtered_resource = filter_bcc_resource(bcc_resource, region)
    uuid_list = get_uuid_list()
    record_uuid_into_file(filtered_resource, uuid_list)

if __name__ == "__main__":
    """need provide region as script parameter
    """
    args = sys.argv[1:]
    if len(args) != 1:
        print("parameter error: need regoin as script parameter")
        sys.exit()
    main(args[0])
