#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""


作者: jintao01(<EMAIL>)
日期: July 16, 2020
"""

import os
import pymysql
import logging
import logging.handlers
from collections import Counter
from collections import defaultdict


g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"   
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_instance_info(user_type):
    """get instance whose res_flavor property is empty
    """
    instance_list = list()
    sql_conn = pymysql.connect(**get_mysql_config())

    with sql_conn.cursor() as cursor:
        sql = "select cache_instance.cluster_id, cache_instance.res_flavor, cache_cluster.version, " \
	    "cache_cluster.node_type, cache_instance.cache_instance_type " \
            "from cache_cluster, cache_instance, userinfo " \
            "where cache_cluster.status!=10 and cache_cluster.status!=12 and cache_cluster.id=cache_instance.cluster_id and " \
            "cache_instance.user_id=userinfo.id and userinfo.type=%d;" % (user_type)
        try:
            cursor.execute(sql)
            ret = cursor.fetchall()
        except Exception as err:
            logging.exception("datbase exec sql error: %s" % err)
        for row in ret:
            ins = dict()
            ins["cluster"], ins["flavor"], ins["version"], ins["node_type"], ins["instance_type"] = row
            instance_list.append(ins)
    return instance_list


def insert_data_to_db(fill_type, res_data):
    """save res_data to db
    """
    sql_conn = pymysql.connect(autocommit=True, **get_mysql_config())
    with sql_conn.cursor() as cursor:
        sql = "insert into res_statistics " \
        "(ncluster, ninstance, vcpu, mem, disk, nv1, nv5, nv7, n1, n2, n4, n8, n16, n32, n64, nproxy, type) " \
        "values (%d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d);" \
	 % (res_data["ncluster"], res_data["ninstance"], res_data["vcpu"], res_data["mem"], res_data["disk"],
        res_data["nv1"], res_data["nv5"], res_data["nv7"], res_data["n1"], res_data["n2"], res_data["n4"], 
        res_data["n8"], res_data["n16"], res_data["n32"], res_data["n64"], res_data["nproxy"], fill_type)
        logging.info("execute sql: %s" % sql)
        try:
            cursor.execute(sql)
        except Exception as err:
            logging.exception("database insert data error: %s" % err)


def process_instance_info(instance_list):
    """process instance info, and return res_statistics table needs data 
    """
    res_data = defaultdict(int)
    cluster_info = list()
    cluster_set = set()
    for instance in instance_list:
        if instance["cluster"] not in cluster_set:
            cluster_set.add(instance["cluster"])
            cluster = dict()
            cluster["id"] = instance["cluster"]
            cluster["version"] = instance["version"]
            cluster["node_type"] = instance["node_type"]
            cluster_info.append(cluster)
        if instance["instance_type"] == 0:
            res_data["nproxy"] += 1
        if instance["flavor"]:
            cpu, mem, diskroot, diskhome = map(int, instance["flavor"].split("_"))
            res_data["vcpu"] += cpu
            res_data["mem"] += mem
            res_data["disk"] += (diskhome + diskroot)
    res_data["ninstance"] = len(instance_list)
    res_data["ncluster"] = len(cluster_set)
    for cluster in cluster_info:
        if cluster["version"] == 1001:
            res_data["nv1"] += 1
        elif cluster["version"] == 5001:
            res_data["nv5"] += 1
        else:
            res_data["nv7"] += 1
        if cluster["node_type"] == "cache.n1.micro":
            res_data["n1"] += 1
        elif cluster["node_type"] == "cache.n1.small":
            res_data["n2"] += 1
        elif cluster["node_type"] == "cache.n1.medium":
            res_data["n4"] += 1
        elif cluster["node_type"] == "cache.n1.large":
            res_data["n8"] += 1
        elif cluster["node_type"] == "cache.n1.xlarge":
            res_data["n16"] += 1
        elif cluster["node_type"] == "cache.n1.2xlarge":
            res_data["n32"] += 1
        elif cluster["node_type"] == "cache.n1.4xlarge":
            res_data["n64"] += 1
        else:
            pass
    return res_data


def job():
    """the main job to execute
    """
    init_log(os.path.join(g_script_path, "log/resources_statistics"))
    logging.info("process resource staticstics for external users.txt")
    instance_list = get_instance_info(0)
    res_data_external = process_instance_info(instance_list)
    # fill_type = 1 means user's type equals zero for db
    insert_data_to_db(1, res_data_external)

    logging.info("process resource staticstics for internal users.txt")
    instance_list = get_instance_info(1)
    res_data_internal = process_instance_info(instance_list)
    # fill_type = 2 means user's type equals one for db
    insert_data_to_db(2, res_data_internal)

    # logging.info("process resource staticstics for spinoff users.txt")
    # instance_list = get_instance_info(2)
    # res_data_spinoff = process_instance_info(instance_list)
    # fill_type = 3 means user's type equals two for db
    # insert_data_to_db(3, res_data_spinoff)

    logging.info("process total resource staticstics")
    res_data = defaultdict(int, Counter(res_data_external) + Counter(res_data_internal))
    # fill_type = 0 means calculate total useage for  db
    insert_data_to_db(0, res_data)


if __name__ == "__main__":
    job()
