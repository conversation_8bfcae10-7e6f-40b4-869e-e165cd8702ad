#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""


作者: jintao01(<EMAIL>)
日期: July 16, 2020
"""

import json
import os
import pymysql
import csv
import commands
import logging
import logging.handlers
import time
import urllib2
import cookielib
import datetime
import sys
from contextlib import contextmanager


g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"   
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_inst_without_res_flavor():
    """get instance whose res_flavor property is empty
    """
    instance_dict = dict()
    uuid_list = list()

    sql_conn = pymysql.connect(**get_mysql_config())

    with sql_conn.cursor() as cursor:
        sql = "select cache_instance.user_id, cache_instance.cluster_id, cache_instance.id, cache_instance.uuid " \
                "from cache_cluster, cache_instance " \
                "where cache_cluster.status!=10 and cache_cluster.status!=12 " \
                "and cache_instance.cluster_id=cache_cluster.id and cache_instance.res_flavor='';"
        try:
            cursor.execute(sql)
            ret = cursor.fetchall()
        except Exception as err:
            logging.exception("database error: %s" % err)
        for row in ret:
            instance_info = dict()
            instance_info["user_id"] = row[0]
            instance_info["cluster_id"] = row[1]
            instance_info["instance_id"] = row[2]
            instance_info["uuid"] = row[3]
            uuid_list.append(row[3])
            instance_dict[row[3]] = instance_info
        return instance_dict, uuid_list


def get_res_flavor_from_file(filename, uuid_list):
    """ get res_flavor info from file
    """
    ret = dict()
    with open_w_error(filename, 'rt') as (f, err):
        if err:
           logging.error("open file error: %s" % err)
        else:
            reader = csv.reader(f, delimiter=' ')
            for line in reader:
                uuid = line[1]
                if uuid in uuid_list:
                    ret[uuid] = line[2].replace("-", "_")
    return ret


@contextmanager
def open_w_error(filename, mode):
    """ open a file and check exception 
    """
    try:
        f = open(filename, mode)
    except IOError as err:
        yield None, err
    else:
        try:
            yield f, None
        finally:
            f.close()
 

def fill_res_flavor(res_flavor, instance_info):
    """ fill res_flavor into db
    """
    octopus_path = "/home/<USER>/scs/octopus/"
    if not os.path.isdir(octopus_path):
        octopus_path = "/home/<USER>/scs/octopus"
    cmd = [os.path.join(octopus_path, "octopus"),
         "modify_instance_res_flavor %s %s %s %s %s" % (instance_info["user_id"],
                instance_info["cluster_id"],
                instance_info["instance_id"],
                instance_info["uuid"],
                res_flavor)]
    logging.info("start execute cmd %s" % cmd)
    status, output = commands.getstatusoutput(" ".join(cmd))
    if status:
        logging.error("fill res_flavor for %s faild, meg: %s" % (instance_info["uuid"], output))


def save_to_csv(rows):
    """save data to csv file
    """
    with open_w_error(os.path.join(g_script_path, "res_flavor.csv"), 'wt') as (f, err):
        if err:
            logging.error("open file error: %s" % err)
        else:
            writer = csv.writer(f, delimiter=' ')
            # csv_head = ["user_id", "uuid", "res_falvor", "source", "region", "cluster", "host", "event_day", "az"]
            # writer.writerow(csv_head)
            for row in rows:
                data_row = [row["user_id"], row["uuid"], row["instance_type_id"], row["source"],
	            row["region"], row["cluster"], row["host"], row["event_day"], row["az"]]
                writer.writerow(data_row)


def obtain_bcc_resource(timepoint):
    """obtain all bcc resource info
    """
    cookie = cookielib.CookieJar()
    handler = urllib2.HTTPCookieProcessor(cookie)
    opener = urllib2.build_opener(handler)
    openapi_url = "http://showx.baidu.com/openapi/login?group=cbubcc&showx_token=cbubcc"
    try:
        response = opener.open(openapi_url)
    except Exception as err:
        logging.exception("send showX request error: %s" % err)

    if response.getcode() != 200:
        logging.error("showX login failed, http_code: %d" % response.getcode()) 
        sys.exit(-1)
    res_data = json.loads(response.read())
    if res_data["status"] != 0:
        logging.error("showX login failed, err: %s" % res_data["data"]) 
        sys.exit(-1)
    
    showX_url = "http://showx.baidu.com/api/group/327/report/72939/chart/211051?" \
	"conditions=[{\"t\":\"daterange\",\"k\":\"date_range\",\"v\":\"%s\"},{\"t\":\"select\",\"k\":\"source\",\"v\":\"scs\"}]"
    try:
        response = opener.open(showX_url % (timepoint.strftime("%Y-%m-%d") + "," + timepoint.strftime("%Y-%m-%d")))
    except Exception as err:
        logging.exception("send getdata request error: %s" % err)

    if response.getcode() != 200:
        logging.error("showX getdata failed, http_code: %d" % response.getcode()) 
        sys.exit(-1)
    res_data = json.loads(response.read())
    if res_data["status"] != 0:
        logging.error("showX getdata failed, err: %d" % res_data["data"]) 
        sys.exit(-1)
    if "rows" not in res_data["data"].keys():
        logging.error("showX getdata failed, err: %s" % res_data["data"]) 
        sys.exit(-1)
    rows = res_data["data"]["rows"]
    # 数据写入csv文件
    save_to_csv(rows)


def job():
    """the main job to excute
    """
    init_log(os.path.join(g_script_path, "log/execute_res-flavor"))
    start_day = datetime.datetime.today() + datetime.timedelta(-1)
    obtain_bcc_resource(start_day)
    instance_dict, uuid_list = get_inst_without_res_flavor()
    res_flavor_dict = get_res_flavor_from_file(os.path.join(g_script_path, "res_flavor.csv"), uuid_list)
    for uuid, res_flavor in res_flavor_dict.items():    
        instance_info = instance_dict[uuid]
        fill_res_flavor(res_flavor, instance_info)


if __name__ == "__main__":
    job()
