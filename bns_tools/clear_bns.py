#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
@file clear_bns.py
@bref clear bns

作者:jintao01(<EMAIL>)
日期:2021年10月25日 19:21:22
"""

import os
import pymysql
import logging
import logging.handlers
import commands
import requests
import pymysql
import time
import sys


g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "log")


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module
    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7
    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)
    log_dir = os.path.dirname(log_path)
    if not os.path.isdir(log_dir):
        os.makedirs(log_dir)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"   
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_services_by_path(bns_group=None):
    """exec get_services_by_path obtain services list
    """
    if bns_group is None:
        return
    cmd = "get_services_by_path {0}".format(bns_group)
    status, output = commands.getstatusoutput(cmd)
    if status != 0:
        logging.warning("exec get_services_by_path failed.")
        return
    file_path = os.path.join(g_script_path, "{0}".format("services_bns.txt")) 
    with open(file_path, "w") as f:
        f.write(output)
    return 


def process_services(filename=None):
    """process services into 2 part
        1 without proxy and redis and memcache
        1 with proxy or redis
    """
    post_fix = ["redisbjonline.BCE.all", 
            "proxybjonline.BCE.all", 
            "memcachebjonline.BCE.all", 
            "hsdbbjonline.BCE.all",
            "pegadbbjonline.BCE.all"]

    with open(filename, "r") as f:
        line = f.readline().strip()
        while line:
            finded = False
            for x in post_fix:
                if line.endswith(x):
                    finded = True
                    with open(os.path.join(g_script_path, "bns_new.txt"), "a+") as f_r:
                        f_r.write(line)
                        f_r.write('\n')
            if not finded:
                with open(os.path.join(g_script_path, "bns_old.txt"), "a+") as f_o:
                    f_o.write(line)
                    f_o.write('\n')
            line = f.readline().strip()
    return 


def del_old_services(file_name=None, bns_group=None, auth_key=None):
    """delete old services
    """
    with open(file_name, "r") as f:
        line = f.readline().strip()
        while line:
            req = "http://bns.noah.baidu.com/webfoot/index.php?r=group/ModifyServicesOfGroup&" \
                "groupName={0}&services={1}&authKey={2}&action=remove".format(
                    bns_group, line, auth_key)
            logging.info("start to clear {0} bns group info...".format(line))
            res = requests.get(req)
            content = res.json()
            if not content["success"]:
                logging.warning("clear groupName:{0}, service:{1} failed".format(bns_group, line))
            else:
                logging.info("clear {0} bns group info success!".format(line))
            line = f.readline().strip()
    return


def del_new_services(file_name=None, bns_group=None, auth_key=None):
    """delete new services which has been deleted
    """
    with open(file_name, "r") as f:
        line = f.readline().strip()
        while line:
            cluster_id = line.split("-")[1]
            cluster_status = get_cluster_status(cluster_id=cluster_id)
            if cluster_status in [10, 12]:
                req = "http://bns.noah.baidu.com/webfoot/index.php?r=group/ModifyServicesOfGroup&" \
                    "groupName={0}&services={1}&authKey={2}&action=remove".format(
                        bns_group, line, auth_key)
                logging.info("start to clear {0} bns group info...".format(cluster_id))
                res = requests.get(req)
                content = res.json()
                if not content["success"]:
                    logging.warning("clear groupName:{0}, service:{1} failed".format(bns_group, line))
                else:
                    logging.info("clear {0} bns group info success!".format(cluster_id))
            else:
                logging.info("cluster {0} is running... not to clear bns.".format(cluster_id))
            line = f.readline().strip()
    return


def get_cluster_status(cluster_id=0):
    """get cluster_status
    """
    ret = get_mysql_config()
    sql_conn = pymysql.connect(
        host=ret["host"],
        user=ret["user"],
        password=ret["password"],
        port=ret["port"],
        database=ret["db"]
    )

    with sql_conn.cursor() as cursor:
        sql = "select status from cache_cluster where id = {0}".format(cluster_id)
        try:
            cursor.execute(sql)
            ret = cursor.fetchall()
        except Exception as err:
            logging.exception("database error: %s" % err)
        for row in ret:
            return row[0]


def main(bns_group, auth_key):
    """the main function to run
    """
    init_log(os.path.join(g_script_path, "clear_bns"))
    get_services_by_path(bns_group)
    file_path = os.path.join(g_script_path, "{0}".format("services_bns.txt"))
    process_services(file_path)
    file_path = os.path.join(g_script_path, "{0}".format("bns_old.txt"))
    del_old_services(file_path, bns_group, auth_key)
    file_path = os.path.join(g_script_path, "{0}".format("bns_new.txt"))
    del_new_services(file_path, bns_group, auth_key)


if __name__ == "__main__":
    """need provide region as script parameter
    """
    args = sys.argv[1:]
    if len(args) != 2:
        print("parameter error: need bns_group, auth_key as script parameter")
        sys.exit()
    main(args[0], args[1])