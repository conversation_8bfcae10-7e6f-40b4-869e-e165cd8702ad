#!/bin/bash
# run_container.sh - 创建符合对应limit的容器
# 当对应变量为空时，表示没有对应limit

# 全局参数配置
CONTAINER_NAME="test"
DEVICE="/dev/vdb"
WRITE_LIMIT="50000000"  # 50MB/s (字节/秒)
READ_LIMIT="50000000"   # 40MB/s (字节/秒) (可选)
CPU_LIMIT="1"          # CPU核心数
MEMORY_LIMIT="2G"      # 内存限制
VOLUME_TEMPLATE="/mnt/container/docker_bind/{name}:/home"  # 模板，{name}将被替换
IMAGE="registry.baidubce.com/scs_test_cce/mysql:v1.0"

# 解析命令行参数
NAME=""
VOLUME=""

# 日志函数
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1" >&2
}

log_warn() {
    echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查 cgroup 版本
check_cgroup_version() {
    local cgroup_version
    cgroup_version=$(docker info 2>/dev/null | grep -i 'Cgroup Version' | awk -F': ' '{print $2}' | tr -d ' ')

    if [[ -z "$cgroup_version" ]]; then
        # 如果无法从docker info获取，尝试检查系统cgroup版本
        if [[ -d "/sys/fs/cgroup/unified" ]] || [[ -f "/sys/fs/cgroup/cgroup.controllers" ]]; then
            cgroup_version="2"
        else
            cgroup_version="1"
        fi
    fi

    echo "$cgroup_version"
}

CGROUP_VERSION=$(check_cgroup_version)
log_info "检测到 cgroup 版本: v${CGROUP_VERSION}"

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                NAME="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                usage
                exit 1
                ;;
        esac
    done

    # 验证必需参数
    if [[ -z "$NAME" ]]; then
        log_error "缺少必需参数: name"
        log_error "请使用 -n 或 --name 指定名称"
        usage
        exit 1
    fi

    # 根据name生成实际的VOLUME路径
    CONTAINER_NAME="$NAME"
    VOLUME="${VOLUME_TEMPLATE/\{name\}/$NAME}"
    log_info "使用名称: $NAME"
    log_info "容器名称: $CONTAINER_NAME"
    log_info "生成卷挂载路径: $VOLUME"
}

# 检查容器是否存在
check_container_exists() {
    local container_name="$1"
    docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"
}

# 停止并删除容器
cleanup_container() {
    local container_name="$1"

    if check_container_exists "$container_name"; then
        log_info "发现已存在的容器 $container_name，正在清理..."

        # 停止容器
        if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            log_info "停止容器 $container_name"
            docker stop "$container_name" >/dev/null 2>&1
        fi

        # 删除容器
        log_info "删除容器 $container_name"
        docker rm "$container_name" >/dev/null 2>&1

        if [[ $? -eq 0 ]]; then
            log_info "容器 $container_name 清理完成"
        else
            log_error "容器 $container_name 清理失败"
            return 1
        fi
    fi
    return 0
}

# 验证参数格式
validate_limits() {
    # 验证CPU限制格式
    if [[ -n "$CPU_LIMIT" ]]; then
        if ! [[ "$CPU_LIMIT" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
            log_error "CPU_LIMIT 格式错误: $CPU_LIMIT (应为数字，如: 2 或 2.5)"
            return 1
        fi
    fi

    # 验证内存限制格式
    if [[ -n "$MEMORY_LIMIT" ]]; then
        if ! [[ "$MEMORY_LIMIT" =~ ^[0-9]+[kmgKMG]?[bB]?$ ]]; then
            log_error "MEMORY_LIMIT 格式错误: $MEMORY_LIMIT (应为如: 1G, 512M, 1024m)"
            return 1
        fi
    fi

    # 验证I/O限制格式（应为数字，单位为字节/秒）
    if [[ -n "$READ_LIMIT" ]]; then
        if ! [[ "$READ_LIMIT" =~ ^[0-9]+$ ]]; then
            log_error "READ_LIMIT 格式错误: $READ_LIMIT (应为数字，单位字节/秒)"
            return 1
        fi
    fi

    if [[ -n "$WRITE_LIMIT" ]]; then
        if ! [[ "$WRITE_LIMIT" =~ ^[0-9]+$ ]]; then
            log_error "WRITE_LIMIT 格式错误: $WRITE_LIMIT (应为数字，单位字节/秒)"
            return 1
        fi
    fi

    return 0
}

# 构建Docker运行命令
build_docker_command() {
    local docker_cmd="docker run -d"

    # 添加容器名称
    docker_cmd="$docker_cmd --name $CONTAINER_NAME"

    # 添加卷挂载
    if [[ -n "$VOLUME" ]]; then
        docker_cmd="$docker_cmd -v $VOLUME"
    fi

    # 添加CPU限制
    if [[ -n "$CPU_LIMIT" ]]; then
        docker_cmd="$docker_cmd --cpus=$CPU_LIMIT"
    fi

    # 添加内存限制
    if [[ -n "$MEMORY_LIMIT" ]]; then
        docker_cmd="$docker_cmd --memory=$MEMORY_LIMIT"
    fi

    # 只有在cgroup v1时才添加Docker的I/O限制参数
    if [[ "$CGROUP_VERSION" == "1" ]]; then
        if [[ -n "$DEVICE" ]]; then
            if [[ -n "$READ_LIMIT" ]]; then
                docker_cmd="$docker_cmd --device-read-bps $DEVICE:$READ_LIMIT"
            fi

            if [[ -n "$WRITE_LIMIT" ]]; then
                docker_cmd="$docker_cmd --device-write-bps $DEVICE:$WRITE_LIMIT"
            fi
        fi
    fi

    # 添加镜像名称
    docker_cmd="$docker_cmd $IMAGE"

    echo "$docker_cmd"
}

# 查找容器的cgroup路径
find_container_cgroup_path() {
    local container_id="$1"

    # 定义可能的cgroup路径模式
    local possible_paths=(
        "/sys/fs/cgroup/docker.slice/docker-${container_id}.scope"
        "/sys/fs/cgroup/system.slice/docker-${container_id}.scope"
        "/sys/fs/cgroup/docker/${container_id}"
        "/sys/fs/cgroup/docker.service/docker-${container_id}.scope"
        "/sys/fs/cgroup/machine.slice/docker-${container_id}.scope"
        "/sys/fs/cgroup/user.slice/docker-${container_id}.scope"
    )

    # 查找存在的路径
    for path in "${possible_paths[@]}"; do
        if [[ -d "$path" ]]; then
            echo "$path"
            return 0
        fi
    done

    # 如果没有找到，尝试动态查找
    local cgroup_root="/sys/fs/cgroup"
    if [[ -d "$cgroup_root" ]]; then
        local found_path
        found_path=$(find "$cgroup_root" -name "*docker-${container_id}.scope" -type d 2>/dev/null | head -1)
        if [[ -n "$found_path" ]]; then
            echo "$found_path"
            return 0
        fi

        # 尝试查找包含容器ID的目录
        found_path=$(find "$cgroup_root" -name "*${container_id}*" -type d 2>/dev/null | head -1)
        if [[ -n "$found_path" ]]; then
            echo "$found_path"
            return 0
        fi
    fi

    return 1
}

# 为cgroup v2设置I/O限制
setup_cgroup_v2_io_limits() {
    local container_name="$1"

    if [[ "$CGROUP_VERSION" != "2" ]]; then
        return 0
    fi

    if [[ -z "$READ_LIMIT" ]] && [[ -z "$WRITE_LIMIT" ]]; then
        log_info "未设置I/O限制，跳过cgroup v2 I/O限制设置"
        return 0
    fi

    if [[ -z "$DEVICE" ]]; then
        log_warn "未指定DEVICE，无法设置cgroup v2 I/O限制"
        return 1
    fi

    log_info "开始设置cgroup v2 I/O限制..."

    # 获取容器的cgroup路径
    local container_id
    container_id=$(docker inspect --format='{{.Id}}' "$container_name" 2>/dev/null)

    if [[ -z "$container_id" ]]; then
        log_error "无法获取容器ID"
        return 1
    fi

    # 查找容器的cgroup路径
    local cgroup_path
    cgroup_path=$(find_container_cgroup_path "$container_id")

    if [[ -z "$cgroup_path" ]]; then
        log_warn "无法找到容器的cgroup路径"
        log_info "尝试的路径模式："
        log_info "  - /sys/fs/cgroup/docker.slice/docker-${container_id}.scope"
        log_info "  - /sys/fs/cgroup/system.slice/docker-${container_id}.scope"
        log_info "  - /sys/fs/cgroup/docker/${container_id}"
        log_info "  - /sys/fs/cgroup/docker.service/docker-${container_id}.scope"
        log_info "  - /sys/fs/cgroup/machine.slice/docker-${container_id}.scope"
        log_info "  - /sys/fs/cgroup/user.slice/docker-${container_id}.scope"
        log_info "以及动态查找 /sys/fs/cgroup 下的相关目录"
        return 1
    fi

    log_info "找到容器cgroup路径: $cgroup_path"

    # 获取设备的major:minor号
    local device_major_minor
    device_major_minor=$(lsblk -no MAJ:MIN "$DEVICE" 2>/dev/null | head -1 | tr -d ' ')

    if [[ -z "$device_major_minor" ]]; then
        log_error "无法获取设备 $DEVICE 的major:minor号"
        return 1
    fi

    log_info "设备 $DEVICE 的major:minor号: $device_major_minor"

    # 设置I/O限制
    local io_max_file="$cgroup_path/io.max"

    if [[ -f "$io_max_file" ]]; then
        local io_limit=""

        if [[ -n "$READ_LIMIT" ]] && [[ -n "$WRITE_LIMIT" ]]; then
            io_limit="${device_major_minor} rbps=${READ_LIMIT} wbps=${WRITE_LIMIT}"
        elif [[ -n "$READ_LIMIT" ]]; then
            io_limit="${device_major_minor} rbps=${READ_LIMIT}"
        elif [[ -n "$WRITE_LIMIT" ]]; then
            io_limit="${device_major_minor} wbps=${WRITE_LIMIT}"
        fi

        if [[ -n "$io_limit" ]]; then
            echo "$io_limit" > "$io_max_file" 2>/dev/null
            if [[ $? -eq 0 ]]; then
                log_info "成功设置cgroup v2 I/O限制: $io_limit"

                # 验证设置是否生效
                local current_limit
                current_limit=$(cat "$io_max_file" 2>/dev/null | grep "$device_major_minor")
                if [[ -n "$current_limit" ]]; then
                    log_info "当前I/O限制: $current_limit"
                else
                    log_warn "设置后无法验证I/O限制，可能未生效"
                fi
            else
                log_error "设置cgroup v2 I/O限制失败，可能需要root权限"
                log_error "尝试手动执行: echo '$io_limit' > $io_max_file"
                return 1
            fi
        fi
    else
        log_warn "找不到io.max文件: $io_max_file"
        log_info "cgroup路径内容："
        ls -la "$cgroup_path" 2>/dev/null || log_warn "无法列出cgroup路径内容"
        return 1
    fi

    return 0
}

# 显示配置信息
show_config() {
    log_info "=== 容器配置信息 ==="
    log_info "容器名称: $CONTAINER_NAME"
    log_info "镜像: $IMAGE"
    log_info "cgroup版本: v$CGROUP_VERSION"

    if [[ -n "$VOLUME" ]]; then
        log_info "卷挂载: $VOLUME"
    else
        log_info "卷挂载: 未设置"
    fi

    if [[ -n "$CPU_LIMIT" ]]; then
        log_info "CPU限制: $CPU_LIMIT 核"
    else
        log_info "CPU限制: 未设置"
    fi

    if [[ -n "$MEMORY_LIMIT" ]]; then
        log_info "内存限制: $MEMORY_LIMIT"
    else
        log_info "内存限制: 未设置"
    fi

    if [[ -n "$DEVICE" ]]; then
        log_info "I/O设备: $DEVICE"
        if [[ -n "$READ_LIMIT" ]]; then
            log_info "读取限制: $READ_LIMIT 字节/秒"
        else
            log_info "读取限制: 未设置"
        fi

        if [[ -n "$WRITE_LIMIT" ]]; then
            log_info "写入限制: $WRITE_LIMIT 字节/秒"
        else
            log_info "写入限制: 未设置"
        fi

        # 显示I/O限制的处理方式
        if [[ "$CGROUP_VERSION" == "1" ]]; then
            log_info "I/O限制方式: Docker参数 (cgroup v1)"
        else
            log_info "I/O限制方式: 容器启动后设置 (cgroup v2)"
        fi
    else
        log_info "I/O设备: 未设置"
        log_info "I/O限制: 未设置"
    fi

    log_info "===================="
}

# 显示应用的限制设置
show_applied_limits() {
    log_info "=== 应用的限制设置 ==="

    # 显示卷挂载
    if [[ -n "$VOLUME" ]]; then
        log_info "✓ 卷挂载: $VOLUME"
    fi

    # 显示CPU限制
    if [[ -n "$CPU_LIMIT" ]]; then
        log_info "✓ CPU限制: $CPU_LIMIT 核"
    fi

    # 显示内存限制
    if [[ -n "$MEMORY_LIMIT" ]]; then
        log_info "✓ 内存限制: $MEMORY_LIMIT"
    fi

    # 显示I/O限制
    if [[ -n "$DEVICE" ]]; then
        if [[ "$CGROUP_VERSION" == "1" ]]; then
            if [[ -n "$READ_LIMIT" ]]; then
                log_info "✓ 读取限制 (cgroup v1): $DEVICE:$READ_LIMIT 字节/秒"
            fi
            if [[ -n "$WRITE_LIMIT" ]]; then
                log_info "✓ 写入限制 (cgroup v1): $DEVICE:$WRITE_LIMIT 字节/秒"
            fi
        else
            log_info "✓ I/O限制将在容器启动后设置 (cgroup v2)"
        fi
    fi

    log_info "=========================="
}

# 主函数
main() {
    # 解析命令行参数
    parse_arguments "$@"

    log_info "开始创建容器..."

    # 显示配置
    show_config

    # 验证参数
    if ! validate_limits; then
        log_error "参数验证失败"
        exit 1
    fi

    # 测试模式：仅显示配置后退出（用于验证参数解析）
    if [[ "${TEST_MODE:-}" == "true" ]]; then
        log_info "测试模式：参数解析和配置显示完成"
        exit 0
    fi

    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行或无法访问"
        exit 1
    fi

    # 检查镜像是否存在
    if ! docker image inspect "$IMAGE" >/dev/null 2>&1; then
        log_warn "镜像 $IMAGE 不存在，尝试拉取..."
        if ! docker pull "$IMAGE"; then
            log_error "无法拉取镜像 $IMAGE"
            exit 1
        fi
    fi

    # 清理已存在的容器
    if ! cleanup_container "$CONTAINER_NAME"; then
        log_error "清理容器失败"
        exit 1
    fi

    # 构建Docker命令
    local docker_cmd
    docker_cmd=$(build_docker_command)

    # 显示应用的限制设置
    show_applied_limits

    log_info "执行Docker命令: $docker_cmd"

    # 运行容器
    local container_id
    container_id=$(eval "$docker_cmd")

    if [[ $? -eq 0 ]] && [[ -n "$container_id" ]]; then
        log_info "容器创建成功，ID: $container_id"

        # 等待容器启动
        sleep 2

        # 检查容器状态
        if docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
            log_info "容器 $CONTAINER_NAME 运行正常"

            # 为cgroup v2设置I/O限制
            if [[ "$CGROUP_VERSION" == "2" ]]; then
                setup_cgroup_v2_io_limits "$CONTAINER_NAME"
            fi

            # 显示容器信息
            log_info "容器创建完成"
            log_info "容器状态: $(docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' --filter name=$CONTAINER_NAME)"

        else
            log_error "容器启动失败"
            log_error "容器日志:"
            docker logs "$CONTAINER_NAME"
            exit 1
        fi
    else
        log_error "容器创建失败"
        exit 1
    fi
}

# 使用说明
usage() {
    cat << EOF
使用说明: $0 -n <name> [选项]

该脚本根据预设的全局参数创建Docker容器，支持CPU、内存和I/O限制。
当对应变量为空时，表示没有对应限制。

必需参数:
  -n, --name <name>  指定名称，用于生成卷挂载路径

全局参数:
  CONTAINER_NAME     - 容器名称 (当前: $CONTAINER_NAME)
  IMAGE             - Docker镜像 (当前: $IMAGE)
  CPU_LIMIT         - CPU限制，核心数 (当前: ${CPU_LIMIT:-未设置})
  MEMORY_LIMIT      - 内存限制 (当前: ${MEMORY_LIMIT:-未设置})
  DEVICE            - I/O限制的设备 (当前: ${DEVICE:-未设置})
  READ_LIMIT        - 读取限制，字节/秒 (当前: ${READ_LIMIT:-未设置})
  WRITE_LIMIT       - 写入限制，字节/秒 (当前: ${WRITE_LIMIT:-未设置})
  VOLUME_TEMPLATE   - 卷挂载模板 (当前: $VOLUME_TEMPLATE)

选项:
  -h, --help        显示此帮助信息

示例:
  $0 -n myapp                    # 使用名称 'myapp' 创建容器
  $0 --name test_env             # 使用名称 'test_env' 创建容器

生成的卷挂载路径示例:
  名称 'myapp' -> /mnt/container/docker_bind/myapp:/home
  名称 'test_env' -> /mnt/container/docker_bind/test_env:/home

注意:
  - cgroup v1: 使用Docker原生的--device-read-bps和--device-write-bps参数
  - cgroup v2: 容器启动后使用cgroupv2的io.max机制设置I/O限制
  - 设置cgroup v2的I/O限制需要root权限
EOF
}

# 脚本入口点
if [[ $# -eq 0 ]]; then
    log_error "缺少必需参数"
    usage
    exit 1
fi

main "$@"
