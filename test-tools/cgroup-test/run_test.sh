#!/bin/bash

# Docker容器性能测试脚本
# 支持CPU和磁盘IO基准测试，专门用于比较cgroup v1和v2的性能差异

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="${SCRIPT_DIR}/performance_report_${TIMESTAMP}.txt"

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $msg"
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $msg" >> "$REPORT_FILE"
}

log_warn() {
    local msg="$1"
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $msg"
    echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') $msg" >> "$REPORT_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $msg" >&2
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $msg" >> "$REPORT_FILE"
}

log_result() {
    local msg="$1"
    echo -e "${CYAN}[RESULT]${NC} $msg"
    echo "[RESULT] $msg" >> "$REPORT_FILE"
}

# 打印帮助信息
show_help() {
    cat << EOF
用法: $0 [选项] <容器名称>

Docker容器性能测试脚本，专门用于比较cgroup v1和v2的性能差异

选项:
  -c, --cpu          只运行CPU测试
  -d, --disk         只运行磁盘IO测试
  -a, --all          运行所有测试（默认）
  -t, --time TIME    CPU测试时间（秒，默认60）
  -s, --size SIZE    磁盘测试文件大小（默认1G）
  -p, --path PATH    磁盘测试路径（默认/home）
  -r, --report FILE  指定报告文件路径
  -v, --verbose      详细输出模式
  -h, --help         显示帮助信息

测试项目:
  CPU测试:
    - sysbench CPU基准测试
    - 多线程计算密集型测试
    - CPU限制验证测试

  磁盘IO测试:
    - 顺序读写性能测试
    - 随机读写性能测试
    - I/O限制验证测试
    - 不同块大小的性能测试

输出:
  - 实时测试结果显示
  - 详细性能报告文件: $REPORT_FILE
  - cgroup版本和限制信息
  - 性能对比分析

示例:
  $0 my-container                    # 运行所有测试
  $0 -c my-container                 # 只运行CPU测试
  $0 -d -s 2G my-container           # 只运行磁盘测试，文件大小2G
  $0 -t 120 -s 500M my-container     # CPU测试120秒，磁盘测试500M
  $0 -v -r /tmp/report.txt my-container  # 详细模式，自定义报告文件

注意:
  - 需要容器中安装sysbench、dd、bc等工具
  - 建议在相同硬件环境下比较不同cgroup版本的性能
  - 测试会在容器内创建临时文件，确保有足够磁盘空间
EOF
}

# 默认参数
CPU_TEST=true
DISK_TEST=true
CPU_TIME=60
DISK_SIZE="1G"
DISK_PATH="/home"
VERBOSE=false
CONTAINER_NAME=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--cpu)
            CPU_TEST=true
            DISK_TEST=false
            shift
            ;;
        -d|--disk)
            DISK_TEST=true
            CPU_TEST=false
            shift
            ;;
        -a|--all)
            CPU_TEST=true
            DISK_TEST=true
            shift
            ;;
        -t|--time)
            CPU_TIME="$2"
            shift 2
            ;;
        -s|--size)
            DISK_SIZE="$2"
            shift 2
            ;;
        -p|--path)
            DISK_PATH="$2"
            shift 2
            ;;
        -r|--report)
            REPORT_FILE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$CONTAINER_NAME" ]]; then
                CONTAINER_NAME="$1"
            else
                echo -e "${RED}错误: 多余的参数 '$1'${NC}"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 初始化报告文件
init_report() {
    cat > "$REPORT_FILE" << EOF
================================================================================
Docker容器性能测试报告
================================================================================
测试时间: $(date '+%Y-%m-%d %H:%M:%S')
容器名称: $CONTAINER_NAME
测试参数:
  - CPU测试: $CPU_TEST (时间: ${CPU_TIME}秒)
  - 磁盘测试: $DISK_TEST (大小: $DISK_SIZE, 路径: $DISK_PATH)
  - 详细模式: $VERBOSE
================================================================================

EOF
}

# 检查参数和环境
validate_environment() {
    # 检查是否提供了容器名称
    if [[ -z "$CONTAINER_NAME" ]]; then
        log_error "请提供容器名称"
        show_help
        exit 1
    fi

    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi

    # 检查容器是否存在且正在运行
    if ! docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_error "容器 '${CONTAINER_NAME}' 不存在或未运行"
        echo -e "${YELLOW}当前运行的容器:${NC}"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
        exit 1
    fi

    # 验证参数格式
    if ! [[ "$CPU_TIME" =~ ^[0-9]+$ ]]; then
        log_error "CPU测试时间必须是正整数: $CPU_TIME"
        exit 1
    fi

    if ! [[ "$DISK_SIZE" =~ ^[0-9]+[GMK]?$ ]]; then
        log_error "磁盘测试大小格式错误: $DISK_SIZE (应为如: 1G, 512M, 1024K)"
        exit 1
    fi

    log_info "环境验证通过"
}

# 收集cgroup信息
collect_cgroup_info() {
    log_info "收集cgroup和容器限制信息..."

    # 获取容器ID
    local container_id
    container_id=$(docker inspect --format='{{.Id}}' "$CONTAINER_NAME" 2>/dev/null)

    if [[ -z "$container_id" ]]; then
        log_error "无法获取容器ID"
        return 1
    fi

    # 检测cgroup版本
    local cgroup_version
    cgroup_version=$(docker info 2>/dev/null | grep -i 'Cgroup Version' | awk -F': ' '{print $2}' | tr -d ' ')

    if [[ -z "$cgroup_version" ]]; then
        if [[ -d "/sys/fs/cgroup/unified" ]] || [[ -f "/sys/fs/cgroup/cgroup.controllers" ]]; then
            cgroup_version="2"
        else
            cgroup_version="1"
        fi
    fi

    log_result "Cgroup版本: v$cgroup_version"

    # 获取容器资源限制
    local cpu_limit memory_limit
    cpu_limit=$(docker inspect "$CONTAINER_NAME" --format='{{.HostConfig.NanoCpus}}' 2>/dev/null)
    memory_limit=$(docker inspect "$CONTAINER_NAME" --format='{{.HostConfig.Memory}}' 2>/dev/null)

    if [[ "$cpu_limit" != "0" ]] && [[ -n "$cpu_limit" ]]; then
        local cpu_cores
        cpu_cores=$(echo "scale=2; $cpu_limit / 1000000000" | bc)
        log_result "CPU限制: ${cpu_cores} 核"
    else
        log_result "CPU限制: 无限制"
    fi

    if [[ "$memory_limit" != "0" ]] && [[ -n "$memory_limit" ]]; then
        local memory_gb
        memory_gb=$(echo "scale=2; $memory_limit / 1024 / 1024 / 1024" | bc)
        log_result "内存限制: ${memory_gb} GB"
    else
        log_result "内存限制: 无限制"
    fi

    # 获取I/O限制信息
    local read_bps write_bps
    read_bps=$(docker inspect "$CONTAINER_NAME" --format='{{range .HostConfig.BlkioDeviceReadBps}}{{.Rate}}{{end}}' 2>/dev/null)
    write_bps=$(docker inspect "$CONTAINER_NAME" --format='{{range .HostConfig.BlkioDeviceWriteBps}}{{.Rate}}{{end}}' 2>/dev/null)

    if [[ -n "$read_bps" ]] && [[ "$read_bps" != "0" ]]; then
        local read_mbps
        read_mbps=$(echo "scale=2; $read_bps / 1024 / 1024" | bc)
        log_result "读取I/O限制: ${read_mbps} MB/s"
    else
        log_result "读取I/O限制: 无限制"
    fi

    if [[ -n "$write_bps" ]] && [[ "$write_bps" != "0" ]]; then
        local write_mbps
        write_mbps=$(echo "scale=2; $write_bps / 1024 / 1024" | bc)
        log_result "写入I/O限制: ${write_mbps} MB/s"
    else
        log_result "写入I/O限制: 无限制"
    fi

    # 如果是cgroup v2，尝试获取更详细的信息
    if [[ "$cgroup_version" == "2" ]]; then
        local cgroup_path="/sys/fs/cgroup/system.slice/docker-${container_id}.scope"
        if [[ ! -d "$cgroup_path" ]]; then
            cgroup_path="/sys/fs/cgroup/docker/${container_id}"
        fi

        if [[ -d "$cgroup_path" ]]; then
            log_result "Cgroup路径: $cgroup_path"

            # 读取cgroup v2的限制信息
            if [[ -f "$cgroup_path/cpu.max" ]]; then
                local cpu_max
                cpu_max=$(cat "$cgroup_path/cpu.max" 2>/dev/null || echo "max")
                log_result "Cgroup v2 CPU限制: $cpu_max"
            fi

            if [[ -f "$cgroup_path/memory.max" ]]; then
                local memory_max
                memory_max=$(cat "$cgroup_path/memory.max" 2>/dev/null || echo "max")
                if [[ "$memory_max" != "max" ]]; then
                    local memory_max_gb
                    memory_max_gb=$(echo "scale=2; $memory_max / 1024 / 1024 / 1024" | bc)
                    log_result "Cgroup v2 内存限制: ${memory_max_gb} GB"
                else
                    log_result "Cgroup v2 内存限制: 无限制"
                fi
            fi

            if [[ -f "$cgroup_path/io.max" ]]; then
                local io_max
                io_max=$(cat "$cgroup_path/io.max" 2>/dev/null || echo "")
                if [[ -n "$io_max" ]]; then
                    log_result "Cgroup v2 I/O限制: $io_max"
                fi
            fi
        fi
    fi

    echo "" >> "$REPORT_FILE"
}

# 检查容器中是否有必要的工具
check_tools() {
    log_info "检查测试工具..."

    local tools_missing=false
    local required_tools=("dd" "bc" "nproc" "stat")
    local optional_tools=("sysbench" "iostat" "vmstat")

    # 检查必需工具
    for tool in "${required_tools[@]}"; do
        if ! docker exec "$CONTAINER_NAME" which "$tool" &> /dev/null; then
            log_error "必需工具 '$tool' 不可用"
            tools_missing=true
        else
            [[ "$VERBOSE" == "true" ]] && log_info "✓ $tool 可用"
        fi
    done

    # 检查可选工具
    for tool in "${optional_tools[@]}"; do
        if ! docker exec "$CONTAINER_NAME" which "$tool" &> /dev/null; then
            log_warn "可选工具 '$tool' 不可用"

            # 尝试安装sysbench
            if [[ "$tool" == "sysbench" ]]; then
                log_info "尝试安装sysbench..."
                if docker exec "$CONTAINER_NAME" which apt-get &> /dev/null; then
                    if docker exec "$CONTAINER_NAME" bash -c "apt-get update && apt-get install -y sysbench bc" &> /dev/null; then
                        log_info "✓ sysbench 安装成功"
                    else
                        log_warn "sysbench 安装失败，将使用替代测试方法"
                    fi
                elif docker exec "$CONTAINER_NAME" which yum &> /dev/null; then
                    if docker exec "$CONTAINER_NAME" yum install -y sysbench bc &> /dev/null; then
                        log_info "✓ sysbench 安装成功"
                    else
                        log_warn "sysbench 安装失败，将使用替代测试方法"
                    fi
                elif docker exec "$CONTAINER_NAME" which apk &> /dev/null; then
                    if docker exec "$CONTAINER_NAME" apk add --no-cache sysbench bc &> /dev/null; then
                        log_info "✓ sysbench 安装成功"
                    else
                        log_warn "sysbench 安装失败，将使用替代测试方法"
                    fi
                else
                    log_warn "无法自动安装sysbench，将使用替代测试方法"
                fi
            fi
        else
            [[ "$VERBOSE" == "true" ]] && log_info "✓ $tool 可用"
        fi
    done

    if [[ "$tools_missing" == "true" ]]; then
        log_error "缺少必需工具，无法继续测试"
        exit 1
    fi

    log_info "工具检查完成"
}

# CPU基准测试
run_cpu_test() {
    echo -e "${BLUE}==================== CPU基准测试 ====================${NC}"
    log_info "开始CPU性能测试 (时间: ${CPU_TIME}秒)"

    # 获取CPU信息
    log_info "收集CPU信息..."
    local cpu_info
    cpu_info=$(docker exec "$CONTAINER_NAME" bash -c "
        if [[ -f /proc/cpuinfo ]]; then
            echo \"CPU型号: \$(grep 'model name' /proc/cpuinfo | head -1 | cut -d: -f2 | xargs)\"
            echo \"CPU核数: \$(nproc)\"
            echo \"CPU架构: \$(uname -m)\"
            echo \"CPU频率: \$(grep 'cpu MHz' /proc/cpuinfo | head -1 | cut -d: -f2 | xargs) MHz\"
        fi
        echo \"负载平均: \$(uptime | awk -F'load average:' '{print \$2}')\"
    " 2>/dev/null)

    echo "$cpu_info"
    echo "$cpu_info" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    local cpu_cores
    cpu_cores=$(docker exec "$CONTAINER_NAME" nproc 2>/dev/null || echo "1")

    # 测试1: sysbench CPU测试（如果可用）
    if docker exec "$CONTAINER_NAME" which sysbench &> /dev/null; then
        log_info "运行sysbench CPU测试..."
        echo -e "${CYAN}测试1: sysbench CPU基准测试${NC}"

        local sysbench_result
        sysbench_result=$(docker exec "$CONTAINER_NAME" sysbench cpu \
            --cpu-max-prime=20000 \
            --threads="$cpu_cores" \
            --time="$CPU_TIME" \
            run 2>/dev/null)

        echo "$sysbench_result"
        echo "sysbench CPU测试结果:" >> "$REPORT_FILE"
        echo "$sysbench_result" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"

        # 提取关键指标
        local events_per_sec
        events_per_sec=$(echo "$sysbench_result" | grep "events per second:" | awk '{print $4}')
        if [[ -n "$events_per_sec" ]]; then
            log_result "CPU性能: $events_per_sec events/sec"
        fi
    else
        log_warn "sysbench不可用，使用替代CPU测试"
    fi

    # 测试2: 计算密集型测试（计算π）
    log_info "运行计算密集型测试..."
    echo -e "${CYAN}测试2: 计算密集型测试 (计算π)${NC}"

    local pi_test_result
    pi_test_result=$(docker exec "$CONTAINER_NAME" bash -c "
        start_time=\$(date +%s.%N)
        echo 'scale=5000; 4*a(1)' | bc -l > /dev/null
        end_time=\$(date +%s.%N)
        duration=\$(echo \"\$end_time - \$start_time\" | bc)
        echo \"计算π用时: \${duration} 秒\"
    " 2>/dev/null)

    echo "$pi_test_result"
    echo "计算密集型测试结果:" >> "$REPORT_FILE"
    echo "$pi_test_result" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    # 测试3: 多线程压力测试
    log_info "运行多线程压力测试..."
    echo -e "${CYAN}测试3: 多线程压力测试${NC}"

    local stress_result
    stress_result=$(docker exec "$CONTAINER_NAME" bash -c "
        start_time=\$(date +%s.%N)
        for i in \$(seq 1 $cpu_cores); do
            (
                count=0
                while [[ \$count -lt 1000000 ]]; do
                    count=\$((count + 1))
                done
            ) &
        done
        wait
        end_time=\$(date +%s.%N)
        duration=\$(echo \"\$end_time - \$start_time\" | bc)
        echo \"多线程计算用时: \${duration} 秒\"
        echo \"使用线程数: $cpu_cores\"
    " 2>/dev/null)

    echo "$stress_result"
    echo "多线程压力测试结果:" >> "$REPORT_FILE"
    echo "$stress_result" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    log_info "CPU测试完成"
    echo ""
}

# 磁盘IO基准测试
run_disk_test() {
    echo -e "${BLUE}==================== 磁盘IO基准测试 ====================${NC}"
    log_info "开始磁盘I/O性能测试 (大小: $DISK_SIZE, 路径: $DISK_PATH)"

    # 获取磁盘信息
    log_info "收集磁盘信息..."
    local disk_info
    disk_info=$(docker exec "$CONTAINER_NAME" bash -c "
        echo \"磁盘空间信息:\"
        df -h '$DISK_PATH'
        echo \"\"
        echo \"文件系统信息:\"
        mount | grep '\$(df '$DISK_PATH' | tail -1 | awk '{print \$1}')' || echo '无法获取挂载信息'
    " 2>/dev/null)

    echo "$disk_info"
    echo "$disk_info" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    # 确保测试目录存在且可写
    if ! docker exec "$CONTAINER_NAME" test -w "$DISK_PATH"; then
        log_error "测试路径 $DISK_PATH 不可写"
        return 1
    fi

    # 清理之前的测试文件
    docker exec "$CONTAINER_NAME" rm -f "${DISK_PATH}/testfile_*" "${DISK_PATH}/sysbench_test*" 2>/dev/null || true

    # 计算测试文件大小（转换为MB）
    local size_mb
    case "$DISK_SIZE" in
        *G) size_mb=$((${DISK_SIZE%G} * 1024)) ;;
        *M) size_mb=${DISK_SIZE%M} ;;
        *K) size_mb=$((${DISK_SIZE%K} / 1024)) ;;
        *) size_mb=$((DISK_SIZE / 1024 / 1024)) ;;
    esac

    # 测试1: 顺序写入测试
    log_info "运行顺序写入测试..."
    echo -e "${CYAN}测试1: 顺序写入测试${NC}"

    local write_result
    write_result=$(docker exec "$CONTAINER_NAME" bash -c "
        sync
        time_start=\$(date +%s.%N)
        dd if=/dev/zero of='${DISK_PATH}/testfile_write' bs=1M count=$size_mb oflag=direct 2>/dev/null
        sync
        time_end=\$(date +%s.%N)
        file_size=\$(stat -c%s '${DISK_PATH}/testfile_write' 2>/dev/null || echo 0)
        time_taken=\$(echo \"\$time_end - \$time_start\" | bc)
        if [[ \$time_taken > 0 ]]; then
            throughput=\$(echo \"scale=2; \$file_size / \$time_taken / 1024 / 1024\" | bc)
            echo \"顺序写入速度: \${throughput} MB/s\"
            echo \"文件大小: \$(echo \"scale=2; \$file_size / 1024 / 1024\" | bc) MB\"
            echo \"用时: \${time_taken} 秒\"
        else
            echo \"测试时间过短，无法计算准确速度\"
        fi
    " 2>/dev/null)

    echo "$write_result"
    echo "顺序写入测试结果:" >> "$REPORT_FILE"
    echo "$write_result" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    # 测试2: 顺序读取测试
    log_info "运行顺序读取测试..."
    echo -e "${CYAN}测试2: 顺序读取测试${NC}"

    local read_result
    read_result=$(docker exec "$CONTAINER_NAME" bash -c "
        sync
        echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
        time_start=\$(date +%s.%N)
        dd if='${DISK_PATH}/testfile_write' of=/dev/null bs=1M iflag=direct 2>/dev/null
        time_end=\$(date +%s.%N)
        file_size=\$(stat -c%s '${DISK_PATH}/testfile_write' 2>/dev/null || echo 0)
        time_taken=\$(echo \"\$time_end - \$time_start\" | bc)
        if [[ \$time_taken > 0 ]]; then
            throughput=\$(echo \"scale=2; \$file_size / \$time_taken / 1024 / 1024\" | bc)
            echo \"顺序读取速度: \${throughput} MB/s\"
            echo \"文件大小: \$(echo \"scale=2; \$file_size / 1024 / 1024\" | bc) MB\"
            echo \"用时: \${time_taken} 秒\"
        else
            echo \"测试时间过短，无法计算准确速度\"
        fi
    " 2>/dev/null)

    echo "$read_result"
    echo "顺序读取测试结果:" >> "$REPORT_FILE"
    echo "$read_result" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    # 测试3: 不同块大小的写入测试
    log_info "运行不同块大小的写入测试..."
    echo -e "${CYAN}测试3: 不同块大小写入性能${NC}"

    local block_sizes=("4K" "64K" "1M" "4M")
    for bs in "${block_sizes[@]}"; do
        local bs_result
        bs_result=$(docker exec "$CONTAINER_NAME" bash -c "
            test_size=\$((100 * 1024 * 1024))  # 100MB测试
            case '$bs' in
                4K) block_size=4096; count=\$((test_size / block_size)) ;;
                64K) block_size=65536; count=\$((test_size / block_size)) ;;
                1M) block_size=1048576; count=\$((test_size / block_size)) ;;
                4M) block_size=4194304; count=\$((test_size / block_size)) ;;
            esac

            sync
            time_start=\$(date +%s.%N)
            dd if=/dev/zero of='${DISK_PATH}/testfile_bs_$bs' bs=\$block_size count=\$count oflag=direct 2>/dev/null
            sync
            time_end=\$(date +%s.%N)

            file_size=\$(stat -c%s '${DISK_PATH}/testfile_bs_$bs' 2>/dev/null || echo 0)
            time_taken=\$(echo \"\$time_end - \$time_start\" | bc)
            if [[ \$time_taken > 0 ]]; then
                throughput=\$(echo \"scale=2; \$file_size / \$time_taken / 1024 / 1024\" | bc)
                echo \"块大小 $bs: \${throughput} MB/s\"
            else
                echo \"块大小 $bs: 测试时间过短\"
            fi
            rm -f '${DISK_PATH}/testfile_bs_$bs'
        " 2>/dev/null)

        echo "$bs_result"
        echo "$bs_result" >> "$REPORT_FILE"
    done
    echo "" >> "$REPORT_FILE"

    # 测试4: 随机I/O测试（使用sysbench，如果可用）
    if docker exec "$CONTAINER_NAME" which sysbench &> /dev/null; then
        log_info "运行sysbench随机I/O测试..."
        echo -e "${CYAN}测试4: sysbench随机I/O测试${NC}"

        local sysbench_result
        sysbench_result=$(docker exec "$CONTAINER_NAME" bash -c "
            cd '$DISK_PATH'
            sysbench fileio --file-total-size='$DISK_SIZE' --file-test-mode=rndrw --time=30 --max-requests=0 prepare > /dev/null 2>&1
            sysbench fileio --file-total-size='$DISK_SIZE' --file-test-mode=rndrw --time=30 --max-requests=0 run 2>/dev/null
            sysbench fileio --file-total-size='$DISK_SIZE' --file-test-mode=rndrw cleanup > /dev/null 2>&1
        " 2>/dev/null)

        echo "$sysbench_result"
        echo "sysbench随机I/O测试结果:" >> "$REPORT_FILE"
        echo "$sysbench_result" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    else
        log_warn "sysbench不可用，跳过随机I/O测试"
    fi

    # 清理测试文件
    docker exec "$CONTAINER_NAME" rm -f "${DISK_PATH}/testfile_*" "${DISK_PATH}/sysbench_test*" 2>/dev/null || true

    log_info "磁盘I/O测试完成"
    echo ""
}

# 生成性能分析报告
generate_analysis() {
    log_info "生成性能分析报告..."

    cat >> "$REPORT_FILE" << EOF

================================================================================
性能分析和建议
================================================================================

测试总结:
- 测试完成时间: $(date '+%Y-%m-%d %H:%M:%S')
- 容器名称: $CONTAINER_NAME
- 测试项目: CPU($CPU_TEST), 磁盘I/O($DISK_TEST)

cgroup性能对比建议:
1. CPU性能对比:
   - 比较不同cgroup版本下的CPU events/sec指标
   - 观察CPU限制是否生效
   - 检查多线程性能差异

2. I/O性能对比:
   - 比较顺序读写速度
   - 观察I/O限制是否生效
   - 检查不同块大小下的性能表现

3. 资源限制验证:
   - 确认设置的CPU/内存/I/O限制是否按预期工作
   - 比较cgroup v1和v2的限制精度

优化建议:
- 如果是cgroup v2环境，建议使用新的I/O限制机制
- 对于CPU密集型应用，关注CPU限制的精确性
- 对于I/O密集型应用，测试不同块大小的性能

注意事项:
- 测试结果可能受到宿主机负载影响
- 建议在相同条件下多次测试取平均值
- 不同存储后端可能影响I/O测试结果

================================================================================
EOF

    log_result "性能测试报告已保存到: $REPORT_FILE"
}

# 主程序
main() {
    echo -e "${BLUE}======================================================${NC}"
    echo -e "${BLUE}     Docker容器性能基准测试工具 (cgroup v1/v2)     ${NC}"
    echo -e "${BLUE}======================================================${NC}"
    echo ""

    # 初始化报告文件
    init_report

    # 验证环境
    validate_environment

    # 检查工具
    check_tools

    # 收集cgroup信息
    collect_cgroup_info

    # 获取容器基本信息
    log_info "收集容器基本信息..."
    local container_info
    container_info=$(docker inspect "$CONTAINER_NAME" --format "
镜像: {{.Config.Image}}
状态: {{.State.Status}}
启动时间: {{.State.StartedAt}}
平台: {{.Platform}}
工作目录: {{.Config.WorkingDir}}" 2>/dev/null)

    echo -e "${GREEN}容器信息:${NC}"
    echo "$container_info"
    echo "容器基本信息:" >> "$REPORT_FILE"
    echo "$container_info" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    # 记录测试开始时间
    local test_start_time
    test_start_time=$(date +%s)
    log_info "开始性能测试..."

    # 运行测试
    if [[ "$CPU_TEST" == "true" ]]; then
        run_cpu_test
    fi

    if [[ "$DISK_TEST" == "true" ]]; then
        run_disk_test
    fi

    # 计算总测试时间
    local test_end_time total_time
    test_end_time=$(date +%s)
    total_time=$((test_end_time - test_start_time))

    log_result "所有测试完成！总用时: ${total_time} 秒"

    # 生成分析报告
    generate_analysis

    echo ""
    echo -e "${GREEN}======================================================${NC}"
    echo -e "${GREEN}测试完成！报告文件: ${REPORT_FILE}${NC}"
    echo -e "${GREEN}======================================================${NC}"
}

# 运行主程序
main "$@"
