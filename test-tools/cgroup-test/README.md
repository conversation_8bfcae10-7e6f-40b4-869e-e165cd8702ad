# Cgroup性能测试工具

这个工具集用于测试和比较Docker容器在cgroup v1和v2环境下的性能差异。

## 文件说明

- `run_container.sh` - 创建带有资源限制的Docker容器
- `run_test.sh` - 在指定容器中运行性能测试
- `README.md` - 使用说明文档

## 快速开始

### 1. 创建测试容器

```bash
# 创建一个名为 "myapp" 的容器，会自动生成卷挂载路径
./run_container.sh -n myapp

# 查看帮助信息
./run_container.sh --help
```

### 2. 运行性能测试

```bash
# 运行所有测试（CPU + 磁盘I/O）
./run_test.sh myapp

# 只运行CPU测试
./run_test.sh -c myapp

# 只运行磁盘I/O测试，使用2G测试文件
./run_test.sh -d -s 2G myapp

# 详细模式，自定义报告文件
./run_test.sh -v -r /tmp/performance_report.txt myapp
```

## 测试项目

### CPU性能测试
- sysbench CPU基准测试
- 多线程计算密集型测试（计算π）
- 多线程压力测试
- CPU限制验证

### 磁盘I/O性能测试
- 顺序写入/读取性能测试
- 不同块大小的写入性能测试（4K, 64K, 1M, 4M）
- sysbench随机I/O测试
- I/O限制验证

## cgroup v1 vs v2 性能对比

### 测试流程建议

1. **准备两个环境**：
   - 一个使用cgroup v1的Docker环境
   - 一个使用cgroup v2的Docker环境

2. **创建相同配置的容器**：
   ```bash
   # 在cgroup v1环境
   ./run_container.sh -n test_v1
   
   # 在cgroup v2环境  
   ./run_container.sh -n test_v2
   ```

3. **运行相同的性能测试**：
   ```bash
   # 在两个环境中运行相同的测试
   ./run_test.sh -t 120 -s 1G test_v1
   ./run_test.sh -t 120 -s 1G test_v2
   ```

4. **对比测试结果**：
   - 比较CPU events/sec指标
   - 比较磁盘读写速度
   - 验证资源限制的精确性
   - 分析性能差异原因

### 关键对比指标

#### CPU性能
- **sysbench CPU events/sec**: 越高越好
- **计算π用时**: 越短越好
- **多线程计算用时**: 越短越好
- **CPU限制精确性**: 是否按预期限制

#### I/O性能
- **顺序读写速度**: MB/s，越高越好
- **不同块大小性能**: 观察性能曲线
- **随机I/O IOPS**: 越高越好
- **I/O限制精确性**: 是否按预期限制

## 配置说明

### run_container.sh 全局参数

```bash
CONTAINER_NAME="test"           # 容器名称
DEVICE="/dev/vdb"              # I/O限制的设备
WRITE_LIMIT="1048576"          # 写入限制 (1MB/s)
READ_LIMIT="10485760"          # 读取限制 (10MB/s)
CPU_LIMIT="4"                  # CPU限制 (4核)
MEMORY_LIMIT="8G"              # 内存限制 (8GB)
VOLUME_TEMPLATE="/mnt/container/docker_bind/{name}:/home"  # 卷挂载模板
IMAGE="registry.baidubce.com/scs_test_cce/mysql:v1.0"     # Docker镜像
```

### 自定义配置

可以通过修改脚本中的全局参数来调整资源限制：

```bash
# 修改CPU限制为2核
CPU_LIMIT="2"

# 修改内存限制为4GB
MEMORY_LIMIT="4G"

# 修改I/O限制为5MB/s写入，20MB/s读取
WRITE_LIMIT="5242880"   # 5MB/s
READ_LIMIT="20971520"   # 20MB/s

# 清空限制（无限制）
CPU_LIMIT=""
MEMORY_LIMIT=""
WRITE_LIMIT=""
READ_LIMIT=""
```

## 注意事项

1. **环境要求**：
   - Docker已安装并运行
   - 容器中需要有基本工具：dd, bc, nproc, stat
   - 建议安装sysbench以获得更全面的测试

2. **测试准确性**：
   - 建议在相同硬件环境下测试
   - 多次运行取平均值
   - 避免在高负载时测试

3. **存储要求**：
   - 确保测试路径有足够空间
   - 不同存储后端可能影响I/O测试结果

4. **权限要求**：
   - cgroup v2的I/O限制可能需要root权限
   - 确保Docker有足够权限访问cgroup

## 故障排除

### 常见问题

1. **容器创建失败**：
   - 检查Docker是否运行
   - 检查镜像是否存在
   - 检查卷挂载路径是否存在

2. **测试工具缺失**：
   - 脚本会自动尝试安装sysbench
   - 手动安装：`apt-get install sysbench bc`

3. **I/O限制不生效**：
   - 检查设备路径是否正确
   - cgroup v2环境下Docker的设备限制可能不工作
   - 查看脚本日志中的cgroup信息

4. **权限错误**：
   - 确保有Docker操作权限
   - cgroup v2可能需要root权限设置I/O限制

## 输出示例

测试完成后会生成详细的性能报告，包含：

- 系统和容器信息
- cgroup版本和限制配置
- 详细的测试结果
- 性能分析和优化建议

报告文件位置：`performance_report_YYYYMMDD_HHMMSS.txt`
