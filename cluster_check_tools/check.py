#!/usr/bin/env python3

"""
Author: wangyu92(<EMAIL>)
Date: 2022/10/17
"""


import os
import shlex
import subprocess
import re
import argparse
import json
import ipaddress

benchmark_default_params = {
    "requests": 100000,
    "clients": 50,
    "pipeline": 1,
    "keyspace": 100000,
    "tests": 'set,get',
    "payload": 1000
}

REDIS_BENCHMARK_BIN_PATH = ''   # set in init_check_environment
REDIS_CLI_BIN_PATH = ''


class ClusterAddress:
    def __init__(self, ip_and_port):
        self.ip, self.port = self.parse(ip_and_port)

    def parse(self, addr):
        items = addr.split(':')
        if len(items) != 2:
            raise ValueError('invalid address, expect format is IP:PORT, e.g., 127.0.0.1:6379')

        try:
            ipaddress.ip_address(items[0])
            ip = items[0]
        except:
            raise ValueError(f'invalid address: bad ip format')

        try:
            port = int(items[1])
            assert port > 0 and port < 65536
        except:
            raise ValueError('invalid address: port is out of range (0, 65536)')

        return ip, port

    def __str__(self):
        return f'{self.ip}:{self.port}'


def parse_benchmark_result(text):
    lines = text.strip().split('\n')
    
    columns = [col.strip('"') for col in lines[0].split(',')]
    records = []

    for line in lines[1:]:
        record = {}
        for i, field in enumerate(line.split(',')):
            v = field.strip('"')
            if re.match(r'\d+', v) is not None:
                v = float(v)
            record[columns[i]] = v
        records.append(record)

    return records


def benchmark(ip='127.0.0.1', port=6379, password=None,  clients=50, pipeline=1, keyspace=100000, tests='set,get', payload=32):
    cmd = f"""
        {REDIS_BENCHMARK_BIN_PATH} -h {ip} -p {port}
        -n {benchmark_default_params["requests"]}
        -r {benchmark_default_params['keyspace']}
        -c {benchmark_default_params['clients']}
        -t {benchmark_default_params['tests']}
        -P {benchmark_default_params['pipeline']}
        -d {benchmark_default_params['payload']}
        -q --csv
        """
    if len(password) > 0:
        cmd += f' -a {password}'

    args = shlex.split(cmd)
    proc = subprocess.Popen(args, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    outs, errs = proc.communicate(timeout=100)

    if len(errs) > 0:
        raise Exception("benchmark error: " + errs.decode("utf-8"))

    csv_content = outs.decode("utf-8")
    return parse_benchmark_result(csv_content)


def check_alive(ip='127.0.0.1', port=6379, password=None):
    cmd = f'{REDIS_CLI_BIN_PATH} -h {ip} -p {port}'
    if len(password) > 0:
        cmd += f' -a {password}'
    cmd += ' PING'

    args = shlex.split(cmd)
    proc = subprocess.Popen(args, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    outs, errs = proc.communicate(timeout=5)

    if len(errs) > 0:
        raise Exception(errs.decode("utf-8"))

    response = outs.decode("utf-8").strip()
    if response != 'PONG':
        raise Exception("unexpected response: " + response)


def format_success_response(benchmark_result):
    rsp = {
        'code': 0,
        'message': '',
        "commands-stat": [
            {
                'command': record['test'],
                'latency': {
                    'avg': record['avg_latency_ms'],
                    "min": record['min_latency_ms'],
                    "max": record['max_latency_ms'],
                    'p50': record['p50_latency_ms'],
                    "p95": record['p95_latency_ms'],
                    "p99": record['p99_latency_ms'],
                }
            } for record in benchmark_result
        ],
        'benchmark_params': benchmark_default_params
    }

    return json.dumps(rsp)


def format_error_response(error_message):
    return json.dumps({
        'code': 1,
        'message': error_message
    })


def init_check_environment():
    global REDIS_BENCHMARK_BIN_PATH
    global REDIS_CLI_BIN_PATH

    REDIS_BENCHMARK_BIN_PATH = os.getenv("REDIS_BENCHMARK_BIN_PATH")
    if REDIS_BENCHMARK_BIN_PATH == None:
        raise Exception("environment variable REDIS_BENCHMARK_BIN_PATH is unset, please set it as the absolute path of redis-benchmark")


    REDIS_CLI_BIN_PATH = os.getenv("REDIS_CLI_BIN_PATH")
    if REDIS_CLI_BIN_PATH == None:
        raise Exception("environment variable REDIS_CLI_BIN_PATH is unset, please set it as the absolute path of redis-cli")

def check(address: ClusterAddress, password=''):
    try:
        init_check_environment()
    except Exception as e:
        output = format_error_response(str(e))
        print(output)
        exit(1)

    ip, port = address.ip, address.port

    try:
        check_alive(ip=ip, port=port, password=password)
    except Exception as e:
        output = format_error_response(f"unable to connect cluster {address}")
        print(output)
        exit(1)

    try:
        benchmark_result = benchmark(ip=ip, port=port, password=password)
        output = format_success_response(benchmark_result)
        print(output)
    except Exception as e:
        error = str(e)
        output = format_error_response(f"benchmark failed: {error}")
        print(output)
        exit(1)


if __name__ == '__main__':

    node_type_list = [
        'redis.c2',
        'redis.c4',
        'pega.c2.cds',
        'pega.c4.cds',
        'pega.c8.cds',
        'pega.c16.cds'
    ]

    proxy_node_type_list = [
        'proxy.c1', 'proxy.c2'
    ]

    parser = argparse.ArgumentParser(description='SCS cluster checker.')
    parser.add_argument('--address', type=ClusterAddress, required=True, help='cluster endpoint')
    parser.add_argument('--password', type=str, default='', help='cluster password')
    parser.add_argument('--shard-count', type=int, required=True, help='shard count')
    parser.add_argument('--proxy-count', type=int, required=True, help='proxy count')
    parser.add_argument('--node-type', type=str, required=True, choices=node_type_list, help='backend node type')
    parser.add_argument('--proxy-node-type', type=str, required=True, choices=proxy_node_type_list, help='cluster shard count')
    args = parser.parse_args()

    check(args.address, args.password)
