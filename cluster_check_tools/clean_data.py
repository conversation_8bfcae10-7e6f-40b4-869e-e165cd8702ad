#!/usr/bin/env python3

"""
Author: ca<PERSON><PERSON><PERSON>(<EMAIL>)
Date: 2024/02/04
Description: Scan the data of each shard and find the key does not belong to the current shard.
"""

import argparse
import redis
import time
import logging

from binascii import crc_hqx
from loguru import logger

RENAMED_CONFIG_COMMAND = "aae420ac56ef116058218c11d8b35b30CONFIG"


class Instance:
    """
    Instance is a redis instance.
    """

    def __init__(self, shard_id, instance_id, ip, port, hashtag):
        ips = ip.split(",")
        if len(ips) == 0:
            raise ValueError("ip is empty")
        self.shard_id = shard_id
        self.id = instance_id
        self.ip = ips[0]
        self.port = port
        self.hashtag = hashtag
        self.redis_client = redis.Redis(host=self.ip, port=self.port)

    def __repr__(self):
        return f"{{shard: {self.shard_id}, id: {self.id}, addr: {self.ip}:{self.port}}}"

    def db_num(self):
        """
        Get the number of databases.
        """
        ret = self.redis_client.execute_command(RENAMED_CONFIG_COMMAND, "GET", "databases")
        if len(ret) < 2:
            raise ValueError("get db num failed")
        return int(ret[1].decode("utf-8"))

    def key_slot(self, key: bytes, bucket: int = 16384) -> int:
        """
        Get the key slot.
        """
        if self.hashtag:
            start = key.find(b"{")
            if start > -1:
                end = key.find(b"}", start + 1)
                if end > -1 and end > start + 1:
                    key = key[start + 1: end]

        return crc_hqx(key, 0) % bucket

    def scan_keys(self, db_id, slot_list, target_qps=-1, delete=False, proxy=None):
        """
        Scan the databases and find the keys does not belong to current shard.
        """
        self.redis_client.select(db_id)

        slots = {}
        for slot in slot_list:
            slots[slot] = True

        keys_num = 0
        del_num = 0
        last_del_time = time.time()
        last_del_num = 0

        cursor = '0'

        while cursor != 0:
            cursor, keys = self.redis_client.scan(cursor=cursor, count=100)
            keys_num += len(keys)

            pending_del_keys = []
            for key in keys:
                slot = self.key_slot(key)
                if slot not in slots:
                    try:
                        key = key.decode("utf-8")
                        pending_del_keys.append(key)
                    except Exception as e:
                        logger.error(f"decode key failed: {key}, exception: {e}, skip delete it")

            if len(pending_del_keys) == 0:
                continue

            if proxy is not None:
                proxy.check_keys_shard(pending_del_keys, self.shard_id)

            if delete:
                deleted = self.redis_client.delete(*pending_del_keys)
                logging.info(
                    f"shard: {self.shard_id}, instance_id: {self.id}, deleted: {deleted}, "
                    f"pending: {len(pending_del_keys)}, pending_del_keys: {pending_del_keys}")
            else:
                deleted = len(pending_del_keys)
                # logger.debug(f"pending_del_keys: {pending_del_keys}")

            del_num += deleted

            # rate limit
            now = time.time()
            diff = now - last_del_time
            qps = (del_num - last_del_num) / diff

            if 0 < target_qps <= qps:
                time.sleep(0.01)

            if now - last_del_time > 1:
                logger.info(f"Delete QPS: {qps}, del_num: {del_num}, last_del_num: {last_del_num}")
                last_del_time = now
                last_del_num = del_num

        if keys_num != 0:
            logger.info(
                f"id: {self.id}, db: {db_id}, keys_num: {keys_num}, "
                f"{'deleted' if delete else 'will delete'}: {del_num}")


class Proxy:
    """
    A proxy class to check the keys in cluster.
    """

    def __init__(self, ip, port, password):
        self.ip = ip
        self.port = port
        self.password = password
        self.redis_client = redis.Redis(host=self.ip, port=self.port, password=self.password)

    def check_keys_shard(self, keys: list, check_shard_id: int) -> bool:
        """
        Check the key distribution.
        """
        for key in keys:
            ret = self.redis_client.execute_command("PD", "KEY", key)
            if ret is None or len(ret) < 2:
                raise ValueError("proxy get shard id failed")

            shard_info = ret[1].decode("utf-8").split(":")

            shard_id = int(shard_info[1])

            if shard_id == check_shard_id:
                raise ValueError(f"{key} should not in {check_shard_id}")

        return True


class MetaServer:
    """
    A class to encapsulate the metaserver interface.
    """

    def __init__(self, ip, port, password):
        self.ip = ip
        self.port = port
        self.password = password
        self.redis_client = redis.Redis(host=self.ip, port=self.port, password=self.password)

    def mcget(self, cluster_id) -> dict:
        """
        Get the cluster info.
        """
        mcget_infos = self.redis_client.execute_command("mcget", cluster_id)
        if mcget_infos is None or len(mcget_infos) == 0:
            return {}
        mcget_infos_maps = {}

        for k, v in zip(mcget_infos[0::2], mcget_infos[1::2]):
            k = k.decode("utf-8")
            v = v.decode("utf-8")

            if k == "shard":
                v = [int(i) for i in v.split(',')]
            elif k == "slot":
                v = [int(i) for i in v.split(',')]

            mcget_infos_maps[k] = v

        return mcget_infos_maps

    def msget(self, cluster_id) -> dict:
        """
        Get the shard info.
        """
        msget_infos = self.redis_client.execute_command("msget", cluster_id)
        if msget_infos is None or len(msget_infos) == 0:
            return {}
        msget_infos_maps = {}
        for k, v in zip(msget_infos[0::2], msget_infos[1::2]):
            k = k.decode("utf-8")
            v = v.decode("utf-8")
            msget_infos_maps[k] = v
        return msget_infos_maps

    def mriget(self, instance_id) -> dict:
        """
        Get the server instance info.
        """
        mriget_infos = self.redis_client.execute_command("mriget", instance_id)
        if mriget_infos is None or len(mriget_infos) == 0:
            return {}
        mriget_infos_maps = {}
        for k, v in zip(mriget_infos[0::2], mriget_infos[1::2]):
            k = k.decode("utf-8")
            v = v.decode("utf-8")
            mriget_infos_maps[k] = v
        return mriget_infos_maps

    def get_shard_slots_dis(self, cluster_id) -> dict:
        """
        Get the shard slots distribution.
        """
        mcget_infos = self.mcget(cluster_id)
        slots = mcget_infos["slot"]
        shard_slots_map = {}

        for slot, shard in enumerate(slots):
            if shard not in shard_slots_map.keys():
                shard_slots_map[shard] = []
            shard_slots_map[shard].append(slot)

        return shard_slots_map


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Scan the cluster to find redundant data')
    parser.add_argument('--host', type=str, default='127.0.0.1', help='Metaserver Server Host(Default: 127.0.0.1)')
    parser.add_argument('--port', type=int, required=True, help='Metaserver Server Port')
    parser.add_argument('--password', type=str, default='', help='Metaserver Server Password(Default: empty)')
    parser.add_argument('--cluster', type=int, required=True, help='Cluster ID')
    parser.add_argument('--hashtag', action='store_true', help='Whether hashtag is configured')
    parser.add_argument('--qps', type=int, default=10000, help='QPS(Default: 10000)')
    parser.add_argument('--delete', action='store_true', help='Delete keys that do not belong to the shard')
    parser.add_argument('--proxy_host', type=str, default="",
                        help='Will use proxy PD command double check key distribution(Default: Empty)')
    parser.add_argument('--proxy_port', type=int, default=0, help='Proxy port')
    parser.add_argument('--proxy_password', type=str, default="", help='Proxy auth(Default: Empty)')

    args = parser.parse_args()

    metaserver_host = args.host
    metaserver_port = args.port
    metaserver_password = args.password
    cluster_id = args.cluster
    hashtag = args.hashtag
    qps = args.qps
    delete = args.delete

    proxy_host = args.proxy_host
    proxy_port = args.proxy_port
    proxy_password = args.proxy_password

    metaserver = MetaServer(metaserver_host, metaserver_port, metaserver_password)

    mcget_infos = metaserver.mcget(cluster_id)

    if len(mcget_infos) == 0:
        logger.error("Get empty cluster info from metaserver")
        exit(0)

    proxy = None

    if proxy_host != "" and proxy_port != 0:
        proxy = Proxy(proxy_host, proxy_port, proxy_password)

    shard_slots = metaserver.get_shard_slots_dis(cluster_id)
    shards = mcget_infos["shard"]

    logger.warning(f"Cluster: {cluster_id}, shards_num: {len(shards)}, shards: {shards}")

    if delete:
        # use to dump the key to be deleted.
        logging.basicConfig(level=logging.INFO, filename=f"./delete_keys_{cluster_id}.log", filemode='a',
                            format='%(asctime)s %(message)s')

    shard_master_map = {}

    for shard in shards:
        msget_infos = metaserver.msget(shard)
        master = msget_infos["master"]
        master_instance = metaserver.mriget(master)
        shard_master_map[shard] = Instance(shard, master, master_instance['ip'], master_instance['port'], hashtag)

    index = 1
    for shard, master in shard_master_map.items():
        dbnum = master.db_num()
        slot_list = shard_slots[shard]
        logger.warning(
            f"({index}/{len(shard_master_map)})Prepare scan{' and delete' if delete else ''}, "
            f"shard: {shard}, master: {master}, slots_num: {len(slot_list)}, db_num: {dbnum}")
        index += 1
        for i in range(dbnum):
            master.scan_keys(i, slot_list, qps, delete, proxy)
