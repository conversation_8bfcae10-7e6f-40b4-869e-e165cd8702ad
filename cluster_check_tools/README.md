# 集群可用性检查工具

集群部署完成后，执行此脚本对新部署的集群进行检查，以确保交付给客户正常工作的集群。


## 调用方式

此脚本依赖两个外部的工具 redis-benchmark、redis-cli 在执行 Python 脚本之前，需要先设置两个环境变量 `REDIS_BENCHMARK_BIN_PATH` 和 `REDIS_CLI_BIN_PATH`。

例如：

```bash
export REDIS_BENCHMARK_BIN_PATH="/home/<USER>/baidu/redis/src/redis-benchmark"
export REDIS_CLI_BIN_PATH="/home/<USER>/baidu/redis/src/redis-cli"

python3 ./check.py --address 127.0.0.1:6379 --shard-count 1 --proxy-count 1 --node-type redis.c2 --proxy-node-type proxy.c1
```

关于命令行参数的说明，请参考此文档: [SCS 交付验收工具](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/8oA-Om4f5IuMKk?source=1)
