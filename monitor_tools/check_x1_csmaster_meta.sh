#!/usr/bin/env bash

SQL='select t1.cache_instance_type, t2.role, t2.app_id, t2.node_id, t2.node_short_id from bce_scs.cache_instance t1, bce_scs_x1_task.node t2 where t1.id = t2.node_short_id and t1.cluster_id in (select id from cache_cluster where cache_cluster.status in (5,8,16,52,54,67) );'

MYSQL_CONF='/home/<USER>/scs/csmaster/conf/mysql.conf'
USER=`grep User $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`
PASSWORD=`grep Password $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`
HOST=`grep Ip $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`
PORT=`grep Port $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`

COUNT=`mysql -u$USER -p$PASSWORD -h$HOST -P$PORT -Nse "$SQL" 2>/dev/null | grep -v cache_instance_type | grep -E $'2\tmaster|3\tslave'|wc -l`
[[ x$1 == xreverse ]] && COUNT=`mysql -u$USER -p$PASSWORD -h$HOST -P$PORT -Nse "$SQL" 2>/dev/null | grep -v cache_instance_type | grep -E $'3\tmaster|2\tslave' | wc -l`
echo "x1_role_error_count:$COUNT"

if [[ x$1 == xdetail ]] ; then
    mysql -u$USER -p$PASSWORD -h$HOST -P$PORT -Nse "${SQL}" 2>/dev/null | grep -v cache_instance_type | grep -E $'2\tmaster|3\tslave'
fi
if [[ x$1 == xreverse ]] ; then
    mysql -u$USER -p$PASSWORD -h$HOST -P$PORT -Nse "${SQL}" 2>/dev/null | grep -v cache_instance_type | grep -E $'3\tmaster|2\tslave'
fi