#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
Check instance port alive.

作者: cuiyi01(<EMAIL>)
日期: Jun 8, 2020 at 4:31:14 PM
"""


import os
import pymysql
import socket
import threading


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = ':'.join(line_sp[1:]).strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_all_clusters():
    """get all clusters
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    ret = []
    with sql_conn.cursor() as cursor:
        cursor.execute("SELECT cache_cluster.id, cache_instance.floating_ip, cache_instance.port "
                       "FROM cache_cluster, cache_instance, userinfo "
                       "WHERE cache_cluster.status not in (0, 8, 9, 10, 12) "
                       "AND cache_instance.cache_instance_type in (3, 4)"
                       "AND cache_cluster.user_id = userinfo.id "
                       "AND cache_cluster.id = cache_instance.cluster_id "
                       "AND userinfo.iam_user_id not in ('4fa25b1e8fb64b06b472e49c876ce24a', 'a11602e1c4c24e59865b9bb9209ff16d', "
                       "'ac16fa60f92a4a699eef343bed08f7a4', '16e3892b19914630b49e47e02e820811', '3aa23ba0d8734fe5a77a4399401a916b') "
                       "ORDER BY cache_cluster.id ASC")
        sub_ret = dict()
        for row in cursor.fetchall():
            if "cluster_id" not in sub_ret:
                sub_ret["cluster_id"] = int(row[0])
                sub_ret["instances"] = [{"floating_ip": row[1], "port": int(row[2])}]
            elif sub_ret["cluster_id"] != row[0]:
                ret.append(sub_ret)
                sub_ret = dict()
                sub_ret["cluster_id"] = int(row[0])
                sub_ret["instances"] = [{"floating_ip": row[1], "port": int(row[2])}]
            else:
                sub_ret["instances"].append({"floating_ip": row[1], "port": int(row[2])})
        if sub_ret:
            ret.append(sub_ret)
    return ret


def socket_connect(ip, port, timeout=1):
    """socket connect
    """
    try:
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(timeout)
        client.connect((ip, port))
        client.close()
        return "ok"
    except Exception as e:
        return "error"


class Detector(threading.Thread):
    """
    Detector
    """
    def __init__(self, instances, retry_times=3, timeout=1):
        """init
        """
        self._instances = instances
        self._retry_times = 3
        self._timeout = timeout
        self._status = "ok"
        self._error_instances = []
        super(Detector, self).__init__()

    def run(self):
        """run
        """
        for instance_info in self._instances:
            floating_ip = instance_info["floating_ip"]
            port = instance_info["port"]
            status = "error"
            for i in range(self._retry_times):
                if socket_connect(floating_ip, port, self._timeout) == "ok":
                    status = "ok"
                    break
            if status != "ok":
                self._status = "error"
                self._error_instances.append((floating_ip, port))


def main():
    """main
    """
    all_clusters = get_all_clusters()
    threads = {}
    for cluster_info in all_clusters:
        threads[cluster_info["cluster_id"]] = Detector(cluster_info["instances"])
    for t in threads.values():
        t.setDaemon(True)
        t.start()
    for t in threads.values():
        t.join()
    ret = "redis_master_status:\""
    for cluster_id, t in threads.items():
        if t._status != "ok":
            ret += (str(cluster_id) + "_" + "[%s]"
                    % ",".join([str(x[0]) + "_" + str(x[1]) for x in t._error_instances]) + ";")
    if ret == "redis_master_status:\"":
        ret += "all_redis_up\""
    else:
        ret += "\""
    print ret


if __name__ == "__main__":
    main()