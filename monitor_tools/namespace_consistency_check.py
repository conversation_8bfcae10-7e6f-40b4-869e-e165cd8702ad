#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
Check instance namespace consistency.

作者: zhangxuepeng(<EMAIL>)
日期: Jun 8, 2020 at 4:31:14 PM
"""


import os
import pymysql
import socket
import threading
import redis


def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = ':'.join(line_sp[1:]).strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_all_clusters_ingested():
    """get all clusters ingested
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    namespace_ret = {}
    instance_ret = {}
    with sql_conn.cursor() as cursor:
        cursor.execute("select app_id, namespace, ns.status from cache_cluster cc, "
                       "bce_scs_x1_task.namespace ns where cc.cluster_show_id=ns.app_id and "
                       "cc.status not in (0, 10, 12)")
        for row in cursor.fetchall():
            if row[0] not in namespace_ret:
                namespace_ret[row[0]] = {row[1]: row[2]}
            else:
                namespace_ret[row[0]][row[1]] = row[2]
        cursor.execute("select app_id from bce_scs_x1_task.namespace_task where status='running'")
        for row in cursor.fetchall():
            if row[0] in namespace_ret:
                namespace_ret.pop(row[0])
        for app_id, namespaces in namespace_ret.items():
            if len(namespaces.keys()) < 2:
                namespace_ret.pop(app_id)
        if namespace_ret:
            format_strings = ",".join(['%s'] * len(namespace_ret.keys()))
            cursor.execute("select floating_ip, port, app_id from bce_scs_x1_task.node where app_id in (%s) "
                           "and status = 'inuse'" % format_strings, tuple(namespace_ret.keys()))
            for row in cursor.fetchall():
                if row[2] not in instance_ret:
                     instance_ret[row[2]] = [[row[0], row[1]],]
                else:
                    instance_ret[row[2]].append([row[0], row[1]])
    return namespace_ret, instance_ret


def get_namespace(ip, port, namespace_db, timeout):
    """get namespace"""
    redis_cli = redis.Redis(host=ip, port=port, socket_timeout=timeout)
    try:
        namespace_list = redis_cli.execute_command("namespace details")
    except redis.exceptions.ConnectionError:
        return 'ok'
    except redis.exceptions.TimeoutError:
        return 'ok'
    namespaces = {}
    for i in xrange(len(namespace_list)/2):
        if namespace_list[i * 2 + 1] == "default":
            status = "online"
        else:
            status = "offline"
        namespaces[namespace_list[i * 2]] = status
    for namespace, status in namespaces.items():
        if status != namespace_db.get(namespace, ""):
            if namespace == "__namespace" and status == "offline" and namespace_db.get(namespace, "") == "purged":
                continue
            else:
                return "error"
    for namespace, status in namespace_db.items():
        if status != "purged" and status != namespaces.get(namespace, ""):
            return "error"
    return 'ok'

class Detector(threading.Thread):
    """
    Detector
    """
    def __init__(self, namespace_db, instances, retry_times=3, timeout=1):
        """init
        """
        self.namespace_db = namespace_db
        self._instances = instances
        self._retry_times = 3
        self._timeout = timeout
        self._status = "ok"
        self._error_instances = []
        super(Detector, self).__init__()

    def run(self):
        """run
        """
        for instance_info in self._instances:
            floating_ip = instance_info[0]
            port = instance_info[1]
            status = "error"
            for i in range(self._retry_times):
                try:
                    if get_namespace(floating_ip, port, self.namespace_db, self._timeout) == "ok":
                        status = "ok"
                        break
                except:
                    continue
            if status != "ok":
                self._status = "error"
                self._error_instances.append((floating_ip, port))


def main():
    """main
    """
    cluster_namespaces, cluster_instances = get_all_clusters_ingested()
    threads = {}
    for cluster in cluster_namespaces:
        threads[cluster] = Detector(cluster_namespaces[cluster], cluster_instances[cluster])
    for t in threads.values():
        t.setDaemon(True)
        t.start()
    for t in threads.values():
        t.join()
    ret = "namespace_status:\""
    for cluster_id, t in threads.items():
        if t._status != "ok":
            ret += (str(cluster_id) + "_" + "[%s]"
                    % ",".join([str(x[0]) + "_" + str(x[1]) for x in t._error_instances]) + ";")
    if ret == "namespace_status:\"":
        ret += "all_namespace_match\""
    else:
        ret += "\""
    print ret


if __name__ == "__main__":
    main()