#!/usr/bin/env bash

BROKER_REDIS_HOST="127.0.0.1"
BROKER_REDIS_PORT="6379"
BROKER_REDIS_PASSWORD="x1taskredispppwwwxnxb8"
REDIS_CLI_PATH="/home/<USER>/scs/redis-cli"

USED_MEMORY=`$REDIS_CLI_PATH -h $BROKER_REDIS_HOST -p $BROKER_REDIS_PORT -a $BROKER_REDIS_PASSWORD info memory | grep 'used_memory:' | awk -F: '{print $2}' | tr -d '\r'`
MAX_MEMROY=`$REDIS_CLI_PATH -h $BROKER_REDIS_HOST -p $BROKER_REDIS_PORT -a $BROKER_REDIS_PASSWORD info memory | grep 'maxmemory:' | awk -F: '{print $2}' | tr -d '\r'`

USED_MEMORY_PERCENT=`echo "scale=2; $USED_MEMORY/$MAX_MEMROY*100" | bc`

echo "broker_redis_memory_percent:"$USED_MEMORY_PERCENT