#!../python27-gcc482/bin/python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# 更新node_show_id.

作者: zhangxuepeng(<EMAIL>)
"""


import argparse
import os
import sys
import time
import commands
import uuid
import logging
import logging.handlers
import pymysql
import requests
import json
import traceback
import commands
import commands
import datetime
import time

#OCTOPUS_PATH = "/home/<USER>/wg/scs-x1-7557/tools"
OCTOPUS_PATH = "/home/<USER>/scs/octopus"
#PYTHON_PAHT = "python"
PYTHON_PAHT = "../python27-gcc482/bin/python"
g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_log_path = os.path.join(g_script_path, "./log")
g_timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime(time.time()))
g_db_config = None

redis_bns_suffix = {
    "bjyz": "redisbjonline.BCE.all",
    "gzns": "redisgzonline.BCE.all",
    "szth": "redis.BCE.all",
    "bdbl": "redisbdblonline.BCE.all",
    "whgg": "rediswhggonline.BCE.all",
    "hkg03": "redishkgonline.BCE.all",
    "sin03": "redissinonline.BCE.all",
    "shwgq": "redisfshonline.BCE.all"
}

proxy_bns_suffix = {
    "bjyz": "proxybjonline.BCE.all",
    "gzns": "proxygzonline.BCE.all",
    "szth": "proxy.BCE.all",
    "bdbl": "proxybdblonline.BCE.all",
    "whgg": "proxywhggonline.BCE.all",
    "hkg03": "proxyhkgonline.BCE.all",
    "sin03": "proxysinonline.BCE.all",
    "shwgq": "proxyfshonline.BCE.all"
}

def get_json_config():
    """
    Get Json config
    """
    global g_db_config, g_script_path
    try:
        with open("%s/conf/db_config.json" % g_script_path, "rt") as f:
            g_db_config = json.loads(f.read())
    except Exception as e:
        logging.error("Load config faied, Errmsg: %s\n%s"
                      % (e.message, traceback.format_exc()))
        sys.exit(1)


def _get_db_config():
    """get db config according to idc
    """
    global g_db_config
    status, hostname = commands.getstatusoutput("hostname")
    if status != 0:
        logging.error("Cannot obtain hostname")
        sys.exit(1)
    idc = hostname.split("-")[0]
    if idc not in g_db_config:
        logging.error("db config of idc %s not exists" % idc)
        sys.exit(1)
    return g_db_config[idc]


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module

    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7

    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)

    dir = os.path.dirname(log_path)
    if not os.path.isdir(dir):
        os.makedirs(dir)

    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    streamHandler = logging.StreamHandler(sys.stdout)
    streamHandler.setFormatter(formatter)
    logger.addHandler(streamHandler)


class Octopus(object):
    """Octopus
    """

    def __init__(self):
        """init
        """
        pass

    def do(self, cmd, parameters):
        """do
        """
        #日志
        cmd = "cd {} && ./octopus {} {}".format(OCTOPUS_PATH, cmd, parameters)
        logging.info("octopus cmd: %s", cmd)
        status, output = commands.getstatusoutput(cmd)
        logging.info("octopus cmd return status:%d, output: %s", status, output)
        if status != 0:
            return False
        if output != "":
            return False
        return True

    def modify_instance_shard_id(self, cluster_id, uuid, shard_id):
        """modify_instance_shard_id
        """
        return self.do("modify_instance_model", "{} {} shard_id:{}".format(cluster_id, uuid, shard_id))

    def modify_node_show_id(self, cluster_id, uuid, node_show_id):
        """modify_node_show_id
        """
        return self.do("modify_instance_model", "{} {} node_show_id:{}".format(cluster_id, uuid, node_show_id))

    def modify_record_max_node_id(self, cluster_id, record_max_node_id):
        """modify_record_max_node_id
        """
        return self.do("modify_cluster_model", "{} record_max_node_id:{}".format(cluster_id, record_max_node_id))

class Monquery(object):
    """Monquery
    """

    def __init__(self):
        """init
        """
        pass

    def do(self, bns, timestamp):
        """do
        """
        cmd = " monquery -n {} -t instance -i monitor_error -s {}".format(bns, timestamp)
        logging.info("mon query cmd: %s", cmd)
        status, output = commands.getstatusoutput(cmd)
        logging.info("monquery cmd return status:%d, output: %s", status, output)
        return status, output

    def check_last_monitor_error(self, bns, value):
        """检查noah 监控数据
        """
        now = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        logging.info("Sleep 60 to get monitor error")
        for i in range(12):
            time.sleep(5)
            status, output = self.do(bns, now)
            if status != 0:
                return False
            lines = output.split('\n')
            if len(lines) < 2:
                continue
            if int(lines[-1].split()[-1]) > value:
                return False
            return True
        return False

    def get_bns(self, instance_primary_id, cluster_id, cache_instance_type):
        """获取bns
        """

        status, hostname = commands.getstatusoutput("hostname")
        if status != 0:
            logging.error("Cannot obtain hostname")
            sys.exit(1)
        idc = hostname.split("-")[0]
        if cache_instance_type == 0:
            return "{}.cluster-{}-{}".format(instance_primary_id, cluster_id, redis_bns_suffix[idc])
        else:
            return "{}.cluster-{}-{}".format(instance_primary_id, cluster_id, redis_bns_suffix[idc])

class RemoteTools(object):
    """RemoteTools
    """

    def __init__(self):
        """init
        """
        pass

    def do(self, cmd, package, instance_id):
        """do
        """
        #日志
        #cmd = "../python27-gcc482/bin/python remote_tools.py {} --tar_file_name={} -i {}".format(cmd,
        cmd = "{} remote_tools.py {} --tar_file_name={} -i {}".format(PYTHON_PAHT, cmd,
                                                                                            package,
                                                                                            instance_id,
                                                                                            )
        logging.info("remote tools cmd: %s", cmd)
        status, output = commands.getstatusoutput(cmd)
        logging.info("remote cmd return status:%d, output: %s", status, output)
        if status != 0:
            return False
        if 'False' in output:
            return False
        return True

    def precheck_python_env(self, instance_id):
        """do
        """
        #日志
        #cmd = "../python27-gcc482/bin/python remote_tools.py {} --tar_file_name={} -i {}".format(cmd,
        cmd = "{} remote_tools.py validate_python_env -i {}".format(PYTHON_PAHT, instance_id)
        logging.info("remote tools cmd: %s", cmd)
        status, output = commands.getstatusoutput(cmd)
        logging.info("remote cmd return status:%d, output: %s", status, output)
        if status != 0:
            return False
        if 'False' in output:
            return False
        return True

    def update_monitor_agent(self, instance_id):
        """更新monitor agent
        """
        return self.do("update_monitor_agent", "monitor_agent_1.0.35.1.tar.gz", instance_id)

    def update_csagent(self, instance_id):
        """更新csagent
        """
        return self.do("update_csagent", "csagent_3.6.20.2.tar.gz", instance_id)

    def update_agent(self, instance_id):
        """更新agent
        """
        return self.do("update_agent_python27", "agent_4.5.109.1.tar.gz", instance_id)

    def update_multi_agent(self, instance_id):
        """更新全部agent
        """
        if not self.update_csagent(instance_id):
            logging.error("update csagent with remote tools failed, instance primary_id: %s" % str(instance_id))
            return False
        if not self.update_agent(instance_id):
            logging.error("update agent with remote tools failed, instance primary_id: %s" % str(instance_id))
            return False
        # if not self.update_monitor_agent(instance_id):
        #     logging.error("update monitor agent with remote tools failed, instance primary_id: %s" % str(instance_id))
        #     return False
        return True


class DbHandler(object):
    """处理数据库请求
    """

    def __init__(self, host, port, user, password, db):
        """init
        """
        self._db_conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            db=db
        )
        self.cursor = self._db_conn.cursor()

    def get_x1_redis_node(self, cluster_id):
        """获取x1节点
        """
        instances = []
        #sql = ("select ci.shard_id, ci.cluster_id, cc.cluster_show_id, ci.uuid, node.node_fix_id, node.id, "
        #       "ci.cache_instance_type, ci.node_show_id, ci.id from cache_instance ci, "
        #       "cache_cluster cc, wg_scs_x1_task.node where ci.cluster_id=cc.id and ci.id=node.node_short_id and "
        #       "cc.status not in (10,12) and ci.cluster_id=%s")
        sql = ("select ci.shard_id, ci.cluster_id, cc.cluster_show_id, ci.uuid, node.node_fix_id, node.id, "
               "ci.cache_instance_type, ci.node_show_id, ci.id from cache_instance ci, "
               "cache_cluster cc, bce_scs_x1_task.node where ci.cluster_id=cc.id and ci.id=node.node_short_id and "
               "cc.status not in (10,12) and ci.cluster_id=%s")
        try:
            self.cursor.execute(sql, (cluster_id,))
            for row in self.cursor.fetchall():
                instances.append({
                    "shard_id": row[0],
                    "cluster_id": row[1],
                    "cluster_show_id": row[2],
                    "uuid": row[3],
                    "node_fix_id": row[4],
                    "node_primary_id": row[5],
                    "cache_instance_type": row[6],
                    "node_show_id": row[7],
                    "instance_primary_id": row[8]
                })
            self._db_conn.commit()
            return instances
        except Exception as e:
            logging.error("Get x1 redis node show id fail", exc_info=True)
            raise
        return []

    def get_x1_redis_proxy(self, cluster_id):
        """获取x1 proxy
        """
        instances = []
        #sql = ("select ci.shard_id, ci.cluster_id, cc.cluster_show_id, ci.uuid, proxy.node_fix_id, proxy.id, "
        #       "ci.cache_instance_type, ci.node_show_id, ci.id from cache_instance ci, "
        #       "cache_cluster cc, wg_scs_x1_task.proxy where ci.cluster_id=cc.id and ci.id=proxy.proxy_short_id and "
        #       "cc.status not in (10,12) and ci.cluster_id=%s")
        sql = ("select ci.shard_id, ci.cluster_id, cc.cluster_show_id, ci.uuid, proxy.node_fix_id, proxy.id, "
               "ci.cache_instance_type, ci.node_show_id, ci.id from cache_instance ci, "
               "cache_cluster cc, bce_scs_x1_task.proxy where ci.cluster_id=cc.id and ci.id=proxy.proxy_short_id and "
               "cc.status not in (10,12) and ci.cluster_id=%s")
        try:
            self.cursor.execute(sql, (cluster_id,))
            for row in self.cursor.fetchall():
                instances.append({
                    "shard_id": row[0],
                    "cluster_id": row[1],
                    "cluster_show_id": row[2],
                    "uuid": row[3],
                    "node_fix_id": row[4],
                    "node_primary_id": row[5],
                    "cache_instance_type": row[6],
                    "node_show_id": row[7],
                    "instance_primary_id": row[8]
                })
            self._db_conn.commit()
            return instances
        except Exception as e:
            logging.error("Get x1 redis node show id fail", exc_info=True)
            raise
        return []

    def update_x1_node_fix_id(self, node_fix_id, node_primary_id):
        """更新x1节点
        """
        #sql = "update wg_scs_x1_task.node set node_fix_id=%s where id=%s"
        sql = "update bce_scs_x1_task.node set node_fix_id=%s where id=%s"
        logging.info("Update x1 node sql: %s %s %s" % (sql, node_fix_id, node_primary_id))
        try:
            affected_rows = self.cursor.execute(sql, (node_fix_id, node_primary_id))
            if affected_rows != 1:
                logging.error("Update_node_fix_id, affect rows != 1")
                sys.exit(1)
            self._db_conn.commit()
        except Exception as e:
            logging.error("Update_node_fix_id Fail", exc_info=True)
            raise
        return True

    def get_csmaster_cache_instance(self, cluster_id):
        """获取csmaster 实例
        """
        instances = []
        sql = ("select id, shard_id, node_show_id, uuid, cache_instance_type from cache_instance where cluster_id=%s")
        try:
            self.cursor.execute(sql, (cluster_id,))
            for row in self.cursor.fetchall():
                instances.append({
                    "instance_primary_id": row[0],
                    "shard_id": row[1],
                    "node_show_id": row[2],
                    "uuid": row[3],
                    "cache_instance_type": row[4]
                })
            self._db_conn.commit()
            return instances
        except Exception as e:
            logging.error("Get x1 redis node show id fail", exc_info=True)
            raise
        return []

    def get_csmaster_cache_cluster(self, cluster_id):
        """获取csmaster集群
        """
        clusters = []
        sql = ("select record_max_node_id, status, cluster_show_id, version from cache_cluster where id=%s")
        try:
            self.cursor.execute(sql, (cluster_id,))
            for row in self.cursor.fetchall():
                clusters.append({
                    "record_max_node_id": row[0],
                    "status": row[1],
                    "cluster_show_id": row[2],
                    "version": row[3]
                })
            self._db_conn.commit()
            return clusters
        except Exception as e:
            logging.error("Get x1 redis node show id fail", exc_info=True)
            raise
        return []

    def get_csmaster_fill_info(self, cluster_id):
        """获取csmaster 填充shard id信息
        """
        instances = []
        sql = ("select cache_instance.cluster_id, cache_instance.uuid, cache_instance.shard_id origin_shard_id, "
               "a.shard_id target_shard_id, cache_instance_type, cache_instance.id from cache_instance,"
               "(select id shard_id,cluster_id c_id from cache_instance where cluster_id=%s "
               "and cache_instance_type in (3) and shard_id=0) a where cache_instance.cluster_id=a.c_id;")
        try:
            self.cursor.execute(sql, (cluster_id, ))
            for row in self.cursor.fetchall():
                instances.append({
                    "cluster_id": row[0],
                    "uuid": row[1],
                    "origin_shard_id": row[2],
                    "target_shard_id": row[3],
                    "cache_instance_type": row[4],
                    "cache_instance_primary_id": row[5]
                })
            self._db_conn.commit()
        except Exception as e:
            logging.error("Cannot get csmaster fill info", exc_info=True)
            raise
        return instances

    def get_x1_fill_info(self, cluster_id):
        """获取x1填充shard id 信息
        """
        x1_cluster_primary_ids = []
        sql = ("select bce_scs_x1_task.cluster.id, bce_scs_x1_task.cluster.cluster_short_id from cache_cluster cc,"
               "bce_scs_x1_task.cluster where cc.cluster_show_id=bce_scs_x1_task.cluster.app_id and cc.id=%s "
               "and bce_scs_x1_task.cluster.status='inuse'")
        #sql = ("select wg_scs_x1_task.cluster.id,  wg_scs_x1_task.cluster.cluster_short_id from cache_cluster cc,"
        #       " wg_scs_x1_task.cluster where cc.cluster_show_id=wg_scs_x1_task.cluster.app_id and cc.id=%s "
        #       "and wg_scs_x1_task.cluster.status='inuse'")
        try:
            self.cursor.execute(sql, (cluster_id, ))
            for row in self.cursor.fetchall():
                x1_cluster_primary_ids.append({
                    "cluster_primary_id": row[0],
                    "cluster_short_id": row[1]
                })
            self._db_conn.commit()
        except Exception as e:
            logging.error("Cannot get x1 fill info", exc_info=True)
            raise
        return x1_cluster_primary_ids

    def get_instances_shard_id(self, cluster_id):
        """获取实例shard id
        """
        shard_ids = []
        sql = "select shard_id from cache_instance where cluster_id=%s"
        try:
            self.cursor.execute(sql, (cluster_id, ))
            for row in self.cursor.fetchall():
                shard_ids.append({
                    "shard_id": row[0]
                })
            self._db_conn.commit()
        except Exception as e:
            logging.error("Cannot get instance shard id", exc_info=True)
            raise
        return shard_ids

    def update_cluster_short_id(self, shard_id, cluster_primary_id):
        """更新实例shard id
        """
        #sql = "update wg_scs_x1_task.cluster set cluster_short_id=%s where id=%s"
        sql = "update bce_scs_x1_task.cluster set cluster_short_id=%s where id=%s"
        logging.info("Update cluster short id, sql: %s, cluster_short_id: %s, id: %s" % (sql, shard_id,
                                                                                        cluster_primary_id))
        try:
            affected_rows = self.cursor.execute(sql, (shard_id, cluster_primary_id))
            if affected_rows != 1:
                logging.error("Update cluster short id, affect rows != 1")
                sys.exit(-1)
            self._db_conn.commit()
        except Exception as e:
            logging.error("Update cluster short id Fail", exc_info=True)
            raise
        return True

    def close(self):
        """close
        """
        try:
            self._db_conn.close()
        except Exception as e:
            logging.error("close db error")


def format_redis_node_show_id(cluster_show_id, shard_id, index):
    """format_redis_node_show_id
    """
    return "{}_redis_{}_{}".format(cluster_show_id, shard_id, index)


def format_proxy_node_show_id(cluster_show_id, index):
    """format_proxy_node_show_id
    """
    return "{}_proxy_{}".format(cluster_show_id, index)


def update_shard_id(cluster_id, db_handler):
    """更新shard id
    """
    octopus = Octopus()
    csmaster_infos = db_handler.get_csmaster_fill_info(cluster_id)
    for item in csmaster_infos:
        if item['origin_shard_id'] != 0:
            logging.error("origin_shard_id is not 0:%s" % (str(csmaster_infos))),
            sys.exit(-1)
    x1_infos = db_handler.get_x1_fill_info(cluster_id)
    if not x1_infos:
        logging.info("cluster:%s not migrate to x1" % cluster_id)
        migrate_x1_flag = False
    else:
        migrate_x1_flag = True
        for item in x1_infos:
            if item['cluster_short_id'] != 0:
                logging.error("cluster_short_id is not 0:%s" % (str(x1_infos))),
                sys.exit(-1)

    for instance in csmaster_infos:
        logging.info("cluster_id: {} uuid:{} cache_instance_type:{} cache_instance_primary_id:{} \
        shard_id:{} => {}".format(
            cluster_id,
            instance["uuid"],
            instance["cache_instance_type"],
            instance["cache_instance_primary_id"],
            instance["origin_shard_id"],
            instance["target_shard_id"]))
    continue_process = raw_input("Continue to process(Y/N):")
    if continue_process != "Y":
        logging.warning("Update shard id stop process for cluster_id: %s" % cluster_id)
        return False
    target_shard_id = -1
    for item in csmaster_infos:
        logging.info("begin modify instance shard with octopus: %s" % str(item))
        target_shard_id = item['target_shard_id']
        if not octopus.modify_instance_shard_id(item['cluster_id'], item['uuid'], item['target_shard_id']):
            logging.error("modify instance shard with octopus failed: %s" % str(item))
            sys.exit(-1)
        else:
            logging.info("modify instance shard with octopus success: %s" % str(item))

    time.sleep(1)
    instances = db_handler.get_instances_shard_id(cluster_id)
    for item in instances:
        if item['shard_id'] != target_shard_id:
            logging.error("shard_id not equal target_shard_id: %s" % str(item))
            sys.exit(-1)
    if migrate_x1_flag:
        for item in x1_infos:
            db_handler.update_cluster_short_id(target_shard_id, item['cluster_primary_id'])
    return True


class OpenRedis(object):
    """7001版本
    """

    def update_metadata_case_1_and_case_3(self, cluster_id, db_handler):
        """场景1、3元数据填充
        """
        logging.info("Open redis case 1 and 3 begin process for cluster_id: %s" % cluster_id)
        clusters = db_handler.get_csmaster_cache_cluster(cluster_id)
        if len(clusters) != 1:
            logging.error("Get cache cluster fail, count is gt 1: %s" % str(clusters))
            return False

        instances = db_handler.get_csmaster_cache_instance(cluster_id)
        if len(instances) < 1:
            logging.error("Get cache instance fail, count is lt 1: %s" % str(instances))
            return False
        shard_id_equal_zero = False

        remote_tools = RemoteTools()
        for instance in instances:
            if not remote_tools.precheck_python_env(instance['instance_primary_id']):
                logging.error("Python env precheck not pass: %s" % str(instance))
                return False
        for instance in instances:
            if instance["shard_id"] == 0:
                shard_id_equal_zero = True
                logging.info("Shard id is zero: %s" % str(instance))
        if shard_id_equal_zero and not update_shard_id(cluster_id, db_handler):
            logging.info("Update shard id fail: %s" % str(cluster_id))
            return False

        instances = db_handler.get_csmaster_cache_instance(cluster_id)
        for instance in instances:
            if instance['node_show_id'] == "":
                break
        else:
            logging.error("All cache instance has node show id: %s" % str(instances))
            return False

        current_record_max_node_id = clusters[0]['record_max_node_id']
        target_record_max_node_id = current_record_max_node_id
        for instance in instances:
            if instance["node_show_id"]:
                instance["target_node_show_id"] = instance["node_show_id"]
        for instance in instances:
            if not instance["node_show_id"] and instance["cache_instance_type"] == 3:
                instance["target_node_show_id"] = format_redis_node_show_id(clusters[0]['cluster_show_id'],
                                                                            instance['shard_id'],
                                                                            target_record_max_node_id)
                target_record_max_node_id += 1
        for instance in instances:
            if not instance["node_show_id"] and instance["cache_instance_type"] != 3:
                instance["target_node_show_id"] = format_redis_node_show_id(clusters[0]['cluster_show_id'],
                                                                            instance['shard_id'],
                                                                            target_record_max_node_id)
                target_record_max_node_id += 1

        logging.info("record_max_node_id: {} => {}".format(current_record_max_node_id, target_record_max_node_id))
        for instance in instances:
            if instance["target_node_show_id"] != instance["node_show_id"]:
                logging.info("cluster_show_id: {} uuid:{} cache_instance_type:{} shard_id:{} \
                node_show_id:{} => {}".format(
                    clusters[0]['cluster_show_id'],
                    instance["uuid"],
                    instance["cache_instance_type"],
                    instance["shard_id"],
                    instance["node_show_id"],
                    instance["target_node_show_id"]))
            else:
                logging.info(
                    "cluster_show_id:{} uuid:{} cache_instance_type:{} shard_id:{} \
                    node_show_id:{} => {}, skip...".format(
                        clusters[0]['cluster_show_id'],
                        instance["uuid"],
                        instance["cache_instance_type"],
                        instance["shard_id"],
                        instance["node_show_id"],
                        instance["target_node_show_id"]))
        continue_process = raw_input("Continue to process(Y/N):")
        if continue_process != "Y":
            logging.warning("Open redis case 1 and 3 stop process for cluster_id: %s" % cluster_id)
            return False
        octopus = Octopus()
        if not octopus.modify_record_max_node_id(cluster_id, target_record_max_node_id):
            logging.error("modify record_max_node_id with octopus failed: %s" % str(cluster_id))
            return False
        else:
            logging.info("modify record_max_node_id with octopus success: %s" % str(cluster_id))
        for instance in instances:
            if instance["node_show_id"] == instance["target_node_show_id"]:
                logging.info("instance has node_show_id, skip: %s" % str(instance))
                continue
            if not octopus.modify_node_show_id(cluster_id, instance['uuid'], instance['target_node_show_id']):
                logging.error("modify instance node show id with octopus failed: %s" % str(instance))
                return False
            else:
                logging.info("modify instance node show id with octopus success: %s" % str(instance))

        # 验证一下
        logging.info("Begin check metadata corrent")
        time.sleep(1)
        clusters = db_handler.get_csmaster_cache_cluster(cluster_id)
        if clusters[0]["record_max_node_id"] != target_record_max_node_id:
            logging.error("Cluster record_max_node_id is wrong, target: %s, now: %s" % (str(target_record_max_node_id),
                          str(clusters[0]["record_max_node_id"])))
            return False
        instances = db_handler.get_csmaster_cache_instance(cluster_id)
        for instance in instances:
            if instance["node_show_id"] == "":
                logging.error(
                    "Instance node show id is '': %s " % str(instance))
                return False
        return True

    def update_metadata_case_5(self, cluster_id, db_handler):
        """场景5元数据填充
        """
        logging.info("Open redis case 5 begin process for cluster_id: %s" % cluster_id)
        instances = db_handler.get_x1_redis_node(cluster_id)
        shard_id_equal_zero = False
        for instance in instances:
            if instance["shard_id"] == 0:
                shard_id_equal_zero = True
                logging.info("Shard id is zero: %s" % str(instance))
        if shard_id_equal_zero and not update_shard_id(cluster_id, db_handler):
            logging.info("Update shard id fail: %s" % str(cluster_id))
            return False

        instances = db_handler.get_x1_redis_node(cluster_id)
        remote_tools = RemoteTools()
        for instance in instances:
            if not remote_tools.precheck_python_env(instance['instance_primary_id']):
                logging.error("Python env precheck not pass: %s" % str(instance))
                return False
        for instance in instances:
            if instance['node_fix_id'].split('_')[2] != str(instance["shard_id"]):
                tmp = instance['node_fix_id'].split('_')
                tmp[2] = str(instance["shard_id"])
                instance['target_node_fix_id'] = "_".join(tmp)
            else:
                instance['target_node_fix_id'] = instance['node_fix_id']
        for instance in instances:
            if instance['target_node_fix_id'] != instance['node_fix_id']:
                logging.info(
                    "cluster_show_id:{} node_primary_id:{} cache_instance_type:{} shard_id:{} \
                    node_fix_id:{} => {}".format(
                        instance["cluster_show_id"],
                        instance["node_primary_id"],
                        instance["cache_instance_type"],
                        instance["shard_id"],
                        instance["node_fix_id"],
                        instance["target_node_fix_id"]))
            else:
                logging.info(
                    "cluster_show_id:{} node_primary_id:{} cache_instance_type:{} shard_id:{} \
                    node_fix_id:{} => {}, skip...".format(
                        instance["cluster_show_id"],
                        instance["node_primary_id"],
                        instance["cache_instance_type"],
                        instance["shard_id"],
                        instance["node_fix_id"],
                        instance["target_node_fix_id"]))
        continue_process = raw_input("Continue to process(Y/N):")
        if continue_process != "Y":
            logging.warning("Open redis case 5 stop process for cluster_id: %s" % cluster_id)
            return False
        for instance in instances:
            if instance['target_node_fix_id'] != instance['node_fix_id']:
                db_handler.update_x1_node_fix_id(instance['target_node_fix_id'], instance["node_primary_id"])

        logging.info("Begin update node show id")
        instances = db_handler.get_x1_redis_node(cluster_id)

        for instance in instances:
            if instance['node_show_id'] == instance['node_fix_id']:
                logging.info(
                    "cluster_show_id:{} uuid:{} cache_instance_type:{} shard_id:{} \
                    node_show_id:{} => {}, skip...".format(
                        instance["cluster_show_id"],
                        instance["uuid"],
                        instance["cache_instance_type"],
                        instance["shard_id"],
                        instance["node_show_id"],
                        instance["node_fix_id"]))
                continue
            logging.info("cluster_show_id:{} uuid:{} cache_instance_type:{} shard_id:{} \
            node_show_id:{} => {}".format(
                instance["cluster_show_id"],
                instance["uuid"],
                instance["cache_instance_type"],
                instance["shard_id"],
                instance["node_show_id"],
                instance["node_fix_id"]))
        continue_process = raw_input("Continue to process(Y/N):")
        if continue_process != "Y":
            logging.warning("Open redis case 5 stop process for cluster_id: %s" % cluster_id)
            return False
        octopus = Octopus()
        for instance in instances:
            if instance['node_show_id'] != instance['node_fix_id']:
                if not octopus.modify_node_show_id(instance['cluster_id'], instance['uuid'], instance['node_fix_id']):
                    logging.error("modify instance node show id with octopus failed: %s" % str(instance))
                    return False
                else:
                    logging.info("modify instance node show id with octopus success: %s" % str(instance))
        instances = db_handler.get_x1_redis_node(cluster_id)
        for instance in instances:
            if instance['node_show_id'] != instance['node_fix_id']:
                logging.error(
                    "Instance node show id not equal node fix id: %s " % str(instance))
                return False
        return True

    def update_agent(self, cluster_id, db_handler):
        """更新agent
        """
        clusters = db_handler.get_csmaster_cache_cluster(cluster_id)
        if len(clusters) != 1:
            logging.error("Get cache cluster fail, count is gt 1: %s" % str(clusters))
            return False

        instances = db_handler.get_csmaster_cache_instance(cluster_id)
        if len(instances) < 1:
            logging.error("Get cache instance fail, count is lt 1: %s" % str(instances))
            return False

        remote_tools = RemoteTools()
        mon_query = Monquery()
        for instance in instances:
            if instance["cache_instance_type"] == 3:
                continue
            if not remote_tools.update_multi_agent(instance["instance_primary_id"]):
                logging.error("update agents with remote tools failed: %s" % str(instance))
                return False
            else:
                logging.info("update agents with remote tools success: %s" % str(instance))
            if not mon_query.check_last_monitor_error(mon_query.get_bns(instance['instance_primary_id'],
                                                                    cluster_id, instance['cache_instance_type']), 5):
                logging.error("Check noah monitor data fail")
                sys.exit(-1)

        for instance in instances:
            if instance["cache_instance_type"] != 3:
                continue
            if not remote_tools.update_multi_agent(instance["instance_primary_id"]):
                logging.error("update agents with remote tools success: %s" % str(instance))
                return False
            else:
                logging.info("update agents with remote tools success: %s" % str(instance))
            if not mon_query.check_last_monitor_error(mon_query.get_bns(instance['instance_primary_id'],
                                                                    cluster_id, instance['cache_instance_type']), 5):
                logging.error("Check noah monitor data fail")
                sys.exit(-1)
        return True


class ClusterRedis(object):
    """5001版本
    """
    def update_metadata_case_1_and_case_3(self, cluster_id, db_handler):
        """场景1、3元数据填充
        """
        clusters = db_handler.get_csmaster_cache_cluster(cluster_id)
        if len(clusters) != 1:
            logging.error("Get cache cluster fail, count is gt 1: %s" % str(clusters))
            return False
        current_record_max_node_id = clusters[0]['record_max_node_id']
        if current_record_max_node_id != 0:
            logging.error("Record_max_node_id is not 0: %s" % str(clusters))
            return False

        instances = db_handler.get_csmaster_cache_instance(cluster_id)
        if len(instances) < 1:
            logging.error("Get cache instance fail, count is lt 1: %s" % str(instances))
            return False

        if len(instances) > 20:
            logging.error("Too many instance, count=%s" % str(len(instances)))
            return False

        shards = {}
        proxys = []
        nums = {}
        for instance in instances:
            if instance["node_show_id"] != "":
                logging.error("Instance node show id is not '' : %s" % str(instances))
                return False
            if instance['cache_instance_type'] == 0:
                proxys.append(instance)
                continue
            if instance['shard_id'] not in shards:
                shards[instance['shard_id']] = [instance, ]
                nums[instance['shard_id']] = 1
            else:
                shards[instance['shard_id']].append(instance)
                nums[instance['shard_id']] += 1

        if len(set(nums.values())) != 1:
            logging.error("Shards instance num is not same : %s" % str(nums))
            return False

        target_record_max_node_id = current_record_max_node_id + nums.values()[0]

        for i in range(len(proxys)):
            proxys[i]['target_node_show_id'] = format_proxy_node_show_id(clusters[0]['cluster_show_id'], i)

        for _, shard_instances in shards.items():
            index = 0
            for instance in shard_instances:
                if instance["cache_instance_type"] == 3:
                    instance["target_node_show_id"] = format_redis_node_show_id(clusters[0]['cluster_show_id'],
                                                                                instance['shard_id'], 0)
                    index += 1
            for instance in shard_instances:
                if instance["cache_instance_type"] == 2:
                    instance["target_node_show_id"] = format_redis_node_show_id(clusters[0]['cluster_show_id'],
                                                                                instance['shard_id'], index)
                    index += 1

        logging.info("record_max_node_id: {} => {}".format(current_record_max_node_id, target_record_max_node_id))
        for instance in proxys:
            logging.info("cluster_show_id: {} uuid:{} cache_instance_type:{} shard_id:{} \
            node_show_id:{} => {}".format(
                                                                                    clusters[0]['cluster_show_id'],
                                                                                    instance["uuid"],
                                                                                    instance["cache_instance_type"],
                                                                                    instance["shard_id"],
                                                                                    instance["node_show_id"],
                                                                                    instance["target_node_show_id"]))
        for _, shard_instances in shards.items():
            for instance in shard_instances:
                logging.info("cluster_show_id: {} uuid:{} cache_instance_type:{} shard_id:{} \
                node_show_id:{} => {}".format(
                                                                                    clusters[0]['cluster_show_id'],
                                                                                    instance["uuid"],
                                                                                    instance["cache_instance_type"],
                                                                                    instance["shard_id"],
                                                                                    instance["node_show_id"],
                                                                                    instance["target_node_show_id"]))
        continue_process = raw_input("Continue to process(Y/N):")
        if continue_process != "Y":
            logging.warning("Cluster redis case 5 stop process for cluster_id: %s" % cluster_id)
            return False
        octopus = Octopus()
        if not octopus.modify_record_max_node_id(cluster_id, target_record_max_node_id):
            logging.error("modify record_max_node_id with octopus failed: %s" % str(cluster_id))
            return False
        else:
            logging.info("modify record_max_node_id with octopus success: %s" % str(cluster_id))
        for instance in proxys:
            if not octopus.modify_node_show_id(cluster_id, instance['uuid'], instance['target_node_show_id']):
                logging.error("modify instance node show id with octopus failed: %s" % str(instance))
                return False
            else:
                logging.info("modify instance node show id with octopus success: %s" % str(instance))

        for instance in instances:
            if instance["node_show_id"] == instance["target_node_show_id"]:
                logging.info("instance has node_show_id, skip: %s" % str(instance))
                continue

        for _, shard_instances in shards.items():
            for instance in shard_instances:
                if not octopus.modify_node_show_id(cluster_id, instance['uuid'], instance['target_node_show_id']):
                    logging.error("modify instance node show id with octopus failed: %s" % str(instance))
                    return False
                else:
                    logging.info("modify instance node show id with octopus success: %s" % str(instance))

        return True

    def update_agent(self, cluster_id, db_handler):
        """update_agent
        """
        instances = db_handler.get_csmaster_cache_instance(cluster_id)
        if len(instances) < 1:
            logging.error("Get cache instance fail, count is lt 1: %s" % str(instances))
            return False

        remote_tools = RemoteTools()
        logging.info("Begine update agent for slave, cluster id :%s" % cluster_id)
        mon_query = Monquery()
        for instance in instances:
            if instance["cache_instance_type"] == 2:
                if not remote_tools.update_multi_agent(instance["instance_primary_id"]):
                    logging.error("update agents with remote tools success: %s" % str(instance))
                    return False
                else:
                    logging.info("update agents with remote tools success: %s" % str(instance))
                if not mon_query.check_last_monitor_error(mon_query.get_bns(instance['instance_primary_id'],
                                                                            cluster_id,
                                                                            instance['cache_instance_type']), 5):
                    logging.error("Check noah monitor data fail")
                    sys.exit(-1)
        for instance in instances:
            if instance["cache_instance_type"] == 3:
                if not remote_tools.update_multi_agent(instance["instance_primary_id"]):
                    logging.error("update agents with remote tools success: %s" % str(instance))
                    return False
                else:
                    logging.info("update agents with remote tools success: %s" % str(instance))
                if not mon_query.check_last_monitor_error(mon_query.get_bns(instance['instance_primary_id'],
                                                                            cluster_id,
                                                                            instance['cache_instance_type']), 5):
                    logging.error("Check noah monitor data fail")
                    sys.exit(-1)
        for instance in instances:
            if instance["cache_instance_type"] == 0:
                if not remote_tools.update_multi_agent(instance["instance_primary_id"]):
                    logging.error("update agents with remote tools success: %s" % str(instance))
                    return False
                else:
                    logging.info("update agents with remote tools success: %s" % str(instance))
                if not mon_query.check_last_monitor_error(mon_query.get_bns(instance['instance_primary_id'],
                                                                            cluster_id,
                                                                            instance['cache_instance_type']), 5):
                    logging.error("Check noah monitor data fail")
                    sys.exit(-1)
        return True


def main():
    """Generate parser
    """
    if len(sys.argv) != 3:
        print "update_node_show_id.py cluster_id action"
        sys.exit(1)
    cluster_id = int(sys.argv[1])
    action = sys.argv[2]
    if action not in ("update_metadata", "update_agent"):
        print "action must be (update_metadata, update_agent)"
        sys.exit(0)
    init_log(os.path.join(g_script_path, "log/update_node_show_id"),
             logging.DEBUG)
    logging.info("begin update node id for cluster: %s" % str(cluster_id))
    db_config = _get_db_config()
    db_handler = DbHandler(db_config["host"], db_config["port"], db_config["user"],
                           db_config["password"], db_config["db"])

    clusters = db_handler.get_csmaster_cache_cluster(cluster_id)
    success = False
    if len(clusters) != 1:
        print "Cluster ID is wrong"
        db_handler.close()
        sys.exit(1)
    if clusters[0]['status'] in (10, 12):
        print "Cluster is released"
        db_handler.close()
        sys.exit(1)
    if clusters[0]['version'] == 7001:
        open_redis = OpenRedis()
        if action == "update_agent":
            success = open_redis.update_agent(cluster_id, db_handler)
        else:
            x1_nodes = db_handler.get_x1_redis_node(cluster_id)
            if len(x1_nodes) == 0:
                success = open_redis.update_metadata_case_1_and_case_3(cluster_id, db_handler)
            else:
                success = open_redis.update_metadata_case_5(cluster_id, db_handler)
    elif clusters[0]['version'] == 5001:
        if len(db_handler.get_x1_redis_node(cluster_id)) > 0 or \
            len(db_handler.get_x1_redis_proxy(cluster_id)) > 0:
            print "Cluster migrate x1, not support"
            db_handler.close()
            sys.exit(1)
        cluster_redis = ClusterRedis()
        if action == "update_agent":
            success = cluster_redis.update_agent(cluster_id, db_handler)
        else:
            success = cluster_redis.update_metadata_case_1_and_case_3(cluster_id, db_handler)
    else:
        print "Cluster Version is wrong"
    db_handler.close()
    if success:
        print "################################"
        print "#SUCCESS                       #"
        print "################################"
        logging.info("Update node show id for cluster:  %s success" % str(cluster_id))
    else:
        logging.error("Update node show id for cluster:  %s fail" % str(cluster_id))

if __name__ == "__main__":
    get_json_config()
    main()