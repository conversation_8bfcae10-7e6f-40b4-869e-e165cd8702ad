#!/usr/bin/env bash
# 扫描 近180秒内 所有故障切换任务，按分片汇聚，输出任务数超过 5个 的分片mutex。
#   输出格式：
#       异常：x1_faulty_shards:n_scs-bj-aerdcjnxdycd122499;n_scs-bj-ajoqfovghekh77912
#       正常：x1_faulty_shards:OK
#   使用：
#       可通过 分片mutex 识别频繁切换的分片。 mutex格式：n_{cluster_show_id}_{shard_short_id} 或 c_{cluster_show_id}_{分片序号}，多个mutex使用';'分隔。
#       可通过 sh $0 detail 查看每个分片具体任务数量。
#       推荐告警配置 60秒检查一次，1/1即可报警；
#   说明：
#      扫描条件 “近180秒内、 超过5个”，主要识别 主节点上报心跳失败、夯住等场景；机器宕机、进程出core等场景，任务数通常不超过5个。



time_window_sec=180
task_count_threshold=5
SQL="select data.mutex, data.task_count from ( select tasks.mutex mutex, count(1) task_count from bce_scs_x1_task.tasks,bce_scs_x1_task.application where tasks.entity = application.app_id and tasks.status != 'fake' and tasks.created_at>date_sub(date_sub(now(),interval ${time_window_sec} second ),interval 8 hour) and work_flow in ('scs-standalone-failover','scs-shard-failover') and application.user_id not in('a11602e1c4c24e59865b9bb9209ff16d','4fa25b1e8fb64b06b472e49c876ce24a') group by tasks.mutex ) data where data.task_count >= ${task_count_threshold};"

MYSQL_CONF='/home/<USER>/scs/csmaster/conf/mysql.conf'
USER=`grep User $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`
PASSWORD=`grep Password $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`
HOST=`grep Ip $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`
PORT=`grep Port $MYSQL_CONF | head -1 | awk -F ': ' '{print $2}'`

faulty_shards=`mysql -u$USER -p$PASSWORD -h$HOST -P$PORT -e "${SQL}" 2>/dev/null | grep -v mutex | awk '{print $1}' | xargs -n1000 |  sed s/' '/';'/g`
if [[ "${faulty_shards}" == "" ]]; then
    faulty_shards="OK"
fi
[[ x$1 == xdetail ]] && mysql -u$USER -p$PASSWORD -h$HOST -P$PORT -e "${SQL}"

echo "x1_faulty_shards:\"$faulty_shards\""
