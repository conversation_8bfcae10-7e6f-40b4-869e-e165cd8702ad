#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
#Check migrate status
author: cuiyi01(<EMAIL>)
date: Jul 28, 2020 at 4:12:11 PM
"""
import json
import os
import pymysql
import time
def get_mysql_config():
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    if not os.path.isfile(mysql_conf):
        mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    ret["autocommit"] = True
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["password"] = ':'.join(line_sp[1:]).strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    return ret


def get_all_clusters():
    """get all clusters
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    ret = []
    with sql_conn.cursor() as cursor:
        sql = '''SELECT
                    t1.id,
                    t1.status,
                    t1.migration_status
                FROM
                    cache_cluster t1,
                    userinfo t2
                WHERE
                    t1.user_id = t2.id
                    AND t1.status NOT in (8, 10, 12)
                    AND t1.store_type NOT in (3)
                    AND t1.migration_status NOT in (0, 2)
                    AND t2.iam_user_id NOT in ('4fa25b1e8fb64b06b472e49c876ce24a', 'a11602e1c4c24e59865b9bb9209ff16d',
                    'ac16fa60f92a4a699eef343bed08f7a4', '16e3892b19914630b49e47e02e820811', '3aa23ba0d8734fe5a77a4399401a916b');
        '''
        cursor.execute(sql)
        for row in cursor.fetchall():
            ret.append({
                "id": int(row[0]),
                "status": int(row[1]),
                "migration_status": int(row[2])
            })
    return ret
def check_and_update_migration_records(records, cluster_id, migration_status):
    """check and update migration records
    """
    if migration_status == 1:
        if str(cluster_id) in records and records[str(cluster_id)]["migration_status"] == 1:
            if int(time.time()) - records[str(cluster_id)]["timestamp"] > 3600:
                return "error"
            else:
                pass
        else:
            records[cluster_id] = {
                "migration_status": 1,
                "timestamp": int(time.time())
            }
        return "ok"
    elif migration_status == 3:
        return "error"
    return "ok"
def execute():
    """execute
    """
    script_path = os.path.split(os.path.realpath(__file__))[0]
    records_path = os.path.join(script_path, "records.json")
    records = {}
    if os.path.isfile(records_path):
        with open(records_path, "rt") as f:
            records = json.load(f)
    clusters = get_all_clusters()
    err_clusters = []
    for cluster_info in clusters:
        if check_and_update_migration_records(records, cluster_info["id"], cluster_info["migration_status"]) != "ok":
            err_clusters.append(str(cluster_info["id"]))
    with open(records_path, "wt") as f:
        json.dump(records, f)
    if err_clusters:
        print "migration_status:%s" % "_".join(err_clusters)
    else:
        print "migration_status:ok"
if __name__ == "__main__":
    execute()
