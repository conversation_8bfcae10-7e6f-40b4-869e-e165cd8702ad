#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
#SCS可达性脚本监控

作者: cuiyi01(<EMAIL>)
日期: 2018年7月19日 下午1:14:38
"""

import commands
import logging
import logging.handlers
import json
import os
import pymysql
import redis
import sys
import time
import threading
import pdb

g_script_path = os.path.split(os.path.realpath(__file__))[0]
g_ignore_clusters = os.path.join(g_script_path, "./ignore_clusters")
g_meta_ignore_clusters = os.path.join(g_script_path, "./meta_ignore_clusters")
g_redis_cli = os.path.join(g_script_path, "./redis-cli")

g_check_interval = 60

g_retry_times = 3
g_retry_interval = 0.5

g_show_warn_times = 6

g_proxy_warn_rate = 0.2

g_max_master_not_up_time = 1800

g_idc_config = {
    "cq02": {
        "host": "***********",
        "port": 6202,
        "user": "bce_scs_w",
        "password": "3gj2OxM1OrfoR1bm",
        "db": "bce_scs",
        "meta_host_list": ["scsmetaserver.bj.baidubce.com"],
        "meta_port_list": [80],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    },
    "gzns": {
        "host": "*************",
        "port": 6001,
        "user": "bce_scs_gzns_w",
        "password": "PVrvns97CC7iQtKy",
        "db": "bce_scs",
        "meta_host_list": ["scsmetaserver.gz.baidubce.com"],
        "meta_port_list": [80],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    },
    "szth": {
        "host": "*************",
        "port": 6300,
        "user": "bce_scs_w",
        "password": "f_UJYZzr5du4iOsj",
        "db": "bce_scs",
        "meta_host_list": [],
        "meta_port_list": [],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    },
    "wh": {
        "host": "db.paas.whlocal.com",
        "port": 6203,
        "user": "paas_scs_w",
        "password": "paas_scs_w_pwd",
        "db": "paas_scs",
        "meta_host_list": [],
        "meta_port_list": [],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    },
    "bdbl": {
        "host": "*************",
        "port": 5254,
        "user": "bce_scs_w",
        "password": "IlUcU4hjCtbyOqOV4",
        "db": "bce_scs",
        "meta_host_list": ["scsmetaserver.bd.baidubce.com"],
        "meta_port_list": [80],
        "meta_is_auth": True,
        "meta_auth": "da0406a080b1cd600810ddf38ec77e4f"
    }
}


def safe_cast(val, to_type, default=None):
    """
    Safe casting
    """
    try:
        return to_type(val)
    except (ValueError, TypeError):
        return default


class WarningManager(object):
    """Warning manager
    """
    def __init__(self):
        """init
        """
        self._warning_data = dict()

    def add_warn(self, warn_type, cluster_id, short_message, message,
                 shard_info=None, inst_info=None):
        """add warn
        """
        self._warning_data.setdefault(cluster_id, []).append({
            "warn_type": warn_type,
            "short_message": short_message,
            "message": message,
            "shard_info": shard_info,
            "inst_info": inst_info,
            "cluster_id": cluster_id
        })

    def show_warn(self):
        """show warn
        """
        for cluster_id, warning_data_list in self._warning_data.items():
            for warning_data in warning_data_list:
                cluster_info_str = "Cluster id: %d" % warning_data["cluster_id"]
                if warning_data["shard_info"] is not None:
                    shard_info_str = "Shard info: %s " \
                                     % json.dumps(warning_data["shard_info"])
                else:
                    shard_info_str = ""
                if warning_data["inst_info"] is not None:
                    inst_info_str = "Instance info: %s " \
                                     % json.dumps(warning_data["inst_info"])
                else:
                    inst_info_str = ""
                logging.warn("{type}: {message}; {cluster_info}{shard_info}{inst_info}"
                             .format(type=warning_data["warn_type"], message=warning_data["message"],
                                     cluster_info=cluster_info_str, shard_info=shard_info_str,
                                     inst_info=inst_info_str))
        self._warning_data = dict()


g_warning_manager = WarningManager()


class ReachableWarningInfo(object):
    """
    Store ping wrong object
    """

    def __init__(self):
        """
        init
        """
        self._proxy_lock = threading.Lock()
        self._master_lock = threading.Lock()
        self._proxy_info = dict()
        self._master_info = dict()

    def add_info(self, type, info):
        """
        add proxy or master info
        :param type:
        :param info:
        :return:
        """
        if type == "proxy":
            self._proxy_lock.acquire()
            if info and isinstance(info, list):
                if info[0]["cluster_id"] not in self._proxy_info:
                    self._proxy_info[info[0]["cluster_id"]] = {"info": info, "times": 1, "flag": True}
                else:
                    self._proxy_info[info[0]["cluster_id"]]["info"] = info
                    self._proxy_info[info[0]["cluster_id"]]["times"] += 1
                    self._proxy_info[info[0]["cluster_id"]]["flag"] = True
            self._proxy_lock.release()
        elif type == "master":
            self._master_lock.acquire()
            if info and isinstance(info, dict):
                if info["uuid"] not in self._master_info:
                    self._master_info[info["uuid"]] = {"info": info, "times": 1, "flag": True}
                else:
                    self._master_info[info["uuid"]]["info"] = info
                    self._master_info[info["uuid"]]["times"] += 1
                    self._master_info[info["uuid"]]["flag"] = True
            self._master_lock.release()

    def reduce_times(self):
        """reduce times when no warn
        """
        self._proxy_lock.acquire()
        for key in self._proxy_info:
            if (not self._proxy_info[key]["flag"]) and self._proxy_info[key]["times"] > 0:
                self._proxy_info[key]["times"] = 0
            elif self._proxy_info[key]["flag"]:
                self._proxy_info[key]["flag"] = False
        self._proxy_lock.release()
        self._master_lock.acquire()
        for key in self._master_info:
            if (not self._master_info[key]["flag"]) and self._master_info[key]["times"] > 0:
                self._master_info[key]["times"] = 0
            elif self._master_info[key]["flag"]:
                self._master_info[key]["flag"] = False
        self._master_lock.release()

    def show_warning(self):
        """
        Print warn when times >= show_warn_times
        :return:
        """
        global g_show_warn_times, g_proxy_warn_rate, g_warning_manager
        self._proxy_lock.acquire()
        for key in self._proxy_info:
            if self._proxy_info[key]["times"] >= g_show_warn_times:
                info = self._proxy_info[key]["info"]
                # logging.warn("Proxy of cluster %s unreachable over %.2f; unreachable proxy list is %s."
                #              % (str(info[0]["cluster_id"]), g_proxy_warn_rate,
                #                 ", ".join([str(x) for x in info])))
                g_warning_manager.add_warn("UNREACHERR", info["cluster_id"],
                                           "Proxy unreachable",
                                           "Proxys unreachable over %.2f" % g_proxy_warn_rate,
                                           shard_info=None, inst_info=info)
        self._proxy_lock.release()
        self._master_lock.acquire()
        for key in self._master_info:
            if self._master_info[key]["times"] >= g_show_warn_times:
                info = self._master_info[key]["info"]
                # logging.warn("Master redis of cluster %s is unreachable; master info is %s"
                #              % (str(info["cluster_id"]), str(info)))
                g_warning_manager.add_warn("UNREACHERR", info["cluster_id"],
                                           "Master redis unreachable",
                                           "Master redis unreachable",
                                           shard_info=None, inst_info=info)
        self._master_lock.release()


g_warn_info = ReachableWarningInfo()


def _get_idc_config():
    """get db config according to idc
    """
    global g_idc_config
    status, hostname = commands.getstatusoutput("hostname")
    if status != 0:
        logging.error("Cannot obtain hostname")
        sys.exit(1)
    idc = hostname.split("-")[0]
    if idc not in g_idc_config:
        logging.error("db config of idc %s not exists" % idc)
        sys.exit(1)
    return g_idc_config[idc]


def init_log(log_path, level=logging.INFO, when="D", backup=7,
             format="%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s",
             datefmt="%m-%d %H:%M:%S"):
    """
    init_log - initialize log module

    Args:
      log_path      - Log file path prefix.
                      Log data will go to two files: log_path.log and log_path.log.wf
                      Any non-exist parent directories will be created automatically
      level         - msg above the level will be displayed
                      DEBUG < INFO < WARNING < ERROR < CRITICAL
                      the default value is logging.INFO
      when          - how to split the log file by time interval
                      'S' : Seconds
                      'M' : Minutes
                      'H' : Hours
                      'D' : Days
                      'W' : Week day
                      default value: 'D'
      format        - format of the log
                      default format:
                      %(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s
                      INFO: 12-09 18:02:42: log.py:40 * 139814749787872 HELLO WORLD
      backup        - how many backup file to keep
                      default value: 7

    Raises:
        OSError: fail to create log directories
        IOError: fail to open log file
    """
    formatter = logging.Formatter(format, datefmt)
    logger = logging.getLogger()
    logger.setLevel(level)

    dir = os.path.dirname(log_path)
    if not os.path.isdir(dir):
        os.makedirs(dir)

    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(level)
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    handler = logging.handlers.TimedRotatingFileHandler(log_path + ".log.wf",
                                                        when=when,
                                                        backupCount=backup)
    handler.setLevel(logging.WARNING)
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def redis_ping(host, port, uuid, cluster_id, socket_connect_timeout=0.5, socket_timeout=0.5, password=None):
    """ping redis
    """
    try:
        if password:
            redis_conn = redis.StrictRedis(
                host=host,
                port=port,
                socket_connect_timeout=socket_connect_timeout,
                socket_timeout=socket_timeout, password=password)
        else:
            redis_conn = redis.StrictRedis(
                host=host,
                port=port,
                socket_connect_timeout=socket_connect_timeout,
                socket_timeout=socket_timeout)
        if redis_conn.ping():
            logging.debug("Ping to %s in cluster %s returns PONG"
                          % (uuid, str(cluster_id)))
            return True
        else:
            logging.info("Ping to %s in cluster %s returns wrong"
                         % (uuid, str(cluster_id)))
            return False
    except Exception as e:
        logging.info("Ping to %s in cluster %s returns wrong"
                     % (uuid, str(cluster_id)))
        if e.message.startswith("NOAUTH"):
            return True
        return False


def redis_info(host, port, section=None,
               socket_connect_timeout=0.5, socket_timeout=0.5):
    """get redis info
    """
    try:
        redis_conn = redis.StrictRedis(
            host=host,
            port=port,
            socket_connect_timeout=socket_connect_timeout,
            socket_timeout=socket_timeout)
        return redis_conn.info(section)
    except Exception as e:
        logging.info("Get redis %s:%s info failed; Errmsg: %s" % (str(host), str(port), e.message))
        return None


def is_type_match_role(meta_type, role):
    """Is Metaserver type match role
    """
    if (meta_type == 1 and role == "master") \
            or (meta_type == 2 and role == "slave"):
        return True
    else:
        return False


def get_ignore_cluster():
    """get ignore cluster
    """
    if not os.path.isfile(g_ignore_clusters):
        return set()
    global g_ignore_clusters
    with open(g_ignore_clusters, "rt") as f:
        return set([int(x.strip()) for x in f.readlines()])


def get_meta_ignore_cluster():
    """Get meta ignore cluster
    """
    global g_meta_ignore_clusters
    if not os.path.isfile(g_meta_ignore_clusters):
        return set()
    with open(g_meta_ignore_clusters, "rt") as f:
        return set([int(x.strip()) for x in f.readlines()])


class DbHandler(object):
    """处理数据库请求
    """

    def __init__(self, host, port, user, password, db):
        """init
        """
        self._db_conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            db=db
        )

    def get_all_cluster_ids(self):
        """get all cluster ids
        """
        cluster_ids = []
        sql = ("select id from cache_cluster where status not in (10, 12, 8) and version in (5001, 7001)"
               " and master_domain = ''")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql)
                for row in cursor.fetchall():
                    cluster_ids.append(row[0])
        except Exception as e:
            logging.exception("Cannot get db")
            return None
        return cluster_ids

    def get_all_salve_cluster_ids(self):
        """get all slave cluster ids
        """
        cluster_ids = []
        sql = ("select id from cache_cluster where status not in (10, 12, 8) and version in (5001, 7001)"
               " and master_domain != ''")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql)
                for row in cursor.fetchall():
                    cluster_ids.append(row[0])
        except Exception as e:
            logging.exception("Cannot get db")
            return None
        return cluster_ids

    def get_all_cluster_info(self, version):
        """get all cluster info
        """
        cluster_infos = []
        sql = ("select id, status, cluster_show_id"
               " from cache_cluster where status not in (10, 12, 8)"
               " and version = %s"
               " and master_domain = ''")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql, (version,))
                for row in cursor.fetchall():
                    cluster_infos.append({
                        "id": int(row[0]),
                        "status": int(row[1]),
                        "cluster_show_id": row[2]
                    })
        except Exception as e:
            logging.exception("Cannot get db")
            return None
        return cluster_infos

    def get_all_slave_cluster_info(self):
        """get all slave cluster info
        """
        cluster_infos = []
        sql = ("select id, status, cluster_show_id"
               " from cache_cluster where status not in (10, 12, 8)"
               " and version = 5001"
               " and master_domain != ''")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql)
                for row in cursor.fetchall():
                    cluster_infos.append({
                        "id": int(row[0]),
                        "status": int(row[1]),
                        "cluster_show_id": row[2]
                    })
        except Exception as e:
            logging.exception("Cannot get db")
            return None
        return cluster_infos

    def get_cluster_info(self, cluster_id):
        """get cluster info
        """
        sql = ("select id, status, cluster_show_id, redis_auth, client_auth"
               " from cache_cluster where id = %s")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql, (cluster_id, ))
                for row in cursor.fetchall():
                    return {
                        "id": int(row[0]),
                        "status": int(row[1]),
                        "cluster_show_id": row[2],
                        "redis_auth": row[3],
                        "client_auth": row[4]
                    }
        except Exception as e:
            logging.exception("Cannot get db")
            return None

    def get_all_insts_info(self, cluster_id, instance_type):
        """get all insts info
        """

        cluster_info = self.get_cluster_info(cluster_id)
        insts_info = []
        sql = ("select id, cluster_id, uuid, fix_ip, floating_ip, port, password, shard_id "
               "from cache_instance where cluster_id = %s and cache_instance_type = %s")
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql, (cluster_id, instance_type))
                for row in cursor.fetchall():
                    insts_info.append({
                        "id": int(row[0]),
                        "cluster_id": int(row[1]),
                        "uuid": row[2],
                        "fix_ip": row[3],
                        "floating_ip": row[4],
                        "port": int(row[5]),
                        "password": row[6],
                        "shard_id": int(row[7]),
                        "redis_auth": cluster_info["redis_auth"],
                        "client_auth": cluster_info["client_auth"]
                    })
        except Exception as e:
            logging.exception("Cannot get db")
            return None
        return insts_info

    def get_all_shards(self, cluster_id):
        """Get all shards
        """
        result = []
        master_infos = self.get_all_insts_info(cluster_id, 3)
        slave_infos = self.get_all_insts_info(cluster_id, 2)
        for master_info in master_infos:
            sub_result = dict()
            sub_result["shard_id"] = master_info["shard_id"]
            sub_result["master_inst"] = master_info
            for slave_info in slave_infos:
                if slave_info["shard_id"] == sub_result["shard_id"]:
                    sub_result["slave_inst"] = slave_info
                    break
            result.append(sub_result)
        return result

    def get_all_unnormal_clusters(self):
        """
        """
        cluster_ids = []
        sql = "select id from cache_cluster where status not in (5, 10, 12, 8, 16)"
        try:
            with self._db_conn.cursor() as cursor:
                cursor.execute(sql)
                for row in cursor.fetchall():
                    cluster_ids.append(row[0])
        except Exception as e:
            logging.exception("Cannot get db")
            return None
        return cluster_ids

    def close(self):
        """close
        """
        try:
            self._db_conn.close()
        except Exception as e:
            logging.exception("close db error")


class ReachableDetector(threading.Thread):
    """Reachable monitor thread
    """

    def __init__(self, cluster_id, db_handler, proxy_warnning_rate=0.2, need_check_slave=False):
        """init
        """
        self._cluster_id = cluster_id
        self._unreachable_list = []
        self._proxy_warnning_rate = proxy_warnning_rate
        self._db_handler = db_handler
        self._need_check_slave = need_check_slave
        super(ReachableDetector, self).__init__()

    def run(self):
        """Do reachable detection
        """
        global g_retry_interval, g_retry_times, g_warn_info
        proxy_infos = self._db_handler.get_all_insts_info(self._cluster_id, 0)
        if proxy_infos is not None and len(proxy_infos) > 0:
            for _ in range(g_retry_times):
                for proxy_info in proxy_infos:
                    if not redis_ping(host=proxy_info["floating_ip"], port=proxy_info["port"],
                                      uuid=proxy_info["uuid"], cluster_id=proxy_info["cluster_id"],
                                      password=None if proxy_info["client_auth"] == "" else proxy_info["client_auth"]):
                        self._unreachable_list.append(proxy_info)
                if (len(self._unreachable_list) * 1.0) / len(proxy_infos) < self._proxy_warnning_rate:
                    break
                time.sleep(g_retry_interval)
            else:
                g_warn_info.add_info("proxy", self._unreachable_list)
                logging.info("Proxy of cluster %s unreachable over %.2f; unreachable proxy list is %s."
                             % (str(proxy_info["cluster_id"]), self._proxy_warnning_rate,
                                ", ".join([str(x) for x in self._unreachable_list])))
        master_infos = self._db_handler.get_all_insts_info(self._cluster_id, 3)
        if self._need_check_slave:
            slave_infos = self._db_handler.get_all_insts_info(self._cluster_id, 2)
            master_infos.extend(slave_infos)
        if master_infos is not None:
            for master_info in master_infos:
                for _ in range(g_retry_times):
                    if redis_ping(host=master_info["floating_ip"], port=master_info["port"],
                                  uuid=master_info["uuid"], cluster_id=master_info["cluster_id"],
                                  password=None if master_info["redis_auth"] == "" else master_info["redis_auth"]):
                        break
                    time.sleep(g_retry_interval)
                else:
                    g_warn_info.add_info("master", master_info)
                    logging.info("Master redis of cluster %s is unreachable; master info is %s"
                                 % (str(master_info["cluster_id"]), str(master_info)))


class ClusterStatusDetector(object):
    """Cluster Status Detector
    """

    def __init__(self, db_handler):
        """init
        """
        self._unnormal_map = dict()
        self._db_handler = db_handler

    def update_db_handler(self, db_handler):
        """update db handler
        """
        self._db_handler = db_handler

    def check_unnormal(self, ignore_clusters):
        """check unnormal
        """
        tmp_dict = self._unnormal_map
        self._unnormal_map = dict()
        cluster_ids = self._db_handler.get_all_unnormal_clusters()
        if cluster_ids:
            for cluster_id in cluster_ids:
                if cluster_id in tmp_dict and cluster_id not in ignore_clusters:
                    self._unnormal_map[cluster_id] = tmp_dict[cluster_id]
                    self._unnormal_map[cluster_id]["now_time"] = time.time()
                else:
                    self._unnormal_map[cluster_id] = {"first_time": time.time(), "now_time": time.time()}
        for cluster_id, info in self._unnormal_map.items():
            if (info["now_time"] - info["first_time"]) > 1200:
                logging.warn("Cluster %s status unnormal." % str(cluster_id))


class MetaServerHandler(object):
    """Get info from metaserver
    """

    def __init__(self, host_list, port_list, is_auth, auth, timeout=1):
        """init
        """
        if len(host_list) != len(port_list):
            raise ValueError("The length of host list and port list must be equal.")
        self._host_list = host_list
        self._port_list = port_list
        self._master_host = None
        self._master_port = None
        self._is_auth = is_auth
        self._auth = auth
        self._cmd_pre = None
        self._timeout = timeout
        self._get_master_conn()

    def _get_master_conn(self):
        """Get master metaserver
        """
        global g_redis_cli
        for i, host in enumerate(self._host_list):
            if not self._is_auth:
                status, output = commands.getstatusoutput(
                    "timeout %s %s -h %s -p %s info replication | grep role"
                    % (str(self._timeout), g_redis_cli, host, str(self._port_list[i])))
            else:
                status, output = commands.getstatusoutput(
                    "timeout %s %s -h %s -p %s -a %s info replication | grep role"
                    % (str(self._timeout), g_redis_cli, host, str(self._port_list[i]),
                       self._auth))
            if status == 0 and output.strip().endswith("master"):
                self._master_host = host
                self._master_port = self._port_list[i]
                break
        if self._master_host is None or self._master_port is None:
            raise ValueError("Connect to metaserver failed")
        if self._is_auth:
            self._cmd_pre = ("timeout %s %s -h %s -p %s -a %s"
                             % (str(self._timeout), g_redis_cli, self._master_host,
                                str(self._master_port), self._auth))
        else:
            self._cmd_pre = ("timeout %s %s -h %s -p %s"
                             % (str(self._timeout), g_redis_cli, self._master_host,
                                str(self._master_port)))

    def get_shard_info(self, shard_id):
        """Get shard info
        """
        global g_redis_cli
        status, output = commands.getstatusoutput("%s msget %s" % (self._cmd_pre, str(shard_id)))
        if status == 0:
            ret = dict()
            r_list = output.strip().split("\n")
            for i in range(len(r_list)):
                if i % 2 == 1:
                    ret[r_list[i - 1]] = safe_cast(r_list[i], int) \
                        if safe_cast(r_list[i], int) is not None else r_list[i]
            return ret
        return None

    def get_redis_info(self, redis_id):
        """Get redis info
        """
        status, output = commands.getstatusoutput("%s mriget %s" % (self._cmd_pre, str(redis_id)))
        if status == 0:
            ret = dict()
            r_list = output.strip().split("\n")
            for i in range(len(r_list)):
                if i % 2 == 1:
                    ret[r_list[i - 1]] = safe_cast(r_list[i], int) \
                        if safe_cast(r_list[i], int) is not None else r_list[i]
            return ret
        return None


class MetaDataDetector(object):
    """Check metadata error
    """

    replication_data = dict()

    def __init__(self, idc_config, db_handler):
        """init
        """
        self._idc_config = idc_config
        self._metaserv_handler = None
        try:
            self._metaserv_handler = MetaServerHandler(
                idc_config["meta_host_list"], idc_config["meta_port_list"],
                idc_config["meta_is_auth"], idc_config["meta_auth"])
        except Exception as e:
            logging.exception("Init metaserver handler failed; Errmsg: %s" % e.message)
        self._db_handler = db_handler
        self._meta_ignores = get_ignore_cluster()

    def check_v5_metadata(self):
        """Check meta between db and metaserver
        """
        global g_max_master_not_up_time
        cluster_infos = self._db_handler.get_all_cluster_info(5001)
        for cluster_info in cluster_infos:
            if cluster_info["id"] in self._meta_ignores:
                continue
            shard_infos = self._db_handler.get_all_shards(
                cluster_info["id"])
            for shard_info in shard_infos:
                # print shard_info
                meta_shard_info = self._metaserv_handler.get_shard_info(shard_info["shard_id"])
                if not meta_shard_info:
                    g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                               "Get metaserver info failed",
                                               "Get metaserver info failed",
                                               shard_info=shard_info, inst_info=None)
                if cluster_info["status"] not in [17, 18]:
                    if meta_shard_info["master"] != shard_info["master_inst"]["id"] \
                            or meta_shard_info["slaves"] != shard_info["slave_inst"]["id"]:
                        g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                                   "Metaserver not match db",
                                                   "Metaserver not match db",
                                                   shard_info=shard_info, inst_info=None)
                for role_in_shard in ["master_inst", "slave_inst"]:
                    redis_meta_info = self._metaserv_handler.get_redis_info(
                        shard_info[role_in_shard]["id"])
                    r_redis_info = redis_info(shard_info[role_in_shard]["floating_ip"],
                                              shard_info[role_in_shard]["port"],
                                              "replication")
                    if not redis_meta_info:
                        g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                                   "Get metaserver info failed",
                                                   "Get metaserver info failed",
                                                   shard_info=shard_info, inst_info=None)
                    if r_redis_info \
                            and not is_type_match_role(redis_meta_info["type"],
                                                   r_redis_info["role"]):
                        g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                                   "Metaserver not match redis role",
                                                   "Metaserver not match redis role",
                                                   shard_info=shard_info, inst_info=None)
                    if r_redis_info and r_redis_info["role"] == "slave":
                        now_time = time.time()
                        if shard_info[role_in_shard]["id"] \
                                not in MetaDataDetector.replication_data:
                            MetaDataDetector.replication_data[shard_info[role_in_shard]["id"]] \
                                = now_time
                        if r_redis_info["master_link_status"] != "up":
                            not_up_time \
                                = now_time - MetaDataDetector.replication_data[
                                      shard_info[role_in_shard]["id"]]
                            if not_up_time > g_max_master_not_up_time:
                                g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                                           "mater not up",
                                                           "mater not up",
                                                           shard_info=shard_info,
                                                           inst_info=None)
                        else:
                            MetaDataDetector.replication_data[shard_info[role_in_shard]["id"]] \
                                = now_time

    def check_v7_metadata(self):
        """check v7 metadata
        """
        cluster_infos = self._db_handler.get_all_cluster_info(7001)
        for cluster_info in cluster_infos:
            if cluster_info["id"] in self._meta_ignores:
                continue
            shard_info = self._db_handler.get_all_shards(
                cluster_info["id"])[0]
            for role_in_shard in ["master_inst", "slave_inst"]:
                r_redis_info = redis_info(shard_info[role_in_shard]["floating_ip"],
                                          shard_info[role_in_shard]["port"],
                                          "replication")
                if cluster_info["status"] not in [17, 46]:
                    if r_redis_info \
                            and r_redis_info["role"] + "_inst" != role_in_shard:
                        g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                                   "Db not match redis role",
                                                   "Db not match redis role",
                                                   shard_info=shard_info, inst_info=None)
                if r_redis_info and r_redis_info["role"] == "slave":
                    now_time = time.time()
                    if shard_info[role_in_shard]["id"] \
                            not in MetaDataDetector.replication_data:
                        MetaDataDetector.replication_data[shard_info[role_in_shard]["id"]] \
                            = now_time
                    if r_redis_info["master_link_status"] != "up":
                        not_up_time \
                            = now_time - MetaDataDetector.replication_data[
                                  shard_info[role_in_shard]["id"]]
                        if not_up_time > g_max_master_not_up_time:
                            g_warning_manager.add_warn("METAERR", cluster_info["id"],
                                                       "mater not up",
                                                       "mater not up",
                                                       shard_info=shard_info,
                                                       inst_info=None)
                    else:
                        MetaDataDetector.replication_data[shard_info[role_in_shard]["id"]] \
                            = now_time

    def check_slave_cluster(self):
        """check slave cluster sync status
        """
        cluster_infos = self._db_handler.get_all_slave_cluster_info()
        for cluster_info in cluster_infos:
            if cluster_info["id"] in self._meta_ignores:
                continue
            shard_info = self._db_handler.get_all_shards(
                cluster_info["id"])[0]
            for role_in_shard in ["master_inst", "slave_inst"]:
                r_redis_info = redis_info(shard_info[role_in_shard]["floating_ip"],
                                          shard_info[role_in_shard]["port"],
                                          "replication")
                now_time = time.time()
                if r_redis_info:
                    if r_redis_info["role"] != "slave":
                        g_warning_manager.add_warn("SLAVE_CLUSTER_NOT_UP", cluster_info["id"],
                                                   "slave cluster not up",
                                                   "slave cluster not up",
                                                   shard_info=shard_info,
                                                   inst_info=None)
                    if shard_info[role_in_shard]["id"] \
                            not in MetaDataDetector.replication_data:
                        MetaDataDetector.replication_data[shard_info[role_in_shard]["id"]] \
                            = now_time
                    if r_redis_info["master_link_status"] != "up":
                        not_up_time \
                            = now_time - MetaDataDetector.replication_data[shard_info[
                                role_in_shard]["id"]]
                        if not_up_time > g_max_master_not_up_time:
                            g_warning_manager.add_warn("SLAVE_CLUSTER_NOT_UP", cluster_info["id"],
                                                       "slave cluster not up",
                                                       "slave cluster not up",
                                                       shard_info=shard_info,
                                                       inst_info=None)
                    else:
                        MetaDataDetector.replication_data[shard_info[role_in_shard]["id"]] \
                            = now_time


def execute():
    """execute
    """
    global g_check_interval
    cluster_status_detector = ClusterStatusDetector(None)

    while True:
        start_time = time.time()
        idc_config = _get_idc_config()
        db_handler = DbHandler(host=idc_config["host"], port=idc_config["port"],
                               user=idc_config["user"], password=idc_config["password"],
                               db=idc_config["db"])
        cluster_ids = db_handler.get_all_cluster_ids()
        ignore_clusters = get_ignore_cluster()
        for cluster_id in cluster_ids:
            if cluster_id not in ignore_clusters:
                detect_thread = ReachableDetector(cluster_id, db_handler, need_check_slave=True)
                detect_thread.setDaemon(True)
                detect_thread.start()
                detect_thread.join()
        cluster_slave_ids = db_handler.get_all_salve_cluster_ids()
        for cluster_id in cluster_slave_ids:
            if cluster_id not in ignore_clusters:
                detect_thread = ReachableDetector(cluster_id, db_handler, need_check_slave=True)
                detect_thread.setDaemon(True)
                detect_thread.start()
                detect_thread.join()
        cluster_status_detector.update_db_handler(db_handler)
        cluster_status_detector.check_unnormal(ignore_clusters)
        meta_handler = MetaDataDetector(idc_config, db_handler)
        meta_handler.check_v5_metadata()
        meta_handler.check_v7_metadata()
        meta_handler.check_slave_cluster()
        g_warn_info.reduce_times()
        g_warn_info.show_warning()
        db_handler.close()
        g_warning_manager.show_warn()
        cost = time.time() - start_time
        if g_check_interval - cost > 0:
            time.sleep(g_check_interval - cost)


if __name__ == "__main__":
    init_log("./scs_reachable")
    execute()