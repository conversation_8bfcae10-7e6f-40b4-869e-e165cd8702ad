#!/usr/bin/env bash

function get_blb_rs() {
    curdir=`pwd`
    cd /home/<USER>/scs/x1-tools
    ./bin/x1-tools  bec ListLbRs ${iam_user_id} ${elb_id} | grep -o -P '(?<="instanceId":")[^"]*'
    cd ${curdir}
}

function send_message() {
    curl --location 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d6b52686a41c7378e3d17dba7b1bb8ee0' --header 'Content-Type: application/json' --data "{\"message\":{\"body\":[{\"content\":\"${1} ${uuid} ${rs} ${role} \",\"type\":\"TEXT\"}]}}"
}

function check() {
    if [ x$cache_instance_type == x"3" ]; then
      if [ x$role != x"master" ]; then
        echo "[ROLE ERROR] uuid: $uuid, cluster_id: $cluster_id, cache_instance_type: $cache_instance_type, rs: $rs, role: $role"
        send_message "ROLE ERROR"
      fi
      if [ x$rs != x$uuid ]; then
        echo "[RS ERROR] uuid: $uuid, cluster_id: $cluster_id, cache_instance_type: $cache_instance_type, rs: $rs, role: $role"
        send_message "RS ERROR"
      fi
    fi
    if [ x$cache_instance_type == x"2" ]; then
      if [ x$role != x"slave" ]; then
        echo "[ROLE ERROR] uuid: $uuid, cluster_id: $cluster_id, cache_instance_type: $cache_instance_type, rs: $rs, role: $role"
        send_message "RS ERROR"
      fi
    fi
}

/home/<USER>/local/mysql/bin/mysql -h10.11.111.73 -P5363 -ubce_scs_w -pf_UJYZzr5du4iOsj bce_scs_edge_x1_task -e 'select uuid, cluster_id, vpc_endpoint.endpoint_ip, endpoint_port, password,redis_auth,cache_instance_type, cache_instance.iam_user_id, cache_cluster.elb_id from bce_scs_edge.cache_instance, bce_scs_edge_x1_task.vpc_endpoint, bce_scs_edge.cache_cluster where cache_instance.uuid = vpc_endpoint.entity and cache_instance.cluster_id = cache_cluster.id and cache_cluster.status = 5 and vpc_endpoint.backend_port not in (7042,22,0)' | grep -v uuid > edge.info

while read line; do
    uuid=`echo $line | awk '{print $1}'`
    cluster_id=`echo $line | awk '{print $2}'`
    endpoint_ip=`echo $line | awk '{print $3}'`
    endpoint_port=`echo $line | awk '{print $4}'`
    password=`echo $line | awk '{print $5}'`
    redis_auth_str=`echo $line | awk '{print $6}'`
    redis_auth=$(./octopus aes_decode a ${redis_auth_str} | awk -F':' '{print $2}')
    cache_instance_type=`echo $line | awk '{print $7}'`
    iam_user_id=`echo $line | awk '{print $8}'`
    elb_id=`echo $line | awk '{print $9}'`

    role=$(/home/<USER>/scs/redis-cli -h $endpoint_ip -p $endpoint_port -a "${redis_auth}" info | grep -o -P '(?<=role:)[^\r]*')
    rs=`get_blb_rs`
    echo "uuid: $uuid, cluster_id: $cluster_id, cache_instance_type: $cache_instance_type, rs: $rs, role: $role"
    check
done < edge.info