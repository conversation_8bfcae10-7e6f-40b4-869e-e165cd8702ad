#!/usr/bin/env python2.7
# -*- coding: utf-8 -*-
"""
get all controller
"""

import commands
from flask import Flask, request, jsonify

app = Flask(__name__)


@app.route('/api/lsAllServices', methods=['GET'])
def ls_all_services():
    """list all services
    """
    token = request.headers.get('Authorization')
    if not token or token != '21c067ebac774f6fb775c2879e251af4':
        return jsonify({'error': 'Unauthorized'}), 401

    status, output = commands.getstatusoutput("sh cmd.sh")
    if status != 0:
        return jsonify({'error': '%s' % output}), 500
    result = dict()
    for line in output.split('\n'):
        if not line.strip():
            continue
        parts = line.strip().split("-")
        region = parts[0][0:2]
        flag = ""
        if "fsg" in line.strip():
            flag = "fsg"
        if "vip" in line.strip():
            flag = "vip"
        if "edge" in line.strip():
            flag = "edge"
        if region + flag not in result:
            result[region + flag] = []
        result[region + flag].append(line.strip())

    return jsonify(result)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8789)
