#!/usr/bin/expect -f

set timeout -1

#expect窗口自适应
trap {
 set rows [stty rows]
 set cols [stty columns]
 stty rows $rows columns $cols < $spawn_out(slave,name)
} WINCH

# 设置输入参数
set host [lindex $argv 0]
set user [lindex $argv 1]
set password [lindex $argv 2]
set RelayHost [lindex $argv 3]
set RelayUser [lindex $argv 4]
set RelayPassword [lindex $argv 5]

# 设置登录远程机器帐号remoteUser以及工作帐号workUser
if { $user == "DoorGod" } {
	# 门神登录使用邮箱前缀作为帐号，密码作为工作帐号
    set remoteUser $RelayUser
    set workUser [lindex $argv 2]
} else {
    set remoteUser $user
}

# 先登录relay
catch {spawn ssh $RelayUser@$RelayHost}
expect {
	# 第一次需要手动输入token密码，将控制权交给终端
	"*password:*" { 
		send "$RelayPassword\r"
		expect "*bash-baidu-ssl*" { send "ssh $remoteUser@$host\r" }
	}
	# 通过共享ssh免密登录relay，自动发起登录远程机器命令
	"*bash-baidu-ssl*" { send "ssh $remoteUser@$host\r" }
}

# 已经成功登录远程机器
if { $user == "DoorGod" } {
	if { $workUser != $RelayUser } {
		# 如果使用门神免密登录，且工作帐号不是本人帐号，切换帐号并cd到用户目录
		send "sudo -iu $workUser\r"
    		#send "cd /home/<USER>"
	}
} else {
	# 使用帐号+密码方式登录，自动输入密码
    expect "*password:*" { send "$password\r" }
}

# 将本机编码切换到utf-8，可以解决utf-8编码的文件显示中文出现乱码问题
# 注意：如果你需要强制转换为其他编码，请修改下行的编码或者删除
send "export LANG=en_US.UTF-8;export LC_ALL=en_US.UTF-8;export LC_CTYPE=en_US.UTF-8\r"


# 切换到控制台，正常手动输入模式
interact
