#!/usr/bin/env python

import iterm2
import sys

def get_color(color="random"):
    """get color
    """
    color_map = {
        "red": iterm2.color.Color(255,182,193),
        "yellow": iterm2.color.Color(255,215,0),
        "blue": iterm2.color.Color(135,206,250),
        "green": iterm2.color.Color(144,238,144), 
        "purple": iterm2.color.Color(255,105,180),
    }
    return color_map[color]

async def main(connection):
    app = await iterm2.async_get_app(connection)
    window = app.current_terminal_window
    session = window.current_tab.current_session
    update = iterm2.LocalWriteOnlyProfile()
    update.set_allow_title_setting(True)
    update.set_name(sys.argv[1])
    if len(sys.argv) >= 3 and sys.argv[2]:
        update.set_tab_color(get_color(sys.argv[2]))
        update.set_use_tab_color(True)
    update.set_allow_title_setting(False)
    await session.async_set_profile_properties(update)

iterm2.run_until_complete(main)
