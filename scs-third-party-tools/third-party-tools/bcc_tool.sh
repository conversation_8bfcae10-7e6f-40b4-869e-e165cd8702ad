#!/bin/bash

#import read_conf.sh
source ./read_conf.sh

if [ $# != 3 ]; then
        echo "sh bcc_tool.sh [start|stop] [iam_user_id] [uuid]"
        exit
fi

url=$(get_openstack_nova_url)
octopus_path=$(get_octopus_path)
octopus_bin=$(get_octopus_bin)

action=$1
iam_user_id=$2
uuid=$3

request_id=$(uuidgen)

token_str=$(cd $octopus_path && ./$octopus_bin fetch_token $iam_user_id)
token_tmp=$(echo $token_str | grep ^Token)
if [ -n "$token_tmp" ]; then
        token=$(echo $token_tmp | awk -F ' ' '{print $1}' | awk -F ':' '{print $2}')
else
        token=$token_str
fi

tenant_id=$(echo $token_str | awk -F ' ' '{print $2}' | awk -F ':' '{print $2}')
echo $tenant_id

echo "url=$url"
if [ $action = "stop" ];then
    echo "stop " $uuid
    curl -i -X POST http://$url/v2/$tenant_id/servers/$uuid/action -d '{"os-stop":null}' -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
fi

if [ $action = "start" ];then
    echo "start "$uuid
    curl -i -X POST http://$url/v2/$tenant_id/servers/$uuid/action -d '{"os-start":null}' -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
fi
