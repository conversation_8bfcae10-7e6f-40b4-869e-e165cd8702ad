#!/bin/bash

#import read_conf.sh
source ./read_conf.sh

#global variable
octopus_path=$(get_octopus_path)
octopus_bin=$(get_octopus_bin)

security_group_url=$(get_security_group_url)

#get token by iam_user_id
function get_token()
{
    iam_user_id=$1
    token_str=$(cd $octopus_path && ./$octopus_bin fetch_token $iam_user_id)
    token_tmp=$(echo $token_str | grep ^Token)
    if [ -n "$token_tmp" ]; then
        token=$(echo $token_tmp | awk -F ' ' '{print $1}' | awk -F ':' '{print $2}')
    else
        token=$token_str
    fi
    echo $token
}

function get_security_group_by_id() {
    iam_user_id=$1
    security_group_id=$2

    echo "iam_user_id = ${iam_user_id}, security_group_id = ${security_group_id}"
    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)

    curl -i -X GET http://${security_group_url}/v2.0/security-groups/${security_group_id}.json -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
}

function create_security_rule() {
    iam_user_id=$1
    security_group_id=$2
    port=$3
    ip=$4

    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)
    curl -i -X POST http://${security_group_url}/v2.0/security-group-rules -d '{
"security_group_rule" : {
"ethertype" : "IPv4",
"security_group_id":"'"${security_group_id}"'",
"direction":"ingress",
"protocol":"tcp",
"port_range_min":"'"${port}"'",
"port_range_max":"'"${port}"'",
"remote_ip_prefix":"'"${ip}"'",
"creator":"scs"
}
}' -H "Content-type:application/json" -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}"
}

function delete_security_rule_by_rule_id() {
    iam_user_id=$1
    security_rule_id=$2

    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)

    curl -i -X DELETE http://${security_group_url}/v2.0/security-group-rules/${security_rule_id}.json -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
}

function usage() {
    echo "security group tool support following commands:"
    echo "sh $0 get_security_group_by_id [iam_user_id] [security_group_id]"
    echo "sh $0 create_security_rule [iam_user_id] [security_group_id] [port] [ip]"
    echo "sh $0 delete_security_rule_by_rule_id [iam_user_id] [security_group_id]"
}

####################################################
case $1 in
    get_security_group_by_id)
	if [ $# -ne 3 ]; then
	    echo "sh $0 get_security_group_by_id [iam_user_id] [security_group_id]"
	else
	    get_security_group_by_id $2 $3
	fi
	;;
    create_security_rule)
	if [ $# -ne 5 ]; then
	    echo "sh $0 create_security_rule [iam_user_id] [security_group_id] [port] [ip]"
	else
	    create_security_rule $2 $3 $4 $5
	fi
	;;
    delete_security_rule_by_rule_id)
	if [ $# -ne 3 ]; then
	    echo "sh $0 delete_security_rule_by_rule_id [iam_user_id] [security_group_id]"
	else
	    delete_security_rule_by_rule_id $2 $3
	fi
	;;
    *)
	usage
	;;
esac
