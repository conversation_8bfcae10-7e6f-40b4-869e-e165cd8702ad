#!/bin/bash

region=qasandbox_90

function get_field_value()
{
    if [ ! -f $1 ] || [ $# -ne 3 ];then
        return 1
    fi
    blockname=$2
    fieldname=$3

    begin_block=0
    end_block=0

    cat $1 | while read line || [[ -n ${line} ]]
    do
        if [ "X$line" = "X[$blockname]" ];then
            begin_block=1
            continue
        fi

        if [ $begin_block -eq 1 ];then
            end_block=$(echo $line | awk 'BEGIN{ret=0} /^\[.*\]$/{ret=1} END{print ret}')
            if [ $end_block -eq 1 ];then
                #echo "end block"
                break
            fi

            need_ignore=$(echo $line | awk 'BEGIN{ret=0} /^#/{ret=1} /^$/{ret=1} END{print ret}')
            if [ $need_ignore -eq 1 ];then
                #echo "ignored line:" $line
                continue
            fi
            field=$(echo $line | awk -F '=' '{gsub(" |\t","",$1); print $1}')
            value=$(echo $line | awk -F '=' '{gsub(" |\t","",$2); print $2}')
            # echo "'$field':'$value'"
            if [ "X$fieldname" = "X$field" ];then
                #echo "result value:'$result'"
                echo $value
                break
            fi

        fi
    done
    return 0
}

#get config file
function get_conf_file() {
    conf_file=""

    if [ "$region" == "qasandbox_115" ]; then
        conf_file="./conf/tool_config_115"
    fi

    if [ "$region" == "qasandbox_y32" ]; then
        conf_file="./conf/tool_config_y32"
    fi

    echo $conf_file
}


#get octopus info
function get_octopus_path() {
    conf_file=$(get_conf_file)

    section_name="octopus"
    key_name="octopus_path"

    octopus_path=$(get_field_value $conf_file $section_name $key_name)
    echo $octopus_path
}

function get_octopus_bin() {
    conf_file=$(get_conf_file)

    section_name="octopus"
    key_name="octopus_bin"

    octopus_bin=$(get_field_value $conf_file $section_name $key_name)
    echo $octopus_bin
}

#get blb url
function get_blb_url() {
    conf_file=$(get_conf_file)

    section_name="blb"
    key_name="blb_url"
    
    blb_url=$(get_field_value $conf_file $section_name $key_name)
    echo $blb_url
}

#get vpc url
function get_vpc_url() {
    conf_file=$(get_conf_file)

    section_name="vpc"
    key_name="vpc_url"

    vpc_url=$(get_field_value $conf_file $section_name $key_name)
    echo $vpc_url
}

#get mysql url
function get_mysql_url() {
    conf_file=$(get_conf_file)

    section_name="mysql"
    key_name="ip"
    ip=$(get_field_value $conf_file $section_name $key_name)

    key_name="port"
    port=$(get_field_value $conf_file $section_name $key_name)

    key_name="user"
    user_name=$(get_field_value $conf_file $section_name $key_name)

    key_name="password"
    db_passwd=$(get_field_value $conf_file $section_name $key_name)

    mysql_url="mysql -u${user_name} -h${ip} -p${db_passwd} -P${port}"
    echo $mysql_url
}

#get mysql db
function get_mysql_db() {
    conf_file=$(get_conf_file)

    section_name="mysql"
    key_name="database"

    db_name=$(get_field_value $conf_file $section_name $key_name)
    echo $db_name
}

#get security group url
function get_security_group_url() {
    conf_file=$(get_conf_file)
    section_name="security_group"
    key_name="security_group_url"

    sg_rule=$(get_field_value $conf_file $section_name $key_name)
    echo $sg_rule
}

#get openstack nova url
function get_openstack_nova_url() {
    conf_file=$(get_conf_file)
    section_name="openstack"
    key_name="NovaUrl"

    nova_url=$(get_field_value $conf_file $section_name $key_name)
    echo $nova_url
}
