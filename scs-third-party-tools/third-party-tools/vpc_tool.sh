#!/bin/bash

#import read_conf.sh
source ./read_conf.sh

#global variable
octopus_path=$(get_octopus_path)
octopus_bin=$(get_octopus_bin)

vpc_url=$(get_vpc_url)

#functions
function get_token() 
{
    iam_user_id=$1
    token_str=$(cd $octopus_path && ./$octopus_bin fetch_token $iam_user_id)
    token_tmp=$(echo $token_str | grep ^Token)
    if [ -n "$token_tmp" ]; then
        token=$(echo $token_tmp | awk -F ' ' '{print $1}' | awk -F ':' '{print $2}')
    else
        token=$token_str
    fi
    echo $token
}

function get_user_vpcs()
{
    iam_user_id=$1

    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)
    echo "token=$token"
    echo "vpc_url=$vpc_url"
    echo "request_id=$request_id"

    curl -i -X GET http://${vpc_url}/v2.0/vpcs -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
}

function get_vpc_by_id() 
{
    iam_user_id=$1
    vpc_id=$2

    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)

    curl -i -X GET http://${vpc_url}/v2.0/vpcs/${vpc_id}.json -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
}

function get_vpc_subnets()
{
    iam_user_id=$1
    vpc_id=$2

    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)

    curl -i -X GET http://${vpc_url}/v2.0/subnets.json?vpc_id=${vpc_id} -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
}

function get_subnet_by_id()
{
    iam_user_id=$1
    subnet_id=$2

    request_id=$(uuidgen)
    token=$(get_token $iam_user_id)

    curl -i -X GET http://${vpc_url}/v2.0/subnets/${subnet_id}.json -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
}

function usage()
{
    echo "vpc tool support following commands:"
    echo "sh $0 get_user_vpcs [iam_user_id]"
    echo "sh $0 get_vpc_by_id [iam_user_id] [vpc_id]"
    echo "sh $0 get_vpc_subnets [iam_user_id] [vpc_id]"
    echo "sh $0 get_subnet_by_id [iam_user_id] [subnet_id]"
}
####################################################
case $1 in
    get_user_vpcs)
        echo "this is get_user_vpcs"
        if [ $# -ne 2 ]; then
            echo "Usage: sh $0 get_user_vpcs [iam_user_id]"
        else
            get_user_vpcs $2
        fi
        ;;
    get_vpc_by_id)
        if [ $# -ne 3 ]; then
            echo "Usage:sh $0 get_vpc_by_id [iam_user_id] [vpc_id]"
        else
            get_vpc_by_id $2 $3
        fi
        ;;
    get_vpc_subnets)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 get_vpc_subnets [iam_user_id] [vpc_id]"
        else
            get_vpc_subnets $2 $3
        fi
        ;;
    get_subnet_by_id)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 get_subnet_by_id [iam_user_id] [subnet_id]"
        else
            get_subnet_by_id $2 $3
        fi
        ;;
    *)
        usage
        ;;
esac

