#!/bin/bash

#import read_conf.sh
source ./read_conf.sh

if [ $# != 1 ]; then
	echo "sh get_security_group_info_by_clusterid.sh [cluster_id]"
	exit
fi

mysql_url=$(get_mysql_url)
mysql_db=$(get_mysql_db)

url=$(get_security_group_url)
octopus_path=$(get_octopus_path)
octopus_bin=$(get_octopus_bin)

request_id=$(uuidgen)

security_group_id=$($mysql_url -e "use ${mysql_db};select id, security_group_id from cache_cluster where id='$1';" | grep $1 | awk -F ' ' '{print $2}' )
iam_user_id=$($mysql_url -e "use ${mysql_db};select distinct iam_user_id, cluster_id from cache_instance where cluster_id = $1;" | grep $1 | awk -F ' ' '{print $1}')

token_str=$(cd $octopus_path && ./$octopus_bin fetch_token $iam_user_id)
token_tmp=$(echo $token_str | grep ^Token)
if [ -n "$token_tmp" ]; then
	token=$(echo $token_tmp | awk -F ' ' '{print $1}' | awk -F ':' '{print $2}')
else
	token=$token_str
fi

echo "url=$url"
curl -i -X GET http://${url}/v2.0/security-groups/$security_group_id -H "Content-type:application/json" -H "x-bce-request-id:$request_id" -H "X-Auth-Token:$token"
