tools:
1. read_conf.sh
    用于设置region，以及读取配置文件的函数。
2. conf
    配置文件的存放目录。其中每个地域都会有一个配置文件。当前tool_config_115是沙盒环境115的配置
3. blb_tool.sh
    blb的相关功能
4. vpc_tool.sh
    vpc的相关功能
5. get_security_group_info_by_clusterid.sh
    根据cluster_id获取安全组信息
6. test_conf.sh
    用于测试read_conf.sh
7. security_group_tool.sh
    安全组相关功能

在使用这些工具前，需要做如下准备工作：
  1）在conf目录中，添加相关地域的配置文件
  2）在read_conf.sh中，修改region的值
  3）在read_conf.sh中，修改get_conf_file函数，对新增的region执行其配置文件目录
