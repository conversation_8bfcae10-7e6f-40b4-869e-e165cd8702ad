#!/bin/bash

#import read_conf.sh
source ./read_conf.sh

blb_url=$(get_blb_url)
octopus_path=$(get_octopus_path)
octopus_bin=$(get_octopus_bin)

#get token by iam_user_id
function get_token()
{
    iam_user_id=$1
    token_str=$(cd $octopus_path && ./$octopus_bin fetch_token $iam_user_id)
    token_tmp=$(echo $token_str | grep ^Token)
    if [ -n "$token_tmp" ]; then
        token=$(echo $token_tmp | awk -F ' ' '{print $1}' | awk -F ':' '{print $2}')
    else
        token=$token_str
    fi
    echo $token
}

################## BLB ################################################
function query_user_blb_by_id() {
    iam_user_id=$1
    blb_id=$2
    token=$(get_token $iam_user_id)
    uri=/open-api/v1/blb
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X GET http://${blb_url}${uri}/${blb_id} -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

##it is a special case of list_user_blb
#use blb name fuzzy matching
function fuzzy_query_user_blb_by_name() {
    iam_user_id=$1
    blb_name=$2
    token=$(get_token $iam_user_id)
    uri=/open-api/v1/blb
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X GET http://${blb_url}${uri}?name=${blb_name}\&exactMatch=0\&includeService=1 -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function list_user_blb(){
    iam_user_id=$1
    token=$(get_token $iam_user_id)
    uri=/open-api/v1/blb
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X GET http://${blb_url}${uri}?includeService=1 -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

#create one ipv4 blb instance in vpc
#change type to ipv6 if creating an ipv6 blb instance
function create_blb() {
    iam_user_id=$1
    vpc_id=$2
    subnet_id=$3
    az_zone=$4
    name=$5
    
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    uri=/open-api/v2/blb

    curl -i -X POST http://${blb_url}${uri} -d '{
"count": 1,
"vpcId": "'"${vpc_id}"'",
"subnetId": "'"${subnet_id}"'",
"name": "'"${name}"'",
"source": "scs",
"internal": true,
"az_zone": "'"${az_zone}"'",
"is_vpc": true,
"type": "normal"}' -H "Content-type:application/json" -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function create_blb_vip() {
    iam_user_id=$1
    vpc_id=$2
    subnet_id=$3
    az_zone=$4
    name=$5

    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    uri=/open-api/v2/blb

    curl -i -X POST http://${blb_url}${uri} -d '{
"count": 1,
"vpcId": "'"${vpc_id}"'",
"subnetId": "'"${subnet_id}"'",
"name": "'"${name}"'",
"source": "scs",
"internal": true,
"az_zone": "'"${az_zone}"'",
"is_vpc": true,
"allocateVip": true,
"type": "normal"}' -H "Content-type:application/json" -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function delete_blb() {
    uri=/open-api/v1/blb
    iam_user_id=$1
    blb_id=$2
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X DELETE http://${blb_url}${uri}/${blb_id} -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function blb_pause() {
    iam_user_id=$1
    blb_id=$2
    uri=/open-api/v1/blb
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X PUT http://${blb_url}${uri}/${blb_id}?action=pause -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function blb_reboot() {
    iam_user_id=$1
    blb_id=$2
    uri=/open-api/v1/blb
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X PUT http://${blb_url}${uri}/${blb_id}?action=recover -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}
################## BLB Listener ################################################

function query_listener_by_id() {
    iam_user_id=$1
    blb_id=$2
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X GET http://${blb_url}/open-api/v1/blb/${blb_id}/listener -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function modify_listener_scheduler() {
    iam_user_id=$1
    blb_id=$2
    port=$3
  scheduler=$4

    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")

    curl -i -X PUT http://${blb_url}/open-api/v1/blb/${blb_id}/listener/${port}?action=update -d '{"scheduler":"'"${scheduler}"'"}' -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}

function delete_listener() {
    iam_user_id=$1
    blb_id=$2
    port=$3

    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")

    curl -i -X DELETE http://${blb_url}/open-api/v1/blb/${blb_id}/listener/${port} -H "X-Auth-Token:${token}" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}"
}
################## BLB Backend server################################################
function list_backend_server() {
    iam_user_id=$1
    blb_id=$2
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
    curl -i -X GET http://${blb_url}/open-api/v1/blb/${blb_id}/backendserver -H "Content-type:application/json" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}" -H "X-Auth-Token:${token}"
}

function update_backend_server() {
    iam_user_id=$1
    blb_id=$2
    bcc_id=$3
    weight=$4
    
    token=$(get_token $iam_user_id)
    request_id=$(uuidgen)
    date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")

    curl -i -X PUT http://${blb_url}/json-api/v2/blb/${blb_id}/backendserver/${bcc_id}?action=update -d '{"weight": "'"${weight}"'"}' -H "Content-type:application/json" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}" -H "X-Auth-Token:${token}"
}

function create_backend_server() {
	iam_user_id=$1
	blb_id=$2
	bcc_id=$3
	weight=$4
	token=$(get_token $iam_user_id)
	request_id=$(uuidgen)
	date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
	curl -i -X POST http://${blb_url}/open-api/v1/blb/${blb_id}/backendserver -d '{"backendServerList":[{"instanceId":"'"${bcc_id}"'", "weight":1}]}' -H "Content-type:application/json" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}" -H "X-Auth-Token:${token}"
}

function delete_backend_server() {
        iam_user_id=$1
        blb_id=$2
        bcc_id=$3
        token=$(get_token $iam_user_id)
        request_id=$(uuidgen)
        date_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
        curl -i -X DELETE http://${blb_url}/json-api/v2/blb/${blb_id}/backendserver/${bcc_id} -H "Content-type:application/json" -H "x-bce-request-id:${request_id}" -H "x-bce-date:${date_time}" -H "X-Auth-Token:${token}"
}
##################################################################
function usage()
{
        echo -e "All interface reference: \033[40;31m http://gollum.baidu.com/BLB \033[0m"
        echo "---------------------------------------"
        echo "blb tool support following commands:"
        echo "---------------------------------------"
        echo "sh $0 create_blb [iam_user_id] [vpc_id] [subnet_id] [az_zone] [name]"
        echo "sh $0 create_blb_vip [iam_user_id] [vpc_id] [subnet_id] [az_zone] [name]"
        echo "sh $0 delete_blb [iam_user_id] [blb_id]"
        echo "sh $0 list_user_blb [iam_user_id]"
        echo "sh $0 query_blb_by_id [iam_user_id] [blb_id]"
        echo "sh $0 fuzzy_query_blb_by_name [iam_user_id] [blb_name]"
        echo "---------------------------------------"
        echo "sh $0 query_listener_by_id [iam_user_id] [blb_id]"
        echo "sh $0 modify_listener_scheduler [iam_user_id] [blb_id] [port] [scheduler]"
        echo "sh $0 delete_listener [iam_user_id] [blb_id] [port]"
        echo "---------------------------------------"
        echo "sh $0 create_backend_server [iam_user_id] [blb_id] [bcc_uuid] [weight]"
        echo "sh $0 list_backend_server [iam_user_id] [blb_id]"
        echo "sh $0 update_backend_server [iam_user_id] [blb_id] [bcc_id] [weight]"
        echo "sh $0 delete_backend_server [iam_user_id] [blb_id] [bcc_id]"
}

####################################################
case $1 in
    create_blb)
        if [ $# -ne 6 ]; then
            echo "Usage: sh $0 create_blb [iam_user_id] [vpc_id] [subnet_id] [az_zone] [name]"
        else
            echo "iam_user_id = $2"
            echo "vpc_id = $3"
            echo "subnet_id=$4"
            echo "az_zone = $5"
            echo "name = $6"
            create_blb $2 $3 $4 $5 $6
        fi
        ;;
    create_blb_vip)
        if [ $# -ne 6 ]; then
            echo "Usage: sh $0 create_blb_vip [iam_user_id] [vpc_id] [subnet_id] [az_zone] [name]"
        else
            echo "iam_user_id = $2"
            echo "vpc_id = $3"
            echo "subnet_id=$4"
            echo "az_zone = $5"
            echo "name = $6"
            create_blb_vip $2 $3 $4 $5 $6
        fi
        ;;
    delete_blb)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 delete_blb [iam_user_id] [blb_id]"
        else
            delete_blb $2 $3
        fi
        ;;
    list_user_blb)
        if [ $# -ne 2 ]; then
            echo "Usage: sh $0 -list_user_blb [iam_user_id]"
        else
            list_user_blb $2
        fi
        ;;
    query_blb_by_id)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 query_blb_by_id [iam_user_id] [blb_id]"
        else
            query_user_blb_by_id $2 $3
        fi
        ;;
    fuzzy_query_blb_by_name)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 fuzzy_query_blb_by_name [iam_user_id] [blb_name]"
        else
            fuzzy_query_user_blb_by_name $2 $3
        fi
        ;;
    query_listener_by_id)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 query_listener_by_id [iam_user_id] [blb_id]"
        else
            query_listener_by_id $2 $3
        fi
        ;;
    create_backend_server)
        if [ $# -ne 5 ]; then
            echo "Usage: sh $0 create_backend_server [iam_user_id] [blb_id] [bcc_uuid] [weight]"
        else
            create_backend_server $2 $3 $4 $5
        fi
        ;;
    list_backend_server)
        if [ $# -ne 3 ]; then
            echo "Usage: sh $0 list_backend_server [iam_user_id] [blb_id]"
        else
            list_backend_server $2 $3
        fi
        ;;
    update_backend_server)
        if [ $# -ne 5 ]; then
            echo "Usage: sh $0 update_backend_server [iam_user_id] [blb_id] [bcc_id] [weight]"
            echo "weight should in [0, 100]"
        else
            update_backend_server $2 $3 $4 $5
        fi
        ;;
    modify_listener_scheduler)
        if [ $# -ne 5 ]; then
            echo "sh $0 modify_listener_scheduler [iam_user_id] [blb_id] [port] [scheduler]"
            echo "scheduler: RoundRobin / LeastConnection / Hash"
        else
            modify_listener_scheduler $2 $3 $4 $5
        fi
        ;;
    delete_backend_server)
        if [ $# -ne 4 ]; then
            echo "Usage: sh $0 delete_backend_server [iam_user_id] [blb_id] [bcc_uuid]"
        else
            delete_backend_server $2 $3 $4
        fi
        ;;
    delete_listener)
        if [ $# -ne 4 ]; then
            echo "sh $0 delete_listener [iam_user_id] [blb_id] [port]"
        else
            delete_listener $2 $3 $4
        fi
        ;;
    *)
        usage
        ;;
    *)
        usage
        ;;
esac

