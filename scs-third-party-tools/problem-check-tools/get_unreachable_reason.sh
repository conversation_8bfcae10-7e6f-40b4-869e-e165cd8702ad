#!/bin/bash

function get_reasion()
{
ssh work@$1 << eeooff
    echo "last 30 lines in file $2"
    echo "====================================="
    tail -n 30 $2
    echo "====================================="
    echo "try to get shard info"
    echo "====================================="
    cat $2 | grep METAERR | tail -1
    echo "====================================="
    cat $2 | grep UNREACHERR | tail -1
    echo "====================================="
    cat $2 | grep unnormal | tail -1
    exit
eeooff
}

region=$1
if [ "$region" == "bj" ]; then
    get_reasion cq02-online-scs-csmaster01.cq02 /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
elif [ "$region" == "gz" ]; then
    get_reasion gzns-scs-online-csmater01.gzns /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
elif [ "$region" == "su" ]; then
    get_reasion szth-scs-online-csmater01.szth /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
elif [ "$region" == "bdbl" ]; then
    get_reasion bdbl-scs-online-csmaster1-01.bdbl /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
elif [ "$region" == "bdbl-fsg" ]; then
    get_reasion bdbl-scs-onlinefsg-csmaster2-01.bdbl /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
elif [ "$region" == "hkg" ]; then
    get_reasion hkg03-scs-online-csmaster01.hkg03 /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
elif [ "$region" == "whgg" ]; then 
    get_reasion whgg-scs-online-csmaster01.whgg /home/<USER>/scs/reachable_monitor/scs_reachable.log.wf
else
    echo "Please input a region: bj, gz, su, bdbl, bdbl-fsg, hkg or whgg"
fi
