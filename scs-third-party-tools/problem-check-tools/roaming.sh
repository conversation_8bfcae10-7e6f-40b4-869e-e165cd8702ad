#!/bin/bash

region=$1
if [ "$region" == "bj" ]; then
    echo "we are going to bj: cq02-online-scs-csmaster01.cq02"
		ssh work@cq02-online-scs-csmaster01.cq02
elif [ "$region" == "bj02" ]; then
		echo "we are going to bj02: cq02-online-scs-csmaster02.cq02"
		ssh work@cq02-online-scs-csmaster02.cq02
elif [ "$region" == "bj-fsg" ]; then
		echo "we are going to bj-fsg: bjdd-scs-onlinefsg-csmaster01.bjdd"
		ssh <EMAIL>
elif [ "$region" == "bj-fsg02" ]; then
		echo "we are going to bj-fsg02: bjdd-scs-onlinefsg-csmaster02.bjdd"
		ssh <EMAIL>
elif [ "$region" == "gz" ]; then
    echo "we are going to gz: gzns-scs-online-csmater01.gzns"
    ssh <EMAIL>
elif [ "$region" == "gz02" ]; then
		echo "we are going to gz02: gzns-scs-online-csmater02.gzns"
		ssh <EMAIL>
elif [ "$region" == "su" ]; then
    echo "we are going to su: szth-scs-online-csmater01.szth"
    ssh <EMAIL>
elif [ "$region" == "su02" ]; then
		echo "we are going to su02: szth-scs-online-csmater02.szth"
		ssh <EMAIL>
elif [ "$region" == "bdbl" ]; then
    echo "we are going to bdbl: bdbl-scs-online-csmaster1-02.bdbl"
    ssh <EMAIL>
elif [ "$region" == "bdbl01" ]; then
		echo "we are going to bdbl01: bdbl-scs-online-csmaster1-01.bdbl"
		ssh <EMAIL>
elif [ "$region" == "bdbl-fsg" ]; then
    echo "we are going to bdbl-fsg: bdbl-scs-onlinefsg-csmaster2-01.bdbl"
    ssh <EMAIL>
elif [ "$region" == "bdbl-fsg02" ]; then
		echo "we are going to bdbl-fsg02: bdbl-scs-onlinefsg-csmaster2-02.bdbl"
		ssh <EMAIL>
elif [ "$region" == "hkg" ]; then
    echo "we are going to hkg: hkg03-scs-online-csmaster01.hkg03"
    ssh work@hkg03-scs-online-csmaster01.hkg03
elif [ "$region" == "hkg02" ]; then
		echo "we are going to hkg02: hkg03-scs-online-csmaster02.hkg03"
		ssh work@hkg03-scs-online-csmaster02.hkg03
elif [ "$region" == "whgg" ]; then 
    echo "we are going to whgg: whgg-scs-online-csmaster01.whgg"
    ssh <EMAIL>
elif [ "$region" == "whgg02" ]; then
		echo "we are going to whgg02: whgg-scs-online-csmaster02.whgg"
		ssh <EMAIL>
elif [ "$region" == "sin" ];then
		echo "we are going to sin: sin03-rds-online-sin03-tm1.sin03"
		ssh work@sin03-rds-online-sin03-tm1.sin03
elif [ "$region" == "sin02" ]; then
		echo "we are going to sin03-rds-online-sin03-tm2.sin03"
		ssh work@sin03-rds-online-sin03-tm2.sin03
elif [ "$region" == "90" ]; then
		echo "we are going to test:90: nmg02-bce-test90.nmg02.baidu.com"
		ssh <EMAIL>
elif [ "$region" == "62" ]; then
		echo "we are going to test:62: nmg02-bce-test62.nmg02.baidu.com"
		ssh <EMAIL>
elif [ "$region" == "61" ]; then
		echo "we are going to test:61: nmg02-bce-test61.nmg02.baidu.com"
		ssh <EMAIL>
elif [ "$region" == 115 ]; then
	echo "we are going to test:115: nmg02-bce-test115.nmg02.baidu.com"
	ssh <EMAIL>
else
    echo "Please input a region: bj, bj-fsg, gz, su, bdbl, bdbl-fsg, hkg, whgg, sin, 90, 61, 62"
		echo "or input a region: bj02, bj-fsg02, gz02, su02, bdbl01, bdbl-fsg02, hkg02, whgg02, sin02"
fi
