{"install_putil": {"description": "install_putil.", "const_list": {"agent_root": "/root/agent", "restart_agent_sh": "restart_agent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name psutil."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{agent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {agent_root} && tar -zxvf {tar_file_name}", "sudo yum -y <PERSON> python-devel", "cd {agent_root}/psutil-2.1.3 && python setup.py install"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_csagent": {"description": "Update scs csagent.", "const_list": {"csagent_root": "/root/csagent", "restart_csagent_sh": "restart_csagent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of csagent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{csagent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {csagent_root} && tar -zxvf {tar_file_name}", "redis_server_num=`ps aux | grep redis-server | grep -v grep | wc -l`; [[ $redis_server_num -gt 1 ]] && error", "cp -r {csagent_root}/src  {csagent_root}/src_bak_{timestamp}", "rm -rf {csagent_root}/src && cp -r {csagent_root}/output/csagent/src {csagent_root}/src", "rm -rf {csagent_root}/.scm && cp -rf {csagent_root}/output/.scm {csagent_root}/", "version=`cat {csagent_root}/src/VERSION.txt`;kill -9 `cat {csagent_root}/csagent.pid$version`; sleep 1", "pid_sup=$(ps -ef| grep {csagent_root}/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/csagent -f 'nohup python {csagent_root}/src/agent.py start' & ", "pid_csagent=$(ps -ef| grep {csagent_root}/src/agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_csagent -lt 1 ]] && nohup python {csagent_root}/src/agent.py start &"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {csagent_root}/output {csagent_root}/{tar_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {csagent_root}/src_new && mv {csagent_root}/src {csagent_root}/src_new && mv {csagent_root}/src_bak_{timestamp} {csagent_root}/src", "ps -ef | grep /root/csagent/src/agent.py | grep -v grep | awk '{{print $2}}' | xargs kill -9", "nohup python /root/csagent/src/agent.py start &"]}]}, "update_container_csagent": {"description": "Update scs csagent.", "const_list": {"bcc_root": "/root", "csagent_root": "/root/csagent", "restart_csagent_sh": "restart_csagent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of csagent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{csagent_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {csagent_root} && tar -zxvf {tar_file_name}'", "docker exec {uuid} /bin/bash -c \"redis_server_num=\\`ps aux | grep redis-server | grep -v grep | wc -l\\`; [[ \\$redis_server_num -gt 1 ]] && error\"", "docker exec {uuid} /bin/bash -c 'cp -r {csagent_root}/src  {csagent_root}/src_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/src && cp -r {csagent_root}/output/csagent/src {csagent_root}/src'", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/.scm && cp -rf {csagent_root}/output/.scm {csagent_root}/'", "docker exec {uuid} /bin/bash -c \"ps aux |grep {csagent_root}/src/agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\"", "docker exec {uuid} /bin/bash -c \"pid_sup=\\$(ps -ef| grep {csagent_root}/src/agent.py | grep supervise |grep -v grep | awk '{{print \\$2}}' | wc -l); [[ \\$pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/csagent -f 'nohup python {csagent_root}/src/agent.py start' & \"", "docker exec {uuid} /bin/bash -c 'sh -c \"pid_csagent=\\$(ps -ef | grep {csagent_root}/src/agent.py | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\pid_csagent -lt 1 ]; then exit 2; fi\"'"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/output {csagent_root}/{tar_file_name}'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/src_new && mv {csagent_root}/src {csagent_root}/src_new && mv {csagent_root}/src_bak_{timestamp} {csagent_root}/src'", "docker exec {uuid} /bin/bash -c \"ps aux |grep {csagent_root}/src/agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\"", "docker exec {uuid} /bin/bash -c \"pid_sup=\\$(ps -ef| grep {csagent_root}/src/agent.py | grep supervise |grep -v grep | awk '{{print \\$2}}' | wc -l); [[ \\$pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/csagent -f 'nohup python {csagent_root}/src/agent.py start' & \"", "docker exec {uuid} /bin/bash -c 'sh -c \"pid_csagent=\\$(ps -ef | grep {csagent_root}/src/agent.py | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\pid_csagent -lt 1 ]; then exit 2; fi\"'"]}]}, "restart_csagent": {"description": "Update scs csagent.", "const_list": {"csagent_root": "/root/csagent"}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef| grep {csagent_root}/src/agent.py | grep -v supervise |grep -v grep | awk '{{print $2}}' | xargs kill -9"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_csagent_config": {"description": "update csagent config.", "const_list": {"csagent_root": "/root/csagent"}, "input_list": [{"name": "csagent_config_file", "type": "string", "description": "csagent_config_file"}], "common_list": [{"type": "scp_push", "source": "{csagent_config_file}", "destination": "/root/{csagent_config_file}"}, {"type": "remote_cmd", "cmd_list": ["mv {csagent_root}/configure  {csagent_root}/configure_{timestamp}", "cp -f /root/{csagent_config_file} {csagent_root}/configure_online_bj", "cp -f /root/{csagent_config_file} {csagent_root}/configure", "kill -9 `cat {csagent_root}/agent.pid`; sleep 1", "rm -f {csagent_root}/agent.pid", "pid_sup=$(ps -ef| grep {csagent_root}/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/csagent -f 'nohup python {csagent_root}/src/agent.py start' & ", "pid_csagent=$(ps -ef| grep {csagent_root}/src/agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_csagent -lt 1 ]] && nohup python {csagent_root}/src/agent.py start &"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_logsplit": {"description": "Update scs logsplit.", "const_list": {"root": "/root", "restart_csagent_sh": "restart_csagent.sh"}, "input_list": [{"name": "sh_file_name", "type": "string", "description": "file name of logsplit output."}], "common_list": [{"type": "scp_push", "source": "{sh_file_name}", "destination": "{root}/output/{sh_file_name}"}, {"type": "remote_cmd", "cmd_list": ["mv {root}/opbin/{sh_file_name} {root}/opbin/{sh_file_name}_bak_{timestamp}", "cp -r {root}/output/{sh_file_name} {root}/opbin/", "sh {root}/opbin/{sh_file_name}"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {root}/output/{sh_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["rm {root}/output/{sh_file_name}", "mv {root}/opbin/{sh_file_name}  {root}/opbin/{sh_file_name}_new", "mv {root}/opbin/{sh_file_name}_bak_{timestamp}  {root}/opbin/{sh_file_name}"]}]}, "config_set_cmd": {"description": "send config set cmd to redis.", "const_list": {}, "input_list": [{"name": "set_cmd", "type": "string", "description": "config set cmd."}], "common_list": [{"type": "remote_cmd", "cmd_list": ["/root/agent/bin/redis-cli  -p 6379 aae420ac56ef116058218c11d8b35b30CONFIG set {set_cmd}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "set_appendonly_no": {"description": "set appendonly no to redis.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["/root/agent/bin/redis-cli  -p 6379 aae420ac56ef116058218c11d8b35b30CONFIG set appendonly no", "/root/agent/bin/redis-cli  -p 6379 aae420ac56ef116058218c11d8b35b30CONFIG set save ''"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "send_cmd": {"description": "send cmd.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["find / -name core.*  -mtime +0| xargs rm -f"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "down_proxy_log_level": {"description": "clear proxy log.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["pid_nut=$(ps -ef| grep /root/agent/bin/nutcracker | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -s SIGTTOU $pid_nut", "find /mnt/log -mtime +1| xargs rm -f"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "clear_proxy_log": {"description": "clear proxy log.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["rm -f /mnt/log/nutcracker.log.*; echo '' > /mnt/log/nutcracker.log"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "set_redis_maxmemory_policy": {"description": "set redis maxmemory-policy.", "const_list": {}, "input_list": [{"name": "maxmemory_policy", "type": "string", "description": "maxmemory-policy type noeviction|allkeys-lru|volatile-lru|allkeys-random|volatile-random|volatile-ttl."}], "common_list": [{"type": "remote_cmd", "cmd_list": ["/root/agent/bin/redis-cli  -p 6379 aae420ac56ef116058218c11d8b35b30CONFIG set maxmemory-policy {maxmemory_policy}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "set_redis_password": {"description": "set redis password.", "const_list": {}, "input_list": [{"name": "requirepass", "type": "string", "description": "redis password."}], "common_list": [{"type": "remote_cmd", "cmd_list": ["/root/agent/bin/redis-cli  -p {port} aae420ac56ef116058218c11d8b35b30CONFIG set requirepass '{requirepass}'", "/root/agent/bin/redis-cli  -p {port} aae420ac56ef116058218c11d8b35b30CONFIG set masterauth '{requirepass}'"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_proxy_config": {"description": "update proxy config.", "const_list": {"config_file": "/root/agent/conf/nutcracker_.yml"}, "input_list": [{"name": "config_key", "type": "string", "description": "proxy config key."}, {"name": "config_value", "type": "string", "description": "proxy config value."}, {"name": "insert_position", "type": "string", "description": "proxy config value."}], "common_list": [{"type": "remote_cmd", "cmd_list": ["sed -i '/^ *{config_key}.*$/d' {config_file}", "sed -i '/{insert_position}/a\\ \\ {config_key}: {config_value}' {config_file}", "pid_nut=$(ps -ef| grep /root/agent/bin/nutcracker | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -s SIGUSR1 $pid_nut"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "remove_proxy_config": {"description": "remove proxy config.", "const_list": {"config_file": "/root/agent/conf/nutcracker_.yml"}, "input_list": [{"name": "config_key", "type": "string", "description": "proxy config key."}], "common_list": [{"type": "remote_cmd", "cmd_list": ["sed -i '/^ *{config_key}.*$/d' {config_file}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "restart_proxy": {"description": "restart proxy.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["pid_nut=$(ps -ef| grep /root/agent/bin/nutcracker | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -9 $pid_nut"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "kill_proxy": {"description": "restart proxy.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["sup_nut=$(ps -ef| grep /root/agent/bin/nutcracker | grep -v supervise |grep -v grep | awk '{{print $3}}')", "pid_nut=$(ps -ef| grep /root/agent/bin/nutcracker | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -9 $pid_nut $sup_nut"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_nutcracker": {"description": "update_nutcracker", "const_list": {"agent_root": "/root/agent"}, "input_list": [{"name": "nutcracker_bin", "type": "string", "description": "Tar file name of agent output."}], "common_list": [{"type": "scp_push", "source": "{nutcracker_bin}", "destination": "{agent_root}/{nutcracker_bin}"}, {"type": "remote_cmd", "cmd_list": ["mv -f /root/agent/bin/nutcracker /root/agent/bin/nutcracker_bak && cp {agent_root}/{nutcracker_bin} /root/agent/bin/nutcracker && mv -f /root/agent/bin/nutcracker-noslot /root/agent/bin/nutcracker-noslot-bak && cp -f {agent_root}/{nutcracker_bin} /root/agent/bin/nutcracker-noslot", "pid_nut=$(ps -ef| grep /root/agent/bin/nutcracker | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -9 $pid_nut"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "start_agent": {"description": "set redis password.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["nohup python /root/agent/src/agent.py start &"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_nutcracker_slot": {"description": "Update nutcracker_slot_conf.", "const_list": {"agent_root": "/root/agent"}, "input_list": [{"name": "nutcracker_slot_conf", "type": "string", "description": "Tar file name of agent output."}], "common_list": [{"type": "scp_push", "source": "{nutcracker_slot_conf}", "destination": "{agent_root}/{nutcracker_slot_conf}"}, {"type": "remote_cmd", "cmd_list": ["mv {agent_root}/conf/nutcracker-slot.yml {agent_root}/conf/nutcracker-slot.yml_bak", "cp {agent_root}/{nutcracker_slot_conf} {agent_root}/conf/nutcracker-slot.yml"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "state_reset": {"description": "reset state", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["echo 0 > /root/agent/recover/CURRENT_STATE"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "recover_proxy_bin": {"description": "restart proxy.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["mv -f /root/agent/bin/nutcracker /root/agent/bin/nutcracker_bak_{timestamp} && cp -rf /root/agent/bin/nutcracker-slot /root/agent/bin/nutcracker"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "kill_redis_server": {"description": "kill redis-server", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef | grep redis-server | grep -v grep | awk '{{print $2}}' | xargs kill -9"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "do_bgsave": {"description": "do bgsave", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["bgsave=`cat /root/agent/conf/redis_.conf  | grep rename-command | grep BGSAVE | awk '{{print $3}}'`", "/root/agent/bin/redis-cli $bgsave "]}], "err_list": [], "clean_list": [], "rollback_list": []}, "download_redis_log": {"description": "download_redis_log", "const_list": {}, "input_list": [], "common_list": [{"type": "scp_pull", "source": "/root/agent/log/redis_6379.log.2019060400", "destination": "./remote_logs/redis_log.{cluster_id}"}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_nutcracker_bin_and_conf": {"description": "Update nutcracker_slot_conf.", "const_list": {"agent_root": "/root/agent"}, "input_list": [{"name": "nutcracker_bin_conf", "type": "string", "description": "Tar file name of agent output."}], "common_list": [{"type": "scp_push", "source": "{nutcracker_bin_conf}", "destination": "{agent_root}/{nutcracker_bin_conf}"}, {"type": "remote_cmd", "cmd_list": ["cd {agent_root} && tar -zxvf {nutcracker_bin_conf}", "cp -r {agent_root}/bin {agent_root}/bin_{timestamp} && cp -r {agent_root}/conf {agent_root}/conf_{timestamp}", "rm -rf {agent_root}/bin/nutcracker", "cp -rf {agent_root}/output/bin/nutcracker {agent_root}/bin/nutcracker && cp -rf {agent_root}/output/bin/nutcracker {agent_root}/bin/nutcracker-slot", "cp -rf {agent_root}/output/conf/nutcracker-slot.yml {agent_root}/conf/nutcracker-slot.yml", "cp -rf {agent_root}/output/conf/nutcracker-slot.yml {agent_root}/conf/nutcracker.yml", "echo 0 > {agent_root}/recover/CURRENT_STATE", "kill -9 `cat /root/agent/agent.pid` ", "pid_sup=$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/agent -f 'nohup python /root/agent/src/agent.py start' &", "pid_agent=$(ps -ef| grep /root/agent/src/agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_agent -lt 1 ]] && nohup python /root/agent/src/agent.py start &"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "check_pbrpc_dir": {"description": "Check pbrpc new directory.", "const_list": {"pbrpc_dir": "/usr/lib/python2.6/site-packages", "sh_location": "/root/opbin"}, "input_list": [{"name": "check_pbrpc_sh", "type": "string", "description": "the shell script name"}, {"name": "pbrpc_package_name", "type": "string", "description": "the pbrpc package name"}], "common_list": [{"type": "scp_push", "source": "{check_pbrpc_sh}", "destination": "{sh_location}/{check_pbrpc_sh}"}, {"type": "remote_cmd", "cmd_list": ["sh {sh_location}/{check_pbrpc_sh} {pbrpc_dir}/{pbrpc_package_name}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "check_csagent_exist": {"description": "Check csagent process whether exist.", "const_list": {"sh_location": "/root/opbin"}, "input_list": [{"name": "check_csagent_sh", "type": "string", "description": "the shell script location of check_csagent"}], "common_list": [{"type": "scp_push", "source": "{check_csagent_sh}", "destination": "{sh_location}/{check_csagent_sh}"}, {"type": "remote_cmd", "cmd_list": ["sh {sh_location}/{check_csagent_sh}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "check_agent_exist": {"description": "Check agent process whether exist.", "const_list": {"sh_location": "/root/opbin"}, "input_list": [{"name": "check_agent_sh", "type": "string", "description": "the shell script location of check_csagent"}], "common_list": [{"type": "scp_push", "source": "{check_agent_sh}", "destination": "{sh_location}/{check_agent_sh}"}, {"type": "remote_cmd", "cmd_list": ["sh {sh_location}/{check_agent_sh}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "check_supervise_and_pull_agent": {"description": "Check supervisor process whether exist, and execute command.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["supervise_pid=$(ps -ef | grep '/root/supervise.centos -p /root/status/agent' | grep -v grep | awk '{{print $2}}' | wc -l); [[ $supervise_pid -lt 1 ]] && /root/supervise.centos -p /root/status/agent -f 'nohup python /root/agent/src/agent.py start' >/root/agent/log/nohup_agent.log 2>&1 &"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "reboot_vm": {"description": "Reboot Vm.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["reboot"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_proxy": {"description": "Update nutcracker_slot_bin.", "const_list": {"agent_root": "/root/agent/bin"}, "input_list": [{"name": "nutcracker_bin", "type": "string", "description": "file name of proxy."}], "common_list": [{"type": "scp_push", "source": "{nutcracker_bin}", "destination": "{agent_root}/{nutcracker_bin}"}, {"type": "remote_cmd", "cmd_list": ["cd {agent_root} && mv nutcracker-slot nutcracker-slot-old && mv nutcracker nutcracker-old && cp -rf nutcracker-new nutcracker-slot && cp -rf nutcracker-new nutcracker"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_sync_agent_bin": {"description": "Update sync agent bin", "const_list": {"agent_root": "/root/agent"}, "input_list": [{"name": "sync_agent_pkg", "type": "string", "description": "sync agent pkg"}], "common_list": [{"type": "scp_push", "source": "{sync_agent_pkg}", "destination": "{agent_root}/{sync_agent_pkg}"}, {"type": "remote_cmd", "cmd_list": ["cd {agent_root} && mkdir sync_agent_pkg_{timestamp} && tar -zxvf {sync_agent_pkg} -C sync_agent_pkg_{timestamp}", "mv {agent_root}/bin/sync-agent {agent_root}/bin/sync-agent_{timestamp}", "mv {agent_root}/sync_agent_pkg_{timestamp}/output/bin/sync-agent {agent_root}/bin/sync-agent", "ps -ef | grep sync-agent | grep -v grep | grep -v /root/supervise.centos | awk '{{print $2}}' | xargs kill -9", "rm -rf {agent_root}/sync_agent_pkg_{timestamp} {agent_root}/{sync_agent_pkg}", "sh -c \"agent_count=$(ps -ef | grep sync-agent | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["mv {agent_root}/bin/sync-agent {agent_root}/bin/sync-agent_rollback_{timestamp}", "mv {agent_root}/bin/sync-agent_{timestamp} {agent_root}/bin/sync-agent", "ps -ef | grep sync-agent | grep -v grep | grep -v /root/supervise.centos | awk '{{print $2}}' | xargs kill -9", "sh -c \"agent_count=$(ps -ef | grep sync-agent | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}]}, "update_container_sync_agent_bin": {"description": "Update sync agent bin", "const_list": {"bcc_root": "/root", "agent_root": "/root/agent"}, "input_list": [{"name": "sync_agent_pkg", "type": "string", "description": "sync agent pkg"}], "common_list": [{"type": "scp_push", "source": "{sync_agent_pkg}", "destination": "{bcc_root}/{sync_agent_pkg}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{sync_agent_pkg} {uuid}:{agent_root}/{sync_agent_pkg}", "docker exec {uuid} /bin/bash -c 'cd {agent_root} && mkdir sync_agent_pkg_{timestamp} && tar -zxvf {sync_agent_pkg} -C sync_agent_pkg_{timestamp}'", "docker exec {uuid} /bin/bash -c 'mv {agent_root}/bin/sync-agent {agent_root}/bin/sync-agent_{timestamp}'", "docker exec {uuid} /bin/bash -c 'mv {agent_root}/sync_agent_pkg_{timestamp}/output/bin/sync-agent {agent_root}/bin/sync-agent'", "docker exec {uuid} /bin/bash -c \"ps -ef | grep sync-agent | grep -v grep | grep -v /root/supervise.centos | awk '{{print \\$2}}' | xargs kill -9\"", "docker exec {uuid} /bin/bash -c 'sh -c \"agent_count=\\$(ps -ef | grep sync-agent | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\"'"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{sync_agent_pkg}", "docker exec {uuid} /bin/bash -c 'rm -rf {agent_root}/sync_agent_pkg_{timestamp} {agent_root}/{sync_agent_pkg}'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'mv {agent_root}/bin/sync-agent {agent_root}/bin/sync-agent_rollback_{timestamp}'", "docker exec {uuid} /bin/bash -c 'mv {agent_root}/bin/sync-agent_{timestamp} {agent_root}/bin/sync-agent'", "docker exec {uuid} /bin/bash -c \"ps -ef | grep sync-agent | grep -v grep | grep -v /root/supervise.centos | awk '{{print \\$2}}' | xargs kill -9\"", "docker exec {uuid} /bin/bash -c 'sh -c \"agent_count=\\$(ps -ef | grep sync-agent | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\"'"]}]}, "update_sync_agent_conf": {"description": "Update sync agent conf", "const_list": {"agent_root": "/root/agent", "conf_root": "/root/agent/conf"}, "input_list": [{"name": "conf_file", "type": "string", "description": "sync agent conf file"}], "common_list": [{"type": "scp_push", "source": "{conf_file}", "destination": "{agent_root}/{conf_file}"}, {"type": "remote_cmd", "cmd_list": ["cd {conf_root}", "mv sync.conf sync.conf_{timestamp}", "mv {agent_root}/{conf_file} sync.conf", "echo -e 'operate_timeout=2\nsecond_exec_enable=false\nsecond_exec_delay_ms=5000\nsecond_exec_cmds=del\nlog_dir=/root/agent/log\nlog_split_type=date\nserver_type=redis' >> sync_.conf "]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_gcc82_opbin": {"description": "Update gcc82 & opbin.", "const_list": {"gcc82_dir": "/opt/compiler", "opbin_dir": "/root"}, "input_list": [{"name": "gcc82_tar", "type": "string", "description": "tar file name of gcc82."}, {"name": "opbin_tar", "type": "string", "description": "tar file name of opbin."}], "common_list": [{"type": "scp_push", "source": "{gcc82_tar}", "destination": "{gcc82_dir}/{gcc82_tar}"}, {"type": "scp_push", "source": "{opbin_tar}", "destination": "{opbin_dir}/{opbin_tar}"}, {"type": "remote_cmd", "cmd_list": ["cd {opbin_dir} && rm -rf opbin && tar -zxf {opbin_tar}", "cd {opbin_dir} && rm -rf  {opbin_tar}", "cd {gcc82_dir} && if [ ! -d gcc-8.2 ]; then tar -zxf {gcc82_tar}; fi", "cd {gcc82_dir} && rm -rf {gcc82_tar}"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "install_eventlet": {"description": "install eventlet.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["sudo yum -y install python-eventlet.noarch", "sudo yum -y install python2-eventlet.noarch"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_pbrpc": {"description": "Update pbrpc", "const_list": {"pbrpc_dir": "/tmp"}, "input_list": [{"name": "pbrpc_tar", "type": "string", "description": "tar file name of pbrpc."}], "common_list": [{"type": "scp_push", "source": "{pbrpc_tar}", "destination": "{pbrpc_dir}/{pbrpc_tar}"}, {"type": "remote_cmd", "cmd_list": ["cd {pbrpc_dir} && tar -xzvf {pbrpc_tar} && cd pbrpc_3-6-264-2_BRANCH && sh build.sh &"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_monitor_agent": {"description": "Update scs monitor-agent.", "const_list": {"monitor_agent_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of monitor-agent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{monitor_agent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {monitor_agent_root} && tar -zxvf {tar_file_name}", "cp -r {monitor_agent_root}/monitor-agent/monitors  {monitor_agent_root}/monitor-agent/monitors_bak_{timestamp}", "cp -r {monitor_agent_root}/monitor-agent/conf  {monitor_agent_root}/monitor-agent/conf_bak_{timestamp}", "rm -rf {monitor_agent_root}/monitor-agent/monitors && cp -r {monitor_agent_root}/output/monitors {monitor_agent_root}/monitor-agent/monitors", "rm -rf {monitor_agent_root}/monitor-agent/conf && cp -r {monitor_agent_root}/output/conf {monitor_agent_root}/monitor-agent/conf", "rm -rf {monitor_agent_root}/monitor-agent/.scm && cp -rf {monitor_agent_root}/output/.scm {monitor_agent_root}/monitor-agent/", "supervise_pid=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep supervise |grep -v grep | awk '{{print $2}}')", "kill -9 $supervise_pid", "monitor_pid=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}')", "kill -9 $monitor_pid", "python27_env_check=0;/root/Python-2.7.14/bin/python -c 'import requests' && python27_env_check=1", "if [ $python27_env_check -eq 1 ]; then pid_sup=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/monitor_agent -f 'nohup /root/Python-2.7.14/bin/python /root/monitor-agent/monitors/monitor_agent.py' & fi", "if [ $python27_env_check -eq 1 ]; then pid_agent=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_agent -lt 1 ]] && nohup /root/Python-2.7.14/bin/python /root/monitor-agent/monitors/monitor_agent.py & fi", "if [ $python27_env_check -eq 0 ]; then pid_sup=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/monitor_agent -f 'nohup python /root/monitor-agent/monitors/monitor_agent.py' & fi", "if [ $python27_env_check -eq 0 ]; then pid_agent=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_agent -lt 1 ]] && nohup python /root/monitor-agent/monitors/monitor_agent.py & fi"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {monitor_agent_root}/output {monitor_agent_root}/{tar_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {monitor_agent_root}/monitor-agent/monitors && mv {monitor_agent_root}/monitor-agent/monitors_bak_{timestamp} {monitor_agent_root}/monitor-agent/monitors", "rm -rf {monitor_agent_root}/monitor-agent/conf && mv {monitor_agent_root}/monitor-agent/conf_bak_{timestamp} {monitor_agent_root}/monitor-agent/conf", "ps -ef | grep /root/monitor-agent/monitors/monitor_agent.py | grep -v grep | awk '{{print $2}}' | xargs kill -9", "python27_env_check=0;/root/Python-2.7.14/bin/python -c 'import requests' && python27_env_check=1", "if [ $python27_env_check -eq 1 ]; then nohup /root/Python-2.7.14/bin/python /root/monitor-agent/monitors/monitor_agent.py & fi", "if [ $python27_env_check -eq 0 ]; then nohup python /root/monitor-agent/monitors/monitor_agent.py & fi"]}]}, "update_cron": {"description": "Update scs cron.", "const_list": {"cron_root": "/root/cron"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of cron output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{cron_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {cron_root} && tar -zxvf {tar_file_name}", "cp -r {cron_root}/scs_cron  {cron_root}/src_bak_{timestamp}", "rm -rf {cron_root}/scs_cron && cp -r {cron_root}/output/cron/scs_cron {cron_root}/scs_cron", "pid_cron=$(ps -ef| grep /root/cron/bin/cron | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -9 $pid_cron", "pid_sup=$(ps -ef| grep /root/cron/bin/cron | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/cron/status/cron -f 'nohup /root/Python-2.7.14/bin/python /root/cron/bin/cron cron_log_download.json' &", "pid_cron_new=$(ps -ef| grep /root/cron/bin/cron | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_cron_new -lt 1 ]] && nohup /root/Python-2.7.14/bin/python /root/cron/bin/cron cron_log_download.json &"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {cron_root}/output {cron_root}/{tar_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {cron_root}/scs_cron_new && mv {cron_root}/scs_cron {cron_root}/scs_cron_new && mv {cron_root}/src_bak_{timestamp} {cron_root}/scs_cron", "ps -ef | grep /root/cron/bin/cron | grep -v grep | awk '{{print $2}}' | xargs kill -9", "pid_cron_new=$(ps -ef| grep /root/cron/bin/cron | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_cron_new -lt 1 ]] && nohup /root/Python-2.7.14/bin/python /root/cron/bin/cron cron_log_download.json &"]}]}, "update_python_env": {"description": "Update scs python env.", "const_list": {"py_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of python27."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{py_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {py_root}", "cp -r {py_root}/Python-2.7.14  {py_root}/Python-2.7.14_bak_{timestamp}", "rm -rf {py_root}/Python-2.7.14 && tar -zxvf {tar_file_name}", "pid_cron=$(ps -ef| grep /root/cron/bin/cron | grep -v supervise |grep -v grep | awk '{{print $2}}')", "kill -9 $pid_cron", "pid_sup=$(ps -ef| grep /root/cron/bin/cron | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/cron/status/cron -f 'nohup /root/Python-2.7.14/bin/python /root/cron/bin/cron cron_log_download.json' &", "pid_cron_new=$(ps -ef| grep /root/cron/bin/cron | grep -v supervise | grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_cron_new -lt 1 ]] && nohup /root/Python-2.7.14/bin/python /root/cron/bin/cron cron_log_download.json &"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {py_root}/{tar_file_name}"]}], "rollback_list": []}, "echo_test": {"description": "Update scs python env.", "const_list": {}, "input_list": [], "common_list": [], "err_list": [], "clean_list": [], "rollback_list": []}, "update_xagent": {"description": "Update scs xagent.", "const_list": {"xagent_root": "/root"}, "input_list": [{"name": "image_tar", "type": "string", "description": "Tar file of image."}, {"name": "package_tar", "type": "string", "description": "Tar file of package."}], "common_list": [{"type": "scp_push", "source": "{image_tar}", "destination": "{xagent_root}/{image_tar}"}, {"type": "scp_push", "source": "{package_tar}", "destination": "{xagent_root}/{package_tar}"}, {"type": "remote_cmd", "cmd_list": ["cd {xagent_root}; rm -rf {xagent_root}/xagent_update; mkdir -p {xagent_root}/xagent_update", "mv {xagent_root}/{image_tar} {xagent_root}/xagent_update/{image_tar}", "mv {xagent_root}/{package_tar} {xagent_root}/xagent_update/{package_tar}", "cd {xagent_root}/xagent_update && tar -zxvf {image_tar}", "[[ -d {xagent_root}/Python-2.7.14 ]] || /bin/cp -rf {xagent_root}/xagent_update/Python-2.7.14 {xagent_root}/Python-2.7.14", "/bin/cp -f {xagent_root}/xagent_update/__guard__.sh {xagent_root}/__guard__.sh", "/bin/cp -f {xagent_root}/xagent_update/__init__.sh {xagent_root}/__init__.sh", "[[ -d  {xagent_root}/xagent ]] && cd {xagent_root}/xagent && sh load.sh stop", "cd {xagent_root}; [[ -d  {xagent_root}/xagent ]] && mv {xagent_root}/xagent {xagent_root}/xagent_bak_{timestamp}", "mv {xagent_root}/xagent_update/xagent {xagent_root}/xagent && cd {xagent_root}/xagent && sh load.sh start", "cd {xagent_root}/xagent_update && tar -zxvf {package_tar}", "/bin/cp -f {xagent_root}/xagent_update/controller.sh {xagent_root}/controller.sh", "/bin/cp -rf {xagent_root}/xagent_update/xagent/script/scs {xagent_root}/xagent/script/scs", "echo 0"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": []}], "rollback_list": []}, "update_xagent_script": {"description": "Update scs xagent.", "const_list": {"xagent_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of xagent."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{xagent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {xagent_root}", "rm -rf {xagent_root}/output/output/xagent/script/scs", "rm -rf {xagent_root}/xagent/script/scs_bak_*", "mkdir -p {xagent_root}/output && tar -zxvf {tar_file_name} -C {xagent_root}/output", "mv {xagent_root}/xagent/script/scs  {xagent_root}/xagent/script/scs_bak_{timestamp}", "mv {xagent_root}/output/output/xagent/script/scs {xagent_root}/xagent/script/scs", "[[ -f {xagent_root}/xagent/version.info ]] && mv {xagent_root}/xagent/version.info {xagent_root}/xagent/version.info_{timestamp} ; cp -f {xagent_root}/output/output/xagent/version.info {xagent_root}/xagent/version.info"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {xagent_root}/{tar_file_name}", "rm -rf {xagent_root}/output"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {xagent_root}/xagent/script/scs && mv {xagent_root}/xagent/script/scs_bak_{timestamp} {xagent_root}/xagent/script/scs"]}]}, "update_container_monitor_agent": {"description": "Update scs monitor-agent.", "const_list": {"bcc_root": "/root", "monitor_agent_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of monitor-agent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{monitor_agent_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {monitor_agent_root} && tar -zxvf {tar_file_name}'", "docker exec {uuid} /bin/bash -c 'cp -r {monitor_agent_root}/monitor-agent/monitors  {monitor_agent_root}/monitor-agent/monitors_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'cp -r {monitor_agent_root}/monitor-agent/conf  {monitor_agent_root}/monitor-agent/conf_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'rm -rf {monitor_agent_root}/monitor-agent/monitors && cp -r {monitor_agent_root}/output/monitors {monitor_agent_root}/monitor-agent/monitors'", "docker exec {uuid} /bin/bash -c 'rm -rf {monitor_agent_root}/monitor-agent/conf && cp -r {monitor_agent_root}/output/conf {monitor_agent_root}/monitor-agent/conf'", "docker exec {uuid} /bin/bash -c 'rm -rf {monitor_agent_root}/monitor-agent/.scm && cp -rf {monitor_agent_root}/output/.scm {monitor_agent_root}/monitor-agent/'", "docker exec {uuid} /bin/bash -c \"ps aux |grep monitor_agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\""]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {monitor_agent_root}/output {monitor_agent_root}/{tar_file_name}'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'rm -rf {monitor_agent_root}/monitor-agent/monitors && mv {monitor_agent_root}/monitor-agent/monitors_bak_{timestamp} {monitor_agent_root}/monitor-agent/monitors'", "docker exec {uuid} /bin/bash -c 'rm -rf {monitor_agent_root}/monitor-agent/conf && mv {monitor_agent_root}/monitor-agent/conf_bak_{timestamp} {monitor_agent_root}/monitor-agent/conf'", "docker exec {uuid} /bin/bash -c \"ps aux |grep monitor_agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\""]}]}, "update_container_xagent_script": {"description": "Update scs xagent script for container.", "const_list": {"bcc_root": "/root", "xagent_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of xagent script."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{xagent_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {xagent_root}/xagent/script/scs_bak_*'", "docker exec {uuid} /bin/bash -c 'rm -rf {xagent_root}/output/output/xagent/script/scs'", "docker exec {uuid} /bin/bash -c 'mkdir -p {xagent_root}/output && tar -zxvf {xagent_root}/{tar_file_name} -C {xagent_root}/output'", "docker exec {uuid} /bin/bash -c 'mv {xagent_root}/xagent/script/scs  {xagent_root}/xagent/script/scs_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'mv {xagent_root}/output/output/xagent/script/scs {xagent_root}/xagent/script/scs'", "docker exec {uuid} /bin/bash -c '[[ -f {xagent_root}/xagent/version.info ]] && mv {xagent_root}/xagent/version.info {xagent_root}/xagent/version.info_{timestamp} ; cp -f {xagent_root}/output/output/xagent/version.info {xagent_root}/xagent/version.info"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {xagent_root}/output'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'rm -rf {xagent_root}/xagent/script/scs && mv {xagent_root}/xagent/script/scs_bak_{timestamp} {xagent_root}/xagent/script/scs'"]}]}, "delivery_python_env": {"description": "Update scs python env.", "const_list": {"py_root": "/root", "py_version": "Python-2.7.14"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of python27."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{py_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {py_root}", "count=$(ls -l /root |grep {py_version}|wc -l);[[ $count -lt 2 ]] && tar -zxvf {tar_file_name}"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {py_root}/{tar_file_name}"]}], "rollback_list": []}, "update_python_for_agent": {"description": "Validate scs python env.", "const_list": {"py_root": "/root", "py_version": "Python-2.7.14"}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["sh -c \"/root/Python-2.7.14/bin/python -c 'pass'\"", "redis_server_num=`ps aux | grep redis-server | grep -v grep | wc -l`; [[ $redis_server_num -gt 1 ]] && error", "old_pid_supervise=$(ps -ef| grep /root/agent/src/agent.py | grep supervise | grep -v grep | awk '{{print $2}}')", "kill -9 $old_pid_supervise", "old_pid_agent=$(ps -ef| grep /root/agent/src/agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}')", "kill -9 $old_pid_agent", "pid_sup=$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/agent -f 'nohup /root/Python-2.7.14/bin/python /root/agent/src/agent.py start' &", "sh -c \"agent_count=$(ps -ef | grep /root/agent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["old_pid_supervise=$(ps -ef| grep /root/agent/src/agent.py | grep supervise | grep -v grep | awk '{{print $2}}')", "kill -9 $old_pid_supervise", "old_pid_agent=$(ps -ef| grep /root/agent/src/agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}')", "kill -9 $old_pid_agent", "pid_sup=$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/agent -f 'nohup python /root/agent/src/agent.py start' &", "sh -c \"agent_count=$(ps -ef | grep /root/agent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}]}, "update_agent": {"description": "Update scs agent.", "const_list": {"agent_root": "/root/agent", "restart_agent_sh": "restart_agent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of agent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{agent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {agent_root} && tar -zxvf {tar_file_name}", "redis_server_num=`ps aux | grep redis-server | grep -v grep | wc -l`; [[ $redis_server_num -gt 1 ]] && error", "cp -r {agent_root}/src  {agent_root}/src_bak_{timestamp}", "rm -rf {agent_root}/src && cp -r {agent_root}/output/agent/src {agent_root}/src", "rm -rf {agent_root}/.scm && cp -rf {agent_root}/output/.scm {agent_root}/", "old_pid_agent=$(ps -ef| grep /root/agent/src/agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}')", "kill -9 $old_pid_agent", "pid_sup=$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && mkdir -p /root/agent/status/agent && /root/supervise.centos -p /root/agent/status/agent -f 'nohup /root/Python-2.7.14/bin/python /root/agent/src/agent.py start' &", "sh -c \"agent_count=$(ps -ef | grep /root/agent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {agent_root}/output {agent_root}/{tar_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": []}]}, "update_container_agent": {"description": "Update scs container agent.", "const_list": {"bcc_root": "/root", "agent_root": "/root/agent", "restart_agent_sh": "restart_agent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of agent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{agent_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {agent_root} && tar -zxvf {tar_file_name}'", "docker exec {uuid} /bin/bash -c \"redis_server_num=\\`ps aux | grep redis-server | grep -v grep | wc -l\\`; [[ \\$redis_server_num -gt 1 ]] && error\"", "docker exec {uuid} /bin/bash -c 'cp -r {agent_root}/src  {agent_root}/src_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'rm -rf {agent_root}/src && cp -r {agent_root}/output/agent/src {agent_root}/src'", "docker exec {uuid} /bin/bash -c 'rm -rf {agent_root}/.scm && cp -rf {agent_root}/output/.scm {agent_root}/'", "docker exec {uuid} /bin/bash -c \"ps aux |grep /root/agent/src/agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\"", "docker exec {uuid} /bin/bash -c \"pid_sup=\\$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print \\$2}}' | wc -l); [[ \\$pid_sup -lt 1 ]] && mkdir -p /root/agent/status/agent && /root/supervise.centos -p /root/agent/status/agent -f 'nohup /root/Python-2.7.14/bin/python /root/agent/src/agent.py start' &\"", "docker exec {uuid} /bin/bash -c 'sh -c \"agent_count=\\$(ps -ef | grep /root/agent/src/agent.py | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\"'"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {agent_root}/output {agent_root}/{tar_file_name}'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'rm -rf {agent_root}/src_new && mv {agent_root}/src {agent_root}/src_new && mv {agent_root}/src_bak_{timestamp} {agent_root}/src'", "docker exec {uuid} /bin/bash -c \"ps aux |grep /root/agent/src/agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\"", "docker exec {uuid} /bin/bash -c \"pid_sup=\\$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print \\$2}}' | wc -l); [[ \\$pid_sup -lt 1 ]] && /root/supervise.centos -p /root/status/agent -f 'nohup /root/Python-2.7.14/bin/python /root/agent/src/agent.py start' &\"", "docker exec {uuid} /bin/bash -c 'sh -c \"agent_count=\\$(ps -ef | grep /root/agent/src/agent.py | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\"'"]}]}, "restart_container_all_agents": {"description": "Restart scs container all agent.", "const_list": {"script_root": "/root"}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'sh {script_root}/controller.sh restart;if [ $? -gt 0 ]; then exit 2; fi'"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_aof_handler": {"description": "Update scs aof handler.", "const_list": {"opbin_root": "/root/opbin"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of opbin output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{opbin_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {opbin_root} && tar -zxvf {tar_file_name}", "mv {opbin_root}/scs_clear_aof.sh  {opbin_root}/scs_clear_aof.sh_bak_{timestamp}", "rm -rf {opbin_root}/handle_aof_file.py", "rm -rf {opbin_root}/get_syncagent_latest_opid.py", "cp -r {opbin_root}/opbin/scs_clear_aof.sh  {opbin_root}/", "cp -r {opbin_root}/opbin/handle_aof_file.py  {opbin_root}/", "cp -r {opbin_root}/opbin/get_syncagent_latest_opid.py  {opbin_root}/"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {opbin_root}/opbin {opbin_root}/{tar_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["cp -r  {opbin_root}/scs_clear_aof.sh_bak_{timestamp} {opbin_root}/scs_clear_aof.sh"]}]}, "update_container_aof_handler": {"description": "Update scs container aof_handler.", "const_list": {"bcc_root": "/root", "opbin_root": "/root/opbin"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of aof_handler output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{opbin_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {opbin_root} && tar -zxvf {tar_file_name}'", "docker exec {uuid} /bin/bash -c 'mv {opbin_root}/scs_clear_aof.sh  {opbin_root}/scs_clear_aof.sh_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'rm -rf {opbin_root}/handle_aof_file.py'", "docker exec {uuid} /bin/bash -c 'rm -rf {opbin_root}/get_syncagent_latest_opid.py'", "docker exec {uuid} /bin/bash -c 'cp -r {opbin_root}/opbin/scs_clear_aof.sh  {opbin_root}/'", "docker exec {uuid} /bin/bash -c 'cp -r {opbin_root}/opbin/handle_aof_file.py  {opbin_root}/'", "docker exec {uuid} /bin/bash -c 'cp -r {opbin_root}/opbin/get_syncagent_latest_opid.py  {opbin_root}/'"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {opbin_root}/opbin {opbin_root}/{tar_file_name}'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'cp -r  {opbin_root}/scs_clear_aof.sh_bak_{timestamp} {opbin_root}/scs_clear_aof.sh'"]}]}, "install_python_package": {"description": "Install scs python package.", "const_list": {"py_root": "/root", "py_version": "Python-2.7.14"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of package."}, {"name": "package_name", "type": "string", "description": "Name of package to import."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{py_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {py_root} && mkdir -p {py_root}/python_package_output && tar -zxvf {py_root}/{tar_file_name} -C {py_root}/python_package_output", "cd {py_root}/python_package_output && cd `ls` && /root/Python-2.7.14/bin/python setup.py install", "sh -c \"status=1;/root/Python-2.7.14/bin/python -c 'import {package_name}' && status=0;if [ \\$status -gt 0 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {py_root}/{tar_file_name}", "rm -rf {py_root}/python_package_output"]}], "rollback_list": []}, "install_container_python_package": {"description": "Install scs python package.", "const_list": {"py_root": "/root", "py_version": "Python-2.7.14", "bcc_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of package."}, {"name": "package_name", "type": "string", "description": "Name of package to import."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{py_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {py_root} && mkdir -p {py_root}/python_package_output && tar -zxvf {tar_file_name} -C {py_root}/python_package_output'", "docker exec {uuid} /bin/bash -c \"cd {py_root}/python_package_output && cd \\`ls\\` && /root/Python-2.7.14/bin/python setup.py install\"", "docker exec {uuid} /bin/bash -c 'sh -c \"status=1;/root/Python-2.7.14/bin/python -c \\\"import {package_name}\\\" && status=0;if [ \\$status -gt 0 ]; then exit 2; fi\"'"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -f {py_root}/{tar_file_name}'", "docker exec {uuid} /bin/bash -c 'rm -rf {py_root}/python_package_output'"]}], "rollback_list": []}, "restart_csagent_include_supervise": {"description": "Restart scs csagent.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef | grep /root/csagent/src/agent.py | grep -v grep | awk '{{print $2}}' | xargs kill", "pid_sup=$(ps -ef| grep /root/csagent/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/csagent/status/csagent -f 'nohup /root/Python-2.7.14/bin/python /root/csagent/src/agent.py start' &", "sh -c \"agent_count=$(ps -ef | grep /root/csagent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "restart_monitor_agent_include_supervise": {"description": "Restart scs monitor agent with supervise.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef | grep /root/monitor-agent/monitors/monitor_agent.py | grep -v grep | awk '{{print $2}}' | xargs kill", "pid_sup=$(ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/monitor-agent/status/monitor_agent -f 'nohup /root/Python-2.7.14/bin/python /root/monitor-agent/monitors/monitor_agent.py' &", "sh -c \"agent_count=$(ps -ef | grep /root/monitor-agent/monitors/monitor_agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "restart_agent_include_supervise": {"description": "Restart scs agent.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef | grep /root/agent/src/agent.py | grep -v grep | awk '{{print $2}}' | xargs kill", "pid_sup=$(ps -ef| grep /root/agent/src/agent.py | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/agent/status/agent -f 'nohup /root/Python-2.7.14/bin/python /root/agent/src/agent.py start' &", "sh -c \"agent_count=$(ps -ef | grep /root/agent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "restart_cron_log_download_include_supervise": {"description": "Restart scs /root/cron/bin/cron.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef | grep /root/cron/bin/cron | grep -v grep | awk '{{print $2}}' | xargs kill", "pid_sup=$(ps -ef| grep /root/cron/bin/cron | grep supervise |grep -v grep | awk '{{print $2}}' | wc -l); [[ $pid_sup -lt 1 ]] && /root/supervise.centos -p /root/cron/status/cron -f 'nohup /root/Python-2.7.14/bin/python /root/cron/bin/cron cron_log_download.json' &", "sh -c \"agent_count=$(ps -ef | grep  /root/cron/bin/cron | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "delivery_file_to_overwrite": {"description": "Delivery_file_to_overwrite .", "const_list": {}, "input_list": [{"name": "file_name", "type": "string", "description": "File to scp."}, {"name": "target_file_name", "type": "string", "description": "File to overwrite."}], "common_list": [{"type": "remote_cmd", "cmd_list": ["cp {target_file_name} {target_file_name}_{timestamp}"]}, {"type": "scp_push", "source": "{file_name}", "destination": "{target_file_name}"}], "err_list": [], "clean_list": [], "rollback_list": []}, "check_new_mon_type": {"description": "Check push new mon type.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["sh -c \"type_count=$(grep -w type /root/agent/data/mon_json.value | wc -l); if [ \\$type_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "check_container_new_mon_type": {"description": "Check push new mon type.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'sh -c \"type_count=$(grep -w type /root/agent/data/mon_json.value | wc -l); if [ \\$type_count -lt 1 ]; then exit 2; fi\"'"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "update_init_file": {"description": "Update file.", "const_list": {"file_root": "/root"}, "input_list": [{"name": "file_name", "type": "string", "description": "Name of file."}], "common_list": [{"type": "scp_push", "source": "{file_name}", "destination": "{file_root}/{file_name}.new"}, {"type": "remote_cmd", "cmd_list": ["cd {file_root} && mv -f {file_root}/{file_name} {file_root}/{file_name}.20231106", "mv -f {file_root}/{file_name}.new {file_root}/{file_name}", "sh -c \"value=$(grep start_servers /root/__init__.sh|grep -v '#'|wc -l);if [ \\$value -gt 0 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "start_smartdba_filebeat": {"description": "Start filebeat", "const_list": {"bcc_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of smartdba filebeat."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["count=$(ps aux |grep filebeat|grep -v grep |wc -l);if [ $count -lt 1 ]; then tar -zxvf {bcc_root}/{tar_file_name}; fi", "count=$(ps aux |grep filebeat|grep -v grep |wc -l);if [ $count -lt 1 ]; then mkdir -p /root/smartdba/filebeat/status; fi", "count=$(ps aux |grep filebeat|grep -v grep |wc -l);if [ $count -lt 1 ]; then chmod a+x /root/smartdba/filebeat/filebeat; fi", "count=$(ps aux |grep filebeat|grep -v grep |wc -l);if [ $count -lt 1 ]; then /root/supervise.centos -p /root/smartdba/filebeat/status/filebeat -f \"nohup /root/smartdba/filebeat/filebeat run --path.home=/root/smartdba/filebeat -c=/root/smartdba/filebeat/filebeat.yml\" /dev/null 2>&1 \\&; fi"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}"]}], "rollback_list": []}, "update_smartdba_script": {"description": "Update scs smartdba script.", "const_list": {"xagent_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of xagent script."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{xagent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {xagent_root}", "mkdir -p {xagent_root}/output && tar -zxvf {tar_file_name} -C {xagent_root}/output", "if [ -d {xagent_root}/xagent/script/smartdba_log_service ]; then mv {xagent_root}/xagent/script/smartdba_log_service  {xagent_root}/xagent/script/smartdba_log_service_bak_{timestamp};fi", "mv {xagent_root}/output/output/xagent/script/smartdba_log_service {xagent_root}/xagent/script/smartdba_log_service"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {xagent_root}/{tar_file_name}", "rm -rf {xagent_root}/output"]}], "rollback_list": []}, "pkm_update_csagent": {"description": "Update scs csagent.", "const_list": {"csagent_root": "/root/csagent"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of csagent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{csagent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {csagent_root} && tar -zxvf {tar_file_name}", "redis_server_num=`ps aux | grep redis-server | grep -v grep | wc -l`; [[ $redis_server_num -gt 1 ]] && error", "cp -r {csagent_root}/src  {csagent_root}/src_bak_{timestamp}", "rm -rf {csagent_root}/src && cp -r {csagent_root}/output/csagent/src {csagent_root}/src", "/root/csagent/controller restart"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {csagent_root}/output", "rm -f {csagent_root}/{tar_file_name}"]}], "rollback_list": []}, "pkm_update_container_csagent": {"description": "Update scs csagent.", "const_list": {"bcc_root": "/root", "csagent_root": "/root/csagent"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of csagent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{csagent_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {csagent_root} && tar -zxvf {tar_file_name}'", "docker exec {uuid} /bin/bash -c \"redis_server_num=\\`ps aux | grep redis-server | grep -v grep | wc -l\\`; [[ \\$redis_server_num -gt 1 ]] && error\"", "docker exec {uuid} /bin/bash -c 'cp -r {csagent_root}/src  {csagent_root}/src_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/src && cp -r {csagent_root}/output/csagent/src {csagent_root}/src'", "docker exec {uuid} /bin/bash -c '/root/csagent/controller restart"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/output'", "docker exec {uuid} /bin/bash -c 'rm -f {csagent_root}/{tar_file_name}'"]}], "rollback_list": []}, "restart_csagent_python27": {"description": "Update scs csagent.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef | grep /root/csagent/src/agent.py|grep supervise | grep -v grep | awk '{{print $2}}' | xargs kill -9", "ps -ef | grep /root/csagent/src/agent.py|grep -v supervise | grep -v grep | awk '{{print $2}}' | xargs kill -9", "mkdir -p /root/csagent/status/csagent", "/root/supervise.centos -p /root/csagent/status/csagent -f \"nohup /root/Python-2.7.14/bin/python /root/csagent/src/agent.py start\" &", "sh -c \"agent_count=$(ps -ef | grep /root/csagent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "restart_monitor_agent_python27": {"description": "Update scs monitor-agent.", "const_list": {"monitor_agent_root": "/root"}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep supervise |grep -v grep | awk '{{print $2}}'|xargs kill -9", "ps -ef| grep /root/monitor-agent/monitors/monitor_agent.py | grep -v supervise | grep -v grep | awk '{{print $2}}' |xargs kill -9", "mkdir -p /root/monitor-agent/status/monitor_agent", "/root/supervise.centos -p /root/monitor-agent/status/monitor_agent -f \"nohup /root/Python-2.7.14/bin/python /root/monitor-agent/monitors/monitor_agent.py\" &", "sh -c \"agent_count=$(ps -ef | grep /root/monitor-agent/monitors/monitor_agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [], "rollback_list": []}, "pkm_update_monitor_agent": {"description": "Update scs monitor-agent.", "const_list": {"monitor_agent_root": "/root"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of monitor-agent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{monitor_agent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {monitor_agent_root} && tar -zxvf {tar_file_name}", "cp -r {monitor_agent_root}/monitor-agent/monitors  {monitor_agent_root}/monitor-agent/monitors_bak_{timestamp}", "cp -r {monitor_agent_root}/monitor-agent/conf  {monitor_agent_root}/monitor-agent/conf_bak_{timestamp}", "rm -rf {monitor_agent_root}/monitor-agent/monitors && cp -r {monitor_agent_root}/output/monitors {monitor_agent_root}/monitor-agent/monitors", "rm -rf {monitor_agent_root}/monitor-agent/conf && cp -r {monitor_agent_root}/output/conf {monitor_agent_root}/monitor-agent/conf", "/root/monitor-agent/controller restart"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {monitor_agent_root}/output {monitor_agent_root}/{tar_file_name}"]}], "rollback_list": []}, "pkm_update_agent": {"description": "Update scs agent.", "const_list": {"agent_root": "/root/agent", "restart_agent_sh": "restart_agent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of agent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{agent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {agent_root} && tar -zxvf {tar_file_name}", "redis_server_num=`ps aux | grep redis-server | grep -v grep | wc -l`; [[ $redis_server_num -gt 1 ]] && error", "cp -r {agent_root}/src  {agent_root}/src_bak_{timestamp}", "rm -rf {agent_root}/src && cp -r {agent_root}/output/agent/src {agent_root}/src", "/root/agent/controller restart"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {agent_root}/output {agent_root}/{tar_file_name}"]}], "rollback_list": []}, "update_csagent_python27": {"description": "Update scs csagent.", "const_list": {"csagent_root": "/root/csagent", "restart_csagent_sh": "restart_csagent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of csagent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{csagent_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["cd {csagent_root} && tar -zxvf {tar_file_name}", "cp -r {csagent_root}/src  {csagent_root}/src_bak_{timestamp}", "rm -rf {csagent_root}/src && cp -r {csagent_root}/output/csagent/src {csagent_root}/src", "rm -rf {csagent_root}/.scm && cp -rf {csagent_root}/output/.scm {csagent_root}/", "ps -ef | grep /root/csagent/src/agent.py|grep supervise | grep -v grep | awk '{{print $2}}' | xargs kill -9", "ps -ef | grep /root/csagent/src/agent.py|grep -v supervise | grep -v grep | awk '{{print $2}}' | xargs kill -9", "mkdir -p /root/csagent/status/csagent", "/root/supervise.centos -p /root/csagent/status/csagent -f \"nohup /root/Python-2.7.14/bin/python /root/csagent/src/agent.py start\" &", "sh -c \"agent_count=$(ps -ef | grep /root/csagent/src/agent.py | grep -v grep | grep -v supervise.centos | awk '{{print $2}}' | wc -l); if [ \\$agent_count -lt 1 ]; then exit 2; fi\""]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -rf {csagent_root}/output {csagent_root}/{tar_file_name}"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": []}]}, "update_container_csagent_python27": {"description": "Update scs csagent.", "const_list": {"bcc_root": "/root", "csagent_root": "/root/csagent", "restart_csagent_sh": "restart_csagent.sh"}, "input_list": [{"name": "tar_file_name", "type": "string", "description": "Tar file name of csagent output."}], "common_list": [{"type": "scp_push", "source": "{tar_file_name}", "destination": "{bcc_root}/{tar_file_name}"}, {"type": "remote_cmd", "cmd_list": ["docker cp {bcc_root}/{tar_file_name} {uuid}:{csagent_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'cd {csagent_root} && tar -zxvf {tar_file_name}'", "docker exec {uuid} /bin/bash -c \"redis_server_num=\\`ps aux | grep redis-server | grep -v grep | wc -l\\`; [[ \\$redis_server_num -gt 1 ]] && error\"", "docker exec {uuid} /bin/bash -c 'cp -r {csagent_root}/src  {csagent_root}/src_bak_{timestamp}'", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/src && cp -r {csagent_root}/output/csagent/src {csagent_root}/src'", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/.scm && cp -rf {csagent_root}/output/.scm {csagent_root}/'", "docker exec {uuid} /bin/bash -c \"ps aux |grep {csagent_root}/src/agent.py |grep -v grep |grep -v supervise |awk '{{print \\$2}}'|xargs kill\"", "docker exec {uuid} /bin/bash -c \"/root/supervise.centos -p /root/csagent/status/csagent -f 'nohup /root/Python-2.7.14/bin/python /root/csagent/src/agent.py start' &\"", "docker exec {uuid} /bin/bash -c 'sh -c \"pid_csagent=\\$(ps -ef | grep {csagent_root}/src/agent.py | grep -v grep | grep -v supervise.centos | awk \\\"{{print \\$2}}\\\" | wc -l); if [ \\pid_csagent -lt 1 ]; then exit 2; fi\"'"]}], "err_list": [], "clean_list": [{"type": "remote_cmd", "cmd_list": ["rm -f {bcc_root}/{tar_file_name}", "docker exec {uuid} /bin/bash -c 'rm -rf {csagent_root}/output {csagent_root}/{tar_file_name}'"]}], "rollback_list": [{"type": "remote_cmd", "cmd_list": []}]}, "fix_shard_id": {"description": "fix_shard_id.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["cp /root/agent/recover/redis_conf.txt /root/agent/recover/redis_conf.txt.20240531", "sed -i '/redis_shard_id/s/ 0,/ {shard_id},/' /root/agent/recover/redis_conf.txt"]}], "err_list": [], "clean_list": [], "rollback_list": []}, "fix_container_shard_id": {"description": "fix_container_shard_id.", "const_list": {}, "input_list": [], "common_list": [{"type": "remote_cmd", "cmd_list": ["docker exec {uuid} /bin/bash -c 'cp /root/agent/recover/redis_conf.txt /root/agent/recover/redis_conf.txt.20240531'", "docker exec {uuid} /bin/bash -c \" sed -i '/redis_shard_id/s/ 0,/ {shard_id},/' /root/agent/recover/redis_conf.txt\""]}], "err_list": [], "clean_list": [], "rollback_list": []}}