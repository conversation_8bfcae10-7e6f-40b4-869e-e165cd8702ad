drop table if exists application;
create table if not exists application
(
    id                         bigint auto_increment
        primary key,
    app_id                     varchar(64)   default ''                    not null,
    app_name                   varchar(256)  default ''                    not null,
    product                    varchar(32)   default ''                    not null,
    type                       varchar(32)   default ''                    not null,
    app_mode                   varchar(32)   default ''                    not null,
    pool                       varchar(256)  default ''                    not null,
    port                       int           default 0                     not null,
    azone                      varchar(32)   default ''                    not null,
    rzone                      varchar(32)   default ''                    not null,
    vpc_id                     varchar(64)   default ''                    not null,
    user_id                    varchar(64)   default ''                    not null,
    zk_host                    varchar(256)  default ''                    not null,
    replicas                   varchar(1024) default ''                    not null,
    status                     varchar(32)   default ''                    not null,
    properties                 varchar(1024) default ''                    not null,
    ip_type                    varchar(32)   default ''                    not null,
    security_group_id          varchar(64)   default ''                    not null,
    internal_security_group_id varchar(64)   default ''                    not null,
    image_id                   varchar(256)  default ''                    not null,
    deploy_set_ids             varchar(1024) default ''                    not null,
    blb_subnet_id              varchar(64)   default ''                    not null,
    app_short_id               bigint        default 0                     not null,
    user_short_id              bigint        default 0                     not null,
    domain                     varchar(256)  default ''                    not null,
    elb_pnetip                 varchar(64)   default ''                    not null,
    elb_ipv6                   varchar(64)   default ''                    not null,
    create_time                datetime      default '0000-00-00 00:00:00' not null,
    update_time                datetime      default '0000-00-00 00:00:00' not null,
    delete_time                datetime      default '0000-00-00 00:00:00' not null,
    semi_status                varchar(32)   default ''                    not null,
    clone_data_access          varchar(256)  default ''                    not null,
    dest_replicas              varchar(1024) default ''                    not null,
    mcpack_port                int           default 0                     not null,
    inner_port                 int           default 0                     not null,
    ports_info                 varchar(1024) default ''                    not null,
    app_group_id               varchar(64)   default ''                    not null,
    app_group_seq_id           int           default 0                     not null,
    local_metaserver           varchar(64)   default ''                    not null,
    global_metaserver          varchar(64)   default ''                    not null,
    region                     varchar(32)   default ''                    not null,
    bns_service                varchar(2000) default ''                    not null,
    resource_type              varchar(32)   default ''                    not null,
    constraint uniq_application_app_id
        unique (app_id)
)
    comment 'application info' engine = InnoDB;

drop table if exists backup;
create table if not exists backup
(
    id        bigint auto_increment
        primary key,
    app_id    varchar(64)   default ''                    not null,
    backup_id varchar(64)   default ''                    not null,
    create_at datetime      default '0000-00-00 00:00:00' not null,
    update_at datetime      default '0000-00-00 00:00:00' not null,
    end_at    datetime      default '0000-00-00 00:00:00' not null,
    expire_at datetime      default '0000-00-00 00:00:00' not null,
    status    varchar(32)   default ''                    not null,
    type      varchar(32)   default ''                    not null,
    comment   varchar(1024) default ''                    not null,
    constraint uniq_backup_backup_id
        unique (backup_id)
)
    comment 'backup info' engine = InnoDB;

create index idx_backup_app_id
    on backup (app_id);

drop table if exists backup_item;
create table if not exists backup_item
(
    id         bigint auto_increment
        primary key,
    app_id     varchar(64)  default ''                    not null,
    cluster_id varchar(64)  default ''                    not null,
    node_id    varchar(64)  default ''                    not null,
    backup_id  varchar(64)  default ''                    not null,
    access     varchar(512) default ''                    not null,
    size       bigint       default 0                     not null,
    create_at  datetime     default '0000-00-00 00:00:00' not null,
    update_at  datetime     default '0000-00-00 00:00:00' not null,
    status     varchar(32)  default ''                    not null
)
    comment 'backup info' engine = InnoDB;

create index idx_backup_app_id
    on backup_item (app_id);

create index idx_backup_backup_id
    on backup_item (backup_id);

create index idx_backup_cluster_id
    on backup_item (cluster_id);

create index idx_backup_node_id
    on backup_item (node_id);

drop table if exists blb;
create table if not exists blb
(
    id                       bigint auto_increment
        primary key,
    app_id                   varchar(64)  default ''                    not null,
    name                     varchar(256) default ''                    not null,
    type                     varchar(32)  default ''                    not null,
    vpc_id                   varchar(64)  default ''                    not null,
    subnet_id                varchar(64)  default ''                    not null,
    bgw_group_id             varchar(64)  default ''                    not null,
    bgw_group_exclusive      int          default 0                     not null,
    bgw_group_mode           varchar(32)  default ''                    null,
    master_az                varchar(32)  default ''                    null,
    slave_az                 varchar(32)  default ''                    null,
    status                   varchar(32)  default ''                    not null,
    blb_id                   varchar(64)  default ''                    not null,
    vip                      varchar(64)  default ''                    not null,
    ovip                     varchar(64)  default ''                    not null,
    ipv6                     varchar(64)  default ''                    not null,
    create_at                datetime     default '0000-00-00 00:00:00' not null,
    update_at                datetime     default '0000-00-00 00:00:00' not null,
    ip_type                  varchar(32)  default ''                    null,
    ro_group_id              varchar(64)  default ''                    not null,
    ip_group_id              varchar(64)  default ''                    not null,
    resource_user_id         varchar(64)  default ''                    not null,
    resource_vpc_id          varchar(64)  default ''                    not null,
    resource_subnet_id       varchar(64)  default ''                    not null,
    service_publish_endpoint varchar(64)  default ''                    not null,
    endpoint_id              varchar(64)  default ''                    not null,
    endpoint_ip              varchar(16)  default ''                    not null
)
    comment 'blb info' engine = InnoDB;

create index idx_blb_app_id
    on blb (app_id);

drop table if exists cluster;
create table if not exists cluster
(
    id                   bigint auto_increment
        primary key,
    cluster_id           varchar(64)                  default ''                    not null,
    app_id               varchar(64)                  default ''                    not null,
    engine               varchar(32)                  default ''                    not null,
    engine_version       varchar(64)                  default ''                    not null,
    engine_minor_version varchar(64)                  default ''                    not null,
    port                 int                          default 0                     not null,
    xagent_sync_port     int                          default 0                     not null,
    node_relation        varchar(64)                  default ''                    not null,
    mem_size             bigint                       default 0                     not null,
    actual_mem_size      bigint                       default 0                     not null,
    disk_size            bigint                       default 0                     not null,
    actual_disk_size     bigint                       default 0                     not null,
    disk_used            bigint                       default 0                     not null,
    cpu                  int                          default 0                     not null,
    actual_cpu           int                          default 0                     not null,
    status               varchar(64)                  default ''                    not null,
    dest_status          varchar(64)                  default ''                    not null,
    remark               text                                                       null,
    properties           varchar(1024)                default ''                    not null,
    store_type           varchar(32)                  default ''                    not null,
    available_volume     int                          default 0                     not null,
    sys_disk_size        bigint                       default 0                     not null,
    sys_disk_used        bigint                       default 0                     not null,
    spec                 varchar(64)                  default ''                    not null,
    dest_spec            varchar(64)                  default ''                    not null,
    cluster_short_id     bigint(11)                   default 0                     not null,
    expire_time          datetime                     default '0000-00-00 00:00:00' null,
    create_time          datetime                     default '0000-00-00 00:00:00' not null,
    update_time          datetime                     default '0000-00-00 00:00:00' not null,
    delete_time          datetime                     default '0000-00-00 00:00:00' not null,
    bns_path             varchar(256)                 default ''                    not null,
    bns                  varchar(256)                 default ''                    not null,
    max_node_index       int                          default 0                     not null,
    global_id            varchar(64) collate utf8_bin default ''                    not null,
    global_seq_id        int                          default 0                     not null,
    constraint uniq_cluster_cluster_id
        unique (cluster_id)
)
    comment 'cluster info' engine = InnoDB;

create index idx_cluster_app_id
    on cluster (app_id);

drop table if exists config;
create table if not exists config
(
    id     bigint auto_increment
        primary key,
    app_id varchar(64)  default '' not null,
    name   varchar(256) default '' not null,
    type   varchar(32)  default '' not null,
    value  longtext                null
)
    comment 'config info' engine = InnoDB;

create index idx_config_app_id
    on config (app_id);

drop table if exists entity;
create table if not exists entity
(
    id          bigint auto_increment
        primary key,
    name        varchar(256) default ''                    not null,
    app_id      varchar(64)  default ''                    not null,
    parent_name varchar(256) default ''                    not null,
    type        varchar(128) default ''                    not null,
    status      varchar(32)  default ''                    not null,
    remark      varchar(256) default ''                    not null,
    etag        int          default 0                     not null,
    create_time datetime     default '0000-00-00 00:00:00' not null,
    update_time datetime     default '0000-00-00 00:00:00' not null,
    properties  longtext                                   null,
    charset     varchar(64)  default ''                    not null
)
    comment 'entity info' engine = InnoDB;

create index idx_entity_app_id
    on entity (app_id);

drop table if exists exp;
create table if not exists exp
(
    id           bigint auto_increment
        primary key,
    strategy_id  varchar(64) default ''                    not null,
    default_flag varchar(64) default ''                    not null,
    create_time  datetime    default '0000-00-00 00:00:00' not null,
    update_time  datetime    default '0000-00-00 00:00:00' not null,
    delete_time  datetime    default '0000-00-00 00:00:00' not null,
    constraint uniq_exp_strategy_id
        unique (strategy_id)
)
    comment 'exp info' engine = InnoDB;

drop table if exists exp_rules;
create table if not exists exp_rules
(
    id          bigint auto_increment
        primary key,
    strategy_id varchar(64) default ''                    not null,
    ranking     int         default 0                     not null,
    rules       longtext                                  null,
    flag        varchar(64) default ''                    not null,
    create_time datetime    default '0000-00-00 00:00:00' not null,
    update_time datetime    default '0000-00-00 00:00:00' not null,
    delete_time datetime    default '0000-00-00 00:00:00' not null
)
    comment 'exp rules' engine = InnoDB;

create index idx_exp_rules_strategy_id
    on exp_rules (strategy_id);

drop table if exists interface;
create table if not exists interface
(
    id                 bigint auto_increment
        primary key,
    interface_id       varchar(64)                  default ''                    not null,
    access_type        varchar(32)                  default ''                    not null,
    engine             varchar(32)                  default ''                    not null,
    engine_version     varchar(64)                  default ''                    not null,
    port               int                          default 0                     not null,
    app_id             varchar(64)                  default ''                    not null,
    proxy_relation     varchar(64)                  default ''                    not null,
    mem_size           bigint                       default 0                     not null,
    actual_mem_size    bigint                       default 0                     not null,
    disk_size          bigint                       default 0                     not null,
    actual_disk_size   bigint                       default 0                     not null,
    disk_used          bigint                       default 0                     not null,
    cpu                int                          default 0                     not null,
    actual_cpu         int                          default 0                     not null,
    status             varchar(32)                  default ''                    not null,
    remark             text                                                       null,
    properties         varchar(1024)                default ''                    not null,
    store_type         varchar(32)                  default ''                    not null,
    available_volume   int(10)                      default 0                     not null,
    sys_disk_size      bigint                       default 0                     not null,
    sys_disk_used      bigint                       default 0                     not null,
    spec               varchar(64)                  default ''                    not null,
    dest_spec          varchar(64)                  default ''                    not null,
    create_time        datetime                     default '0000-00-00 00:00:00' not null,
    update_time        datetime                     default '0000-00-00 00:00:00' not null,
    delete_time        datetime                     default '0000-00-00 00:00:00' not null,
    bns_path           varchar(256)                 default ''                    not null,
    bns                varchar(256)                 default ''                    not null,
    access             varchar(1024)                default ''                    not null,
    resource_id        varchar(64)                  default ''                    not null,
    interface_short_id bigint                       default 0                     not null,
    max_node_index     int                          default 0                     not null,
    mcpack_port        varchar(64) collate utf8_bin default ''                    not null,
    constraint uniq_interface_interface_id
        unique (interface_id)
)
    comment 'interface' engine = InnoDB;

create index idx_interface_app_id
    on interface (app_id);

drop table if exists meta_cluster;
create table if not exists meta_cluster
(
    id              bigint auto_increment
        primary key,
    meta_cluster_id varchar(64)  default ''                    not null,
    name            varchar(256) default ''                    not null,
    `desc`          varchar(256) default ''                    not null,
    status          varchar(32)  default ''                    not null,
    type            varchar(32)  default ''                    not null,
    user_id         varchar(64)  default ''                    not null,
    engine          varchar(32)  default ''                    not null,
    region          varchar(32)  default ''                    not null,
    engine_version  varchar(32)  default ''                    not null,
    entrance        varchar(64)  default ''                    not null,
    cur_master      varchar(64)  default ''                    not null,
    quorum          int          default 0                     not null,
    created_at      timestamp    default '0000-00-00 00:00:00' not null,
    updated_at      timestamp    default '0000-00-00 00:00:00' not null,
    password        varchar(256) default ''                    not null,
    constraint meta_cluster_id
        unique (meta_cluster_id)
)
    engine = InnoDB;

create index idx_meta_cluster_created_at
    on meta_cluster (created_at);

create index idx_meta_cluster_status
    on meta_cluster (status);

drop table if exists meta_node;
create table if not exists meta_node
(
    id                bigint auto_increment
        primary key,
    meta_node_id      varchar(64) default ''                    not null,
    meta_cluster_id   varchar(64) default ''                    not null,
    status            varchar(32) default ''                    not null,
    role              varchar(32) default ''                    not null,
    engine            varchar(32) default ''                    not null,
    engine_version    varchar(32) default ''                    not null,
    port              int         default 0                     not null,
    region            varchar(32) default ''                    not null,
    logical_zone      varchar(32) default ''                    not null,
    azone             varchar(32) default ''                    not null,
    vpc_id            varchar(64) default ''                    not null,
    subnet_id         varchar(64) default ''                    not null,
    base_dir          varchar(64) default ''                    not null,
    data_dir          varchar(64) default ''                    not null,
    ip                varchar(64) default ''                    not null,
    floating_ip       varchar(64) default ''                    not null,
    ipv6              varchar(64) default ''                    not null,
    resource_order_id varchar(64) default ''                    not null,
    resource_id       varchar(64) default ''                    not null,
    created_at        timestamp   default '0000-00-00 00:00:00' not null,
    updated_at        timestamp   default '0000-00-00 00:00:00' not null,
    constraint meta_node_id
        unique (meta_node_id)
)
    engine = InnoDB;

create index idx_meta_node_created_at
    on meta_node (created_at);

create index idx_meta_node_meta_cluster_id
    on meta_node (meta_cluster_id);

create index idx_meta_node_status
    on meta_node (status);

drop table if exists node;
create table if not exists node
(
    id                 bigint auto_increment
        primary key,
    app_id             varchar(64)                  default '' not null,
    cluster_id         varchar(64)                  default '' not null,
    node_id            varchar(64)                  default '' not null,
    container_id       varchar(256)                 default '' not null,
    engine             varchar(32)                  default '' not null,
    engine_version     varchar(64)                  default '' not null,
    port               int                          default 0  not null,
    basedir            varchar(256)                 default '' not null,
    datadir            varchar(256)                 default '' not null,
    innodb_buffer_size int                          default 0  not null,
    ip                 varchar(64)                  default '' not null,
    xagent_port        int                          default 0  not null,
    region             varchar(32)                  default '' not null,
    logic_zone         varchar(32)                  default '' not null,
    azone              varchar(32)                  default '' not null,
    vpc_id             varchar(64)                  default '' not null,
    subnet_id          varchar(64)                  default '' not null,
    pool               varchar(256)                 default '' not null,
    tags               varchar(256)                 default '' not null,
    role               varchar(32)                  default '' not null,
    dest_role          varchar(32)                  default '' not null,
    status             varchar(32)                  default '' not null,
    dest_status        varchar(32)                  default '' not null,
    task_id            int                          default 0  not null,
    properties         varchar(1024)                default '' not null,
    resource_order_id  varchar(64)                  default '' not null,
    resource_id        varchar(64)                  default '' not null,
    floating_ip        varchar(64)                  default '' not null,
    ipv6               varchar(64)                  default '' not null,
    root_password      varchar(256)                 default '' not null,
    node_short_id      bigint                       default 0  not null,
    host_name          varchar(256)                 default '' not null,
    node_fix_id        varchar(256)                 default '' not null,
    temp_flags         varchar(512)                 default '' not null,
    global_id          varchar(64) collate utf8_bin default '' not null,
    global_seq_id      int                          default 0  not null,
    container_name     varchar(64)                  default '' not null,
    constraint uniq_node_node_id
        unique (node_id)
)
    comment 'node infos' engine = InnoDB;

create index idx_node_app_id
    on node (app_id);

create index idx_node_cluster_id
    on node (cluster_id);

drop table if exists proxy;
create table if not exists proxy
(
    id                bigint auto_increment
        primary key,
    proxy_id          varchar(64)   default '' not null,
    ip                varchar(64)   default '' not null,
    port              int           default 0  not null,
    region            varchar(32)   default '' not null,
    logic_zone        varchar(32)   default '' not null,
    azone             varchar(32)   default '' not null,
    vpc_id            varchar(64)   default '' not null,
    xagent_port       int           default 0  not null,
    app_id            varchar(64)   default '' not null,
    properties        varchar(1024) default '' not null,
    basedir           varchar(256)  default '' not null,
    status            varchar(32)   default '' not null,
    dest_status       varchar(32)   default '' not null,
    container_id      varchar(256)  default '' not null,
    interface_id      varchar(64)   default '' not null,
    subnet_id         varchar(64)   default '' not null,
    resource_order_id varchar(64)   default '' not null,
    resource_id       varchar(64)   default '' not null,
    floating_ip       varchar(64)   default '' not null,
    ipv6              varchar(64)   default '' not null,
    root_password     varchar(256)  default '' not null,
    engine            varchar(32)   default '' not null,
    engine_version    varchar(64)   default '' not null,
    proxy_short_id    bigint        default 0  not null,
    host_name         varchar(256)  default '' not null,
    node_fix_id       varchar(256)  default '' not null,
    temp_flags        varchar(512)  default '' not null,
    mcpack_port       varchar(64)   default '' not null,
    global_id         varchar(64)   default '' not null,
    global_seq_id     int           default 0  not null,
    stat_port         int           default 0  not null,
    container_name    varchar(64)   default '' not null,
    constraint uniq_proxy_proxy_id
        unique (proxy_id)
)
    comment 'proxy infos' engine = InnoDB;

create index idx_proxy_app_id
    on proxy (app_id);

create index idx_proxy_interface_id
    on proxy (interface_id);

drop table if exists redis_acl;
create table if not exists redis_acl
(
    id               int auto_increment
        primary key,
    app_id           varchar(64)   default ''                    not null,
    account_name     varchar(256)  default ''                    not null,
    create_at        datetime      default '0000-00-00 00:00:00' not null,
    update_at        datetime      default '0000-00-00 00:00:00' not null,
    version          int           default 0                     not null,
    engine           varchar(32)   default ''                    not null,
    password         varchar(256)  default ''                    not null,
    allowed_cmds     varchar(1024) default ''                    not null,
    allowed_sub_cmds varchar(1024) default ''                    not null,
    key_patterns     varchar(1024) default ''                    not null,
    properties       varchar(1024) default ''                    not null,
    status           varchar(32)   default ''                    not null
)
    comment 'redis acl infos' engine = InnoDB;

create index idx_redis_acl_app_id
    on redis_acl (app_id);

drop table if exists ro_node;
create table if not exists ro_node
(
    id                 bigint auto_increment
        primary key,
    app_id             varchar(64)   default '' not null,
    cluster_id         varchar(64)   default '' not null,
    node_id            varchar(64)   default '' not null,
    container_id       varchar(256)  default '' not null,
    engine             varchar(32)   default '' not null,
    engine_version     varchar(64)   default '' not null,
    port               int           default 0  not null,
    basedir            varchar(256)  default '' not null,
    datadir            varchar(256)  default '' not null,
    innodb_buffer_size int           default 0  not null,
    ip                 varchar(64)   default '' not null,
    xagent_port        int           default 0  not null,
    region             varchar(32)   default '' not null,
    logic_zone         varchar(32)   default '' not null,
    azone              varchar(32)   default '' not null,
    vpc_id             varchar(64)   default '' not null,
    subnet_id          varchar(64)   default '' not null,
    pool               varchar(256)  default '' not null,
    tags               varchar(256)  default '' not null,
    role               varchar(32)   default '' not null,
    dest_role          varchar(32)   default '' not null,
    status             varchar(32)   default '' not null,
    dest_status        varchar(32)   default '' not null,
    task_id            int           default 0  not null,
    properties         varchar(1024) default '' not null,
    resource_order_id  varchar(64)   default '' not null,
    resource_id        varchar(64)   default '' not null,
    floating_ip        varchar(64)   default '' not null,
    ipv6               varchar(64)   default '' not null,
    root_password      varchar(256)  default '' not null,
    node_short_id      bigint        default 0  not null,
    host_name          varchar(256)  default '' not null,
    node_fix_id        varchar(256)  default '' not null,
    temp_flags         varchar(512)  default '' not null,
    ro_group_id        varchar(64)   default '' not null comment 'readonly group id',
    ro_group_status    tinyint(1)    default 0  not null comment 'readonly group status',
    ro_group_weight    int           default 0  not null comment 'readonly group weight',
    global_id          varchar(64)   default '' not null,
    global_seq_id      int           default 0  not null,
    container_name     varchar(64)   default '' not null,
    constraint uniq_node_node_id
        unique (node_id)
)
    comment 'ro node infos' engine = InnoDB;

create index idx_node_app_id
    on ro_node (app_id);

create index idx_node_cluster_id
    on ro_node (cluster_id);

drop table if exists tasks;
create table if not exists tasks
(
    id                 bigint auto_increment
        primary key,
    deleted_at         int          default 0                     null,
    task_id            varchar(64)  default ''                    not null,
    task_batch_id      varchar(256) default ''                    not null,
    last_task_batch_id varchar(256) default ''                    not null,
    work_flow          varchar(64)  default ''                    not null,
    entity             varchar(64)  default ''                    not null,
    entity_dim         varchar(64)  default ''                    not null,
    status             varchar(64)  default ''                    not null,
    step               varchar(256)                               not null,
    step_start_at      timestamp    default CURRENT_TIMESTAMP     not null on update CURRENT_TIMESTAMP,
    step_deadline      timestamp    default '0000-00-00 00:00:00' not null,
    step_teu_count     int          default 0                     null,
    created_at         timestamp    default '0000-00-00 00:00:00' not null,
    schedule           timestamp    default '0000-00-00 00:00:00' not null,
    cron               varchar(64)  default ''                    not null,
    deadline           timestamp    default '0000-00-00 00:00:00' not null,
    start_at           timestamp    default '0000-00-00 00:00:00' not null,
    completed_at       timestamp    default '0000-00-00 00:00:00' not null,
    mutex              varchar(64)  default ''                    not null,
    priority           varchar(64)  default ''                    not null,
    parameters         longtext                                   null,
    error_step         varchar(256) default ''                    not null,
    err_msg            longtext                                   null,
    updated_at         timestamp    default '0000-00-00 00:00:00' not null,
    version            int          default 0                     not null,
    constraint task_id
        unique (task_id)
)
    engine = InnoDB;

create index idx_tasks_created_at
    on tasks (created_at);

create index idx_tasks_delete_at
    on tasks (deleted_at);

create index idx_tasks_status
    on tasks (status);

