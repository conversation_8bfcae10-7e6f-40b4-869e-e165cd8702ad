drop table if exists account_list;
create table if not exists account_list
(
    id              int auto_increment comment 'id'
        primary key,
    user_id         int          default 0                 not null comment 'user id',
    account_name    varchar(100) default ''                not null comment 'account name',
    account_show_id varchar(100) default ''                not null comment 'account show id',
    share_proto     varchar(100) default ''                not null comment 'share proto',
    ip              varchar(100) default ''                not null comment 'ip',
    mode            varchar(100) default ''                not null comment 'mode',
    persistence     varchar(100) default ''                not null comment 'persistence',
    squash          varchar(100) default ''                not null comment 'squash',
    user_name       varchar(100) default ''                not null comment 'user name',
    password        varchar(100) default ''                not null comment 'password',
    create_time     timestamp    default CURRENT_TIMESTAMP not null comment 'create time',
    directory       varchar(100) default ''                not null comment 'directory'
)
    comment 'account list' engine = InnoDB;

create index account_show_id
    on account_list (account_show_id);

drop table if exists backup_record;
create table if not exists backup_record
(
    id          bigint auto_increment comment 'id'
        primary key,
    batch_id    varchar(200)  default ''                not null comment 'batch_id',
    instance_id int           default 0                 not null comment 'instance_id',
    cluster_id  int           default 0                 not null comment 'cluster_id',
    start_time  timestamp     default CURRENT_TIMESTAMP not null comment 'start_time',
    duration    int           default 0                 not null comment 'duration',
    status      tinyint(2)    default 0                 not null comment 'status',
    backup_type tinyint(2)    default 0                 not null comment 'backup_type',
    bucket      varchar(200)  default ''                not null comment 'bucket',
    object_key  varchar(200)  default ''                not null comment 'object_key',
    object_size bigint        default 0                 not null comment 'object_size',
    shard_name  varchar(200)  default ''                not null comment 'shard_name',
    comment     varchar(1000) default ''                not null comment 'comment',
    expairation int           default 0                 not null comment 'expairation'
)
    comment 'backup_record' engine = InnoDB;

drop table if exists bae_cluster_white_list;
create table if not exists bae_cluster_white_list
(
    id          int auto_increment comment 'id'
        primary key,
    cluster_id  int                     not null comment 'cluster id',
    vm_uuid     varchar(200) default '' not null comment 'vm uuid',
    floating_ip varchar(200) default '' not null comment 'floating ip',
    mode        varchar(100) default '' not null comment 'mode'
)
    comment 'cache cluster white list' engine = InnoDB;

drop table if exists cache_cluster;
create table if not exists cache_cluster
(
    id                       int auto_increment comment 'id'
        primary key,
    user_id                  int                                          not null comment 'user id',
    cluster_name             varchar(100) default ''                      not null comment 'cluster name',
    engine_type              tinyint(1)                                   not null comment 'engine type',
    security_group_id        varchar(200) default ''                      not null comment 'group id',
    elb_id                   varchar(200) default ''                      null comment 'elb id',
    eip                      varchar(200) default ''                      null comment 'eip',
    elb_pnetip               varchar(200) default ''                      null comment 'elb pnetip',
    status                   tinyint(1)                                   not null comment 'status',
    create_time              timestamp    default CURRENT_TIMESTAMP       not null comment 'create time',
    pool_name                varchar(100) default ''                      not null comment 'pool name',
    security_group_rules_id  varchar(200) default ''                      not null comment 'group rule id',
    port                     int          default 0                       not null comment 'port',
    flavor                   int          default 0                       not null comment 'flavor',
    persistence              int          default 0                       not null comment 'persistence',
    domain                   varchar(100) default ''                      not null comment 'domain',
    cluster_show_id          varchar(100) default ''                      not null comment 'cluster show id',
    transaction_id           varchar(200) default ''                      not null comment 'transaction id',
    order_id                 varchar(200) default ''                      not null comment 'order id',
    version                  int          default 0                       not null comment 'version',
    dest_flavor              int          default 0                       not null comment 'dest flavor',
    tag_type                 int          default 0                       not null comment 'tag type',
    instance_num             int          default 0                       not null comment 'instance num',
    availability_zone        varchar(200) default ''                      not null comment 'availability zone',
    subnet_id                varchar(200) default ''                      not null comment 'subnet id',
    vpc_id                   varchar(200) default ''                      not null comment 'vpc id',
    cluster_type             varchar(200) default ''                      not null comment 'cluster type',
    master_domain            varchar(200) default ''                      not null comment 'master domain',
    master_port              int          default 0                       not null comment 'master port',
    master_vpc_id            varchar(200) default ''                      not null comment 'master vpc id',
    second_subnet_id         varchar(200) default ''                      not null comment 'second az subnet id',
    bcc_callback_flag        varchar(200) default ''                      not null comment 'bcc callback flag',
    old_availability_zone    varchar(200) default ''                      not null comment 'old_availability_zone',
    cluster_tags             varchar(200) default ''                      not null comment 'cluster_tags',
    cluster_tag_type         varchar(200) default ''                      not null comment 'cluster_tag_type',
    cluster_flavor_type      varchar(200) default ''                      not null comment 'cluster_flavor_type',
    backup_config            varchar(200) default ''                      not null comment 'backup_config',
    backup_status            int          default 0                       not null comment 'backup_status',
    last_backup_day          int          default 0                       not null comment 'last_backup_day',
    kernel_version           varchar(32)  default ''                      not null comment 'redis&mc kernel version',
    conf_version             tinyint(1)   default 0                       not null comment 'configue version',
    whitelist_version        tinyint(1)   default 0                       not null comment 'whitelist version',
    store_type               tinyint(1)   default 0                       not null comment 'store type',
    replication_num          tinyint(1)   default 2                       not null comment 'replication num',
    client_auth              varchar(64)  default ''                      not null comment 'client auth',
    meta_auth                varchar(64)  default ''                      not null comment 'meta auth',
    redis_auth               varchar(64)  default ''                      not null comment 'redis auth',
    elb_ipv6_id              varchar(200) default ''                      not null comment 'elb ipv6 id',
    elb_ipv6                 varchar(200) default ''                      not null comment 'elb ipv6 address',
    dest_proxy_num           int          default 0                       not null comment 'dest proxy num',
    recover_batch_id         varchar(200) default ''                      not null comment 'recover batch id',
    dest_instance_num        int          default 0                       not null comment 'dest instance num',
    node_type                varchar(200) default ''                      not null comment 'node type',
    dest_node_type           varchar(200) default ''                      not null comment 'dest node type',
    expect_version           varchar(200) default ''                      not null comment 'expect_version',
    update_uuid              varchar(200) default 'Success'               not null comment 'uuid',
    metaserver_id            varchar(200) default ''                      not null comment 'metaserver id',
    shard_security_group_id  varchar(200) default ''                      not null comment 'shard_security_group_id',
    encrypt_flag             tinyint(1)   default 0                       not null comment 'encrypt flag',
    recycle_time             timestamp    default CURRENT_TIMESTAMP       null,
    recycle_status           int          default 0                       null,
    cur_image                int          default 0                       not null,
    exp_image                int          default 0                       not null,
    proxy_num                int          default -1                      not null comment 'proxy num',
    az_deploy_info           varchar(300) default ''                      not null comment 'az deploy info',
    dest_az_deploy_info      varchar(300) default ''                      not null comment 'dest_az_deploy_info',
    op_type                  int          default 0                       not null comment 'op type',
    dest_replication_num     int          default 0                       not null comment 'dest_replication_num',
    enable_read_only         int          default 2                       not null comment 'enable_read_only',
    instance_order_id        varchar(300) default ''                      not null comment 'instance order id',
    migration_status         int          default 0                       not null comment 'migration_status',
    alias_name               varchar(100) default ''                      not null comment 'alias_name',
    acluser_version          tinyint(1)   default 0                       not null comment 'acl user',
    timeout                  int          default -1                      not null comment 'timeout',
    blb_listener_port        int          default 0                       null comment 'blb_listener_port',
    public_domain            varchar(200) default ''                      null comment 'public_domain',
    isolate_status           int          default 0                       not null,
    time_window              varchar(200) default '0,1,2,3,4,5,6;03:00;1' null,
    event_state              varchar(200) default ''                      not null comment 'event_state',
    env_type                 varchar(32)  default 'bcc'                   not null comment 'env_type',
    group_id                 varchar(100) default ''                      not null comment 'group id',
    group_role               tinyint(1)   default 0                       not null comment 'group role 0:not beloing to group, 1:master 2:slave',
    deploy_id_list           varchar(200) default ''                      not null comment 'deploy id list',
    enable_access_log        int          default 0                       not null comment 'enable access log',
    clone_data_cluster_id    varchar(100) default ''                      not null comment 'src cluster show id',
    clone_data_backup_id     varchar(200) default ''                      not null comment 'backup batchid',
    clone_data_moment        varchar(50)  default ''                      not null comment 'recover start time',
    clone_status             int          default 0                       not null comment 'clone status',
    enable_restore           int          default 0                       not null comment 'enable restore',
    restore_time             varchar(200) default ''                      not null comment 'restore start time',
    restore_status           int          default 0                       not null comment 'restore_status',
    last_restore_time        varchar(200) default ''                      not null comment 'last restore backup time',
    restore_recover_time     varchar(200) default ''                      not null comment 'restore recover time',
    restore_batch_id         varchar(200) default ''                      not null comment 'recover batch id',
    cur_image_ref            varchar(200) default ''                      not null comment 'current image ref',
    bgw_group_id             varchar(50)  default ''                      not null comment 'bgw group id',
    bgw_group_exclusive      int          default 0                       not null comment 'bgw_group_exclusive',
    restore_type             tinyint(1)   default 0                       not null comment 'restore type',
    need_update_restore_time tinyint(1)   default 0                       not null comment 'update restore_time or not',
    disk_flavor              int          default 0                       not null comment 'disk flavor',
    dest_disk_flavor         int          default 0                       not null comment 'dest disk flavor',
    enable_slow_log          int          default 0                       not null comment 'enable slow log',
    enable_hotkey            int          default 0                       not null comment 'enable hotkey',
    enable_switch_domain     tinyint(1)   default 0                       not null comment '0:not switch 1:switch',
    record_max_node_id       int(1)       default 0                       not null comment 'record max node id',
    cb_username_admin        varchar(64)  default ''                      not null comment 'couchbase cluster admin username',
    cb_passwd_admin          varchar(64)  default ''                      not null comment 'couchbase cluster admin password',
    cb_username_ro           varchar(64)  default ''                      not null comment 'couchbase cluster readonly username',
    cb_passwd_ro             varchar(64)  default ''                      not null comment 'couchbase cluster readonly password',
    cb_memory_quota          int          default 0                       not null comment 'couchbase cluster mmeory quota',
    cb_services              varchar(64)  default ''                      not null comment 'couchbase cluster services',
    ro_group_type            tinyint(1)   default 0                       not null comment 'readonly group type',
    pushed_flag              int(1)       default 0                       not null comment 'pushed failed event or not',
    disk_type                varchar(64)  default ''                      not null comment 'pega disk type',
    metaserver_address       varchar(256) default ''                      not null,
    bns_service              varchar(256) default ''                      not null,
    resource_type            varchar(64)  default ''                      not null comment 'resource type',
    bns_group                varchar(200) default ''                      not null comment 'bns group',
    bns_node_name            varchar(200) default ''                      not null comment 'bns node',
    endpoint_id              varchar(64)  default ''                      not null,
    endpoint_ip              varchar(16)  default ''                      not null,
    hit_x1                   int          default 0                       not null
)
    comment 'cache service info' engine = InnoDB;

drop table if exists cache_cluster_analyze;
create table if not exists cache_cluster_analyze
(
    id               bigint auto_increment comment 'id'
        primary key,
    cluster_id       int                                     not null comment 'cluster id',
    type             tinyint(1)                              not null comment 'analyze type',
    bigkey_status    tinyint(1)                              not null comment 'bigkey status',
    bigkey_top_n     int                                     not null comment 'top n bigkey',
    create_time      timestamp default CURRENT_TIMESTAMP     not null comment 'create time',
    analyze_executor int                                     not null comment 'analyze executor',
    update_time      timestamp default '0000-00-00 00:00:00' not null comment 'update time',
    task_version     int       default 0                     not null comment 'task version'
)
    comment 'cache_cluster_analyze' engine = InnoDB;

create index task_index
    on cache_cluster_analyze (bigkey_status);

drop table if exists cache_instance;
create table if not exists cache_instance
(
    id                  int auto_increment comment 'id'
        primary key,
    cluster_id          int                                    not null comment 'cluster id',
    user_id             int                                    not null comment 'user id',
    port                int                                    not null comment 'port',
    create_time         timestamp    default CURRENT_TIMESTAMP not null comment 'time',
    flavor              tinyint(1)                             not null comment 'flavor',
    uuid                varchar(200)                           not null comment 'uuid',
    cache_instance_type tinyint(11)                            not null comment 'cache instance type',
    master_redis        varchar(200) default ''                null comment 'master redis',
    slaver_redis        varchar(200) default ''                null comment 'slaver redis',
    status              tinyint(1)                             not null comment 'status',
    persistence         tinyint(1)                             not null comment 'persistence',
    fix_ip              varchar(50)                            not null comment 'fix ip',
    floating_ip         varchar(50)                            not null comment 'floating ip',
    password            varchar(50)                            not null comment 'password',
    hash_name           varchar(100) default ''                not null comment 'hash name',
    host_name           varchar(100) default ''                not null comment 'host name',
    iam_user_id         varchar(200) default ''                not null comment 'iamuser id',
    shard_id            int          default 0                 not null comment 'shard id',
    migrate_status      int          default 0                 not null comment 'migrate status',
    hash_id             varchar(200) default ''                not null comment 'hash id',
    availability_zone   varchar(200) default ''                not null comment 'availability zone',
    subnet_id           varchar(200) default ''                not null comment 'subnet id',
    ipv6                varchar(200) default ''                not null comment 'ipv6 address',
    res_flavor          varchar(50)  default ''                not null,
    node_id             varchar(200) default ''                null comment 'store node id of opensource redis cluster mode',
    stat_port           int          default 0                 not null comment 'stat_port',
    xagent_port         int          default 0                 not null comment 'xagent_port',
    home_path           varchar(200) default ''                not null comment 'home_path',
    bbc_id              varchar(200) default ''                not null comment 'bbc_id',
    node_show_id        varchar(200) default ''                not null comment 'node_show_id',
    ro_group_id         int          default 0                 not null comment 'ro group id',
    ro_group_status     tinyint(1)   default 0                 not null comment 'ro group status',
    ro_group_weight     int          default 0                 not null comment 'ro group weight',
    is_readonly         tinyint(1)   default 0                 not null comment 'is readonly or not',
    container_id        varchar(200) default ''                not null comment 'container_id',
    container_name      varchar(100) default ''                not null comment 'container_name',
    constraint uuid
        unique (uuid)
)
    comment 'cache instance info' engine = InnoDB;

drop table if exists cache_instance_to_delete;
create table if not exists cache_instance_to_delete
(
    id                  int auto_increment comment 'id'
        primary key,
    cluster_id          int                                     not null comment 'cluster id',
    user_id             int                                     not null comment 'user id',
    port                int                                     not null comment 'port',
    create_time         timestamp     default CURRENT_TIMESTAMP not null comment 'create time',
    flavor              tinyint(1)                              not null comment 'flavor',
    uuid                varchar(200)                            not null comment 'uuid',
    cache_instance_type tinyint(11)                             not null comment 'cache instance type',
    master_redis        varchar(200)  default ''                null comment 'master redis',
    slaver_redis        varchar(200)  default ''                null comment 'slaver redis',
    persistence         tinyint(1)                              not null comment 'persistence',
    fix_ip              varchar(50)                             not null comment 'fix ip',
    floating_ip         varchar(50)                             not null comment 'floating ip',
    password            varchar(50)                             not null comment 'password',
    hash_name           varchar(100)  default ''                not null comment 'hash name',
    host_name           varchar(100)  default ''                not null comment 'host name',
    shard_id            int           default 0                 not null comment 'shard id',
    delete_time         varchar(1024) default ''                not null comment 'delete time',
    ipv6                varchar(200)  default ''                not null comment 'ipv6 address',
    status              varchar(100)  default ''                null comment 'status',
    instance_id         int                                     null comment 'instance_id',
    clear_status        tinyint(1)    default 0                 not null comment 'clear_status'
)
    comment 'cache instance info to delete' engine = InnoDB;

drop table if exists cluster_acl_user;
create table if not exists cluster_acl_user
(
    id                   int auto_increment comment 'id'
        primary key,
    cluster_id           int                                     not null comment 'cluster id',
    user_name            varchar(200)                            not null comment 'user name',
    create_time          timestamp     default CURRENT_TIMESTAMP not null comment 'time',
    password             varchar(200)                            not null comment 'password',
    allowed_commands     varchar(1000) default ''                null comment 'allowed commands',
    allowed_sub_commands varchar(1000) default ''                null comment 'allowed sub commands',
    key_patterns         varchar(200)  default ''                null comment 'slaver redis',
    modify_time          timestamp     default CURRENT_TIMESTAMP not null comment 'time',
    update_status        tinyint(1)                              not null comment 'key patterns',
    extra                varchar(200)                            not null comment 'extra',
    transaction_id       varchar(50)                             not null comment 'transaction id',
    user_type            int           default -1                not null comment 'user_type'
)
    comment 'cluster acl user info' engine = InnoDB;

drop table if exists cluster_analysis_result;
create table if not exists cluster_analysis_result
(
    id          bigint auto_increment comment 'id'
        primary key,
    cluster_id  int                                    not null comment 'cluster id',
    shard_id    int                                    not null comment 'shard id',
    shard_name  varchar(200) default ''                not null comment 'shard name',
    type        tinyint(1)                             not null comment 'analyze type',
    key_name    varchar(512) default ''                not null comment 'key name',
    info        varchar(512) default ''                not null comment 'key info',
    update_time timestamp    default CURRENT_TIMESTAMP not null comment 'update time'
)
    comment 'cluster_analysis_result' engine = InnoDB;

create index cluster_result_index
    on cluster_analysis_result (cluster_id);

drop table if exists cluster_white_list;
create table if not exists cluster_white_list
(
    id          int auto_increment comment 'id'
        primary key,
    cluster_id  int                     not null comment 'cluster id',
    vm_uuid     varchar(200) default '' not null comment 'vm uuid',
    floating_ip varchar(200) default '' not null comment ' floating ip',
    mode        varchar(100) default '' not null comment 'mode',
    insert_mode varchar(200) default '' not null comment 'insert mode'
)
    comment 'cache cluster white list' engine = InnoDB;

drop table if exists conf_history_list;
create table if not exists conf_history_list
(
    id           bigint(11) auto_increment comment 'id'
        primary key,
    cluster_id   int                                    not null comment 'cluster_id',
    conf_name    varchar(200) default ''                not null comment 'conf_name',
    value_before varchar(128) default ''                not null comment 'value_before',
    value_after  varchar(128) default ''                not null comment 'value_after',
    change_time  timestamp    default CURRENT_TIMESTAMP not null comment 'change_time'
)
    comment 'conf_history_list' engine = InnoDB;

create index cluster_id
    on conf_history_list (cluster_id);

drop table if exists conf_record_list;
create table if not exists conf_record_list
(
    id          int auto_increment comment 'id'
        primary key,
    cluster_id  int                     not null comment 'cluster_id',
    conf_name   varchar(200) default '' not null comment 'conf_name',
    conf_module int          default 0  not null comment 'conf_module',
    value       varchar(128) default '' not null comment 'value',
    effected    int          default 0  not null comment 'effect'
)
    comment 'conf_record_list' engine = InnoDB;

create index cluster_id
    on conf_record_list (cluster_id);

drop table if exists event_list;
create table if not exists event_list
(
    id          bigint(11) auto_increment comment 'id'
        primary key,
    cluster_id  int                                    not null comment 'cluster_id',
    instance_id int                                    not null comment 'instance_id',
    level       int                                    not null comment 'level',
    type        int                                    not null comment 'type',
    time        timestamp    default CURRENT_TIMESTAMP not null comment 'time',
    event_desc  varchar(256) default ''                not null comment 'event_desc'
)
    comment 'event_list' engine = InnoDB;

create index cluster_id
    on event_list (cluster_id);

drop table if exists exp_rule;
create table if not exists exp_rule
(
    id          bigint auto_increment comment 'id'
        primary key,
    exp_id      bigint                                  not null comment 'strategy id',
    create_time timestamp     default CURRENT_TIMESTAMP not null comment 'create time',
    status      tinyint(1)    default 0                 not null comment 'status',
    rule        varchar(1024) default ''                not null comment 'rule',
    flag        varchar(64)   default ''                not null comment 'flag',
    priority    int           default 0                 not null comment 'priority'
)
    comment 'exp_rule' engine = InnoDB;

drop table if exists exp_strategy;
create table if not exists exp_strategy
(
    id           bigint auto_increment comment 'id'
        primary key,
    name         varchar(64)   default ''                not null comment 'strategy name',
    create_time  timestamp     default CURRENT_TIMESTAMP not null comment 'create time',
    status       tinyint(1)    default 0                 not null comment 'status',
    features     varchar(1024) default ''                not null comment 'features',
    default_flag varchar(64)   default ''                not null comment 'default_flag',
    owner        varchar(64)   default ''                not null comment 'owner'
)
    comment 'exp_strategy' engine = InnoDB;

drop table if exists group_list;
create table if not exists group_list
(
    id                   int auto_increment comment 'id'
        primary key,
    user_id              int           default 0                 not null comment 'user id',
    group_name           varchar(100)  default ''                not null comment 'group name',
    group_show_id        varchar(100)  default ''                not null comment 'group show id',
    share_proto          varchar(100)  default ''                not null comment 'share proto',
    account_show_id_list varchar(1000) default ''                not null comment 'account show id list',
    create_time          timestamp     default CURRENT_TIMESTAMP not null comment 'create time'
)
    comment 'group list' engine = InnoDB;

drop table if exists image;
create table if not exists image
(
    id              bigint auto_increment comment 'id'
        primary key,
    object_key      varchar(64) default '' not null comment 'object_key',
    node_version    varchar(64) default '' not null comment 'node_version',
    proxy_version   varchar(64) default '' not null comment 'proxy_version',
    agent_version   varchar(64) default '' not null comment 'agent_version',
    cluster_version int         default 0  not null comment 'cluster_version',
    kernel_version  varchar(64) default '' not null comment 'kernel_version'
)
    comment 'image' engine = InnoDB;

drop table if exists image_version;
create table if not exists image_version
(
    id               bigint auto_increment comment 'id'
        primary key,
    object_key       varchar(64)   default ''                not null comment 'object_key',
    redisv5_version  varchar(64)   default ''                not null comment 'redisv5_version',
    redisv7_version  varchar(64)   default ''                not null comment 'redisv7_version',
    proxy_version    varchar(64)   default ''                not null comment 'proxy_version',
    agent_version    varchar(64)   default ''                not null comment 'agent_version',
    memcache_version varchar(64)   default ''                not null comment 'memcache_version',
    memproxy_version varchar(64)   default ''                not null comment 'memproxy_version',
    create_time      timestamp     default CURRENT_TIMESTAMP not null comment 'create time',
    comment          varchar(1000) default ''                not null comment 'comment'
)
    comment 'image_version' engine = InnoDB;

drop table if exists master_ha;
create table if not exists master_ha
(
    id        bigint auto_increment comment 'master group id'
        primary key,
    master_id varchar(64) default '' not null comment 'master id',
    time      bigint      default 0  not null comment 'time'
)
    comment 'master_ha' engine = InnoDB;

drop table if exists multi_active_channel_list;
create table if not exists multi_active_channel_list
(
    id                   int auto_increment comment 'id'
        primary key,
    cluster_id           int                                    not null comment 'cluster_id',
    peer_cluster_show_id varchar(100) default ''                not null comment 'peer_cluster_show_id',
    peer_ip              varchar(100) default ''                not null comment 'peer_ip',
    peer_port            int                                    not null comment 'peer_port',
    peer_auth            varchar(64)  default ''                not null comment 'peer_auth',
    status               tinyint(1)                             not null comment 'status',
    create_time          timestamp    default CURRENT_TIMESTAMP not null comment 'create_time'
)
    comment 'multi_active_channel_list' engine = InnoDB;

drop table if exists order_list;
create table if not exists order_list
(
    id           int auto_increment comment 'id'
        primary key,
    order_id     varchar(200) default ''                not null comment 'order id',
    status       tinyint(1)                             not null comment 'status',
    create_time  timestamp    default CURRENT_TIMESTAMP not null comment 'create time',
    iam_user_id  varchar(200) default ''                not null comment 'iam user id',
    cluster_name varchar(200) default ''                not null comment 'cluster name',
    engine_type  tinyint(1)                             not null comment 'engine type',
    cluster_num  int          default 0                 not null comment 'cluster num',
    instance_num int          default 0                 not null comment 'instance num',
    port         int          default 0                 not null comment 'port',
    flavor       int          default 0                 not null comment 'flavor',
    persistence  int          default 0                 not null comment 'persistence',
    error_code   int          default 0                 not null comment 'error code',
    action       int          default 0                 not null comment 'action',
    cluster_type varchar(200) default 'default'         not null
)
    comment 'console order id info' engine = InnoDB;

drop table if exists parameter_template;
create table if not exists parameter_template
(
    id               int auto_increment
        primary key,
    name             varchar(64)   default ''                    not null,
    template_show_id varchar(64)   default ''                    not null,
    user_id          int           default 0                     not null,
    cluster_type     varchar(200)  default 'default'             not null comment 'cluster type',
    engine           varchar(32)   default ''                    not null,
    engine_version   varchar(64)   default ''                    not null,
    template_type    int           default 1                     not null,
    need_reboot      int           default 0                     not null,
    comment          varchar(1024) default ''                    not null,
    parameters       varchar(2048) default ''                    not null,
    param_num        int           default 0                     not null,
    create_time      datetime      default '0000-00-00 00:00:00' not null,
    update_time      datetime      default '0000-00-00 00:00:00' not null,
    delete_time      datetime      default '0000-00-00 00:00:00' not null
)
    comment 'parameter template' engine = InnoDB;

drop table if exists process_record;
create table if not exists process_record
(
    id              bigint auto_increment comment 'auto increment id'
        primary key,
    cluster_id      bigint        default 0                 not null comment 'cluster id',
    stage           varchar(64)   default ''                not null comment 'stage',
    stage_phase     tinyint(1)    default 0                 not null comment 'stage phase',
    rule_name       varchar(64)   default ''                not null comment 'rule name',
    transaction     varchar(64)   default ''                not null comment 'transaction id',
    sub_transaction varchar(64)   default ''                not null comment 'sub transaction id',
    create_time     timestamp     default CURRENT_TIMESTAMP not null comment 'create time',
    status          tinyint(1)    default 0                 not null comment 'process status',
    callback_flag   tinyint(1)    default 0                 not null comment 'callback flag',
    callback_info   varchar(64)   default ''                not null comment 'callback info',
    msg             varchar(4096) default ''                not null comment 'message',
    processor_name  varchar(64)   default ''                not null comment 'processor name'
)
    comment 'process record' engine = InnoDB;

drop table if exists record_event_id;
create table if not exists record_event_id
(
    id        bigint auto_increment comment 'id'
        primary key,
    record_id int not null comment 'record event id'
)
    comment 'record event id' engine = InnoDB;

drop table if exists repair_quot;
create table if not exists repair_quot
(
    id                bigint auto_increment
        primary key,
    uuid              varchar(200)                         not null comment 'uuid',
    cluster_id        int                                  not null,
    user_id           int                                  not null,
    repair_status     tinyint(1) default 0                 not null,
    quot_time         timestamp  default CURRENT_TIMESTAMP not null,
    repair_start_time timestamp  default CURRENT_TIMESTAMP not null,
    repair_end_time   timestamp  default CURRENT_TIMESTAMP not null
)
    engine = InnoDB;

drop table if exists res_statistics;
create table if not exists res_statistics
(
    id          int auto_increment comment 'id'
        primary key,
    ncluster    int       default 0                 not null comment 'ncluster',
    ninstance   int       default 0                 not null comment 'ninstance',
    vcpu        int       default 0                 not null comment 'vcpu',
    mem         int       default 0                 not null comment 'mem',
    disk        int       default 0                 not null comment 'disk',
    nv1         int       default 0                 not null comment 'nv1',
    nv5         int       default 0                 not null comment 'nv5',
    nv7         int       default 0                 not null comment 'nv7',
    n1          int       default 0                 not null comment 'n1',
    n2          int       default 0                 not null comment 'n2',
    n4          int       default 0                 not null comment 'n4',
    n8          int       default 0                 not null comment 'n8',
    n16         int       default 0                 not null comment 'n16',
    n32         int       default 0                 not null comment 'n32',
    n64         int       default 0                 not null comment 'n64',
    nproxy      int       default 0                 not null comment 'nproxy',
    create_time timestamp default CURRENT_TIMESTAMP not null comment 'create_time',
    type        int       default 0                 not null comment 'statistic type 0all 1external 2internal 3spinoff'
)
    comment 'res_statistics' engine = InnoDB;

drop table if exists resource_action_record;
create table if not exists resource_action_record
(
    id             int auto_increment comment 'id'
        primary key,
    cluster_id     int                                 not null comment 'cluster id',
    create_time    timestamp default CURRENT_TIMESTAMP not null comment 'create time',
    resource_type  varchar(200)                        not null comment 'resource type',
    identification varchar(200)                        not null comment 'identification',
    action         varchar(200)                        not null comment 'action',
    transaction_id varchar(200)                        not null comment 'transaction id',
    status         varchar(200)                        not null comment 'status'
)
    comment 'resource action record' engine = InnoDB;

drop table if exists restore_record;
create table if not exists restore_record
(
    id           bigint auto_increment comment 'id'
        primary key,
    batch_id     varchar(200) default ''                not null comment 'batch_id',
    instance_id  int          default 0                 not null comment 'instance_id',
    cluster_id   int          default 0                 not null comment 'cluster_id',
    start_time   timestamp    default CURRENT_TIMESTAMP not null comment 'start_time',
    status       tinyint(2)   default 0                 not null comment 'status',
    restore_type tinyint(2)   default 0                 not null comment 'restore_type',
    bucket       varchar(200) default ''                not null comment 'bucket',
    object_key   varchar(200) default ''                not null comment 'object_key',
    shard_name   varchar(200) default ''                not null comment 'shard_name'
)
    comment 'restore_record' engine = InnoDB;

drop table if exists ro_group;
create table if not exists ro_group
(
    id                    int auto_increment comment 'id'
        primary key,
    cluster_id            int                                    not null comment 'cluster id',
    ro_group_name         varchar(200) default ''                not null comment 'ro group name',
    ro_group_show_id      varchar(200) default ''                not null comment 'ro group show id',
    status                tinyint(1)                             not null comment 'status',
    domain                varchar(200) default ''                not null comment 'ro group domain',
    enable_delay_off      tinyint(1)   default 0                 not null comment 'enable delay off',
    user_id               int                                    not null comment 'user id',
    enable_threshold      tinyint(1)   default 20                not null comment 'delay threshold',
    least_instance_amount tinyint(1)   default 1                 not null comment 'least instance amount',
    is_balance_reload     tinyint(1)   default 0                 not null comment 'is balance reload',
    eip                   varchar(200) default ''                not null comment 'eip',
    eip_status            varchar(200) default ''                not null comment 'eip status',
    blb_id                varchar(200) default ''                not null comment 'blb id',
    ip                    varchar(200) default ''                not null comment 'ip',
    bgw_group_exclusive   int          default 0                 not null comment 'bgw group exclusive',
    bgw_group_id          varchar(50)  default ''                not null comment 'bgw group id',
    listener_port         int          default 0                 not null comment 'listener port',
    vpc_id                varchar(200) default ''                not null comment 'vpc id',
    subnet_id             varchar(200) default ''                not null comment 'subnet id',
    op_type               int          default 0                 not null comment 'op type',
    create_time           timestamp    default CURRENT_TIMESTAMP not null comment 'create time',
    transaction_id        varchar(200) default ''                not null comment 'transaction id',
    blb_ipv6_id           varchar(200) default ''                not null comment 'blb ipv6 id',
    blb_ipv6_ip           varchar(200) default ''                not null comment 'blb ipv6 address',
    public_domain         varchar(200) default ''                not null,
    endpoint_id           varchar(64)  default ''                not null,
    endpoint_ip           varchar(16)  default ''                not null
)
    comment 'ro_group' engine = InnoDB;

drop table if exists share_instance;
create table if not exists share_instance
(
    id              int auto_increment comment 'id'
        primary key,
    share_server_id int          default 0                 not null comment 'share server  id',
    user_id         int          default 0                 not null comment 'user id',
    create_time     timestamp    default CURRENT_TIMESTAMP not null comment 'create time',
    type            tinyint(1)   default 0                 not null comment 'type',
    flavor          int          default 0                 not null comment 'flavor',
    uuid            varchar(200) default ''                not null comment 'uuid',
    status          tinyint(1)   default 0                 not null comment 'status',
    fix_ip          varchar(50)  default ''                not null comment 'fix ip',
    floating_ip     varchar(50)  default ''                not null comment 'floating ip',
    password        varchar(50)  default ''                not null comment 'password',
    host_name       varchar(100) default ''                not null comment 'host name',
    iam_user_id     varchar(200) default ''                not null comment 'iam user id',
    hash_id         varchar(200) default ''                not null comment 'hash id'
)
    comment 'share server instance info' engine = InnoDB;

drop table if exists share_server;
create table if not exists share_server
(
    id                      int auto_increment comment 'id'
        primary key,
    user_id                 int           default 0                 not null comment 'user id',
    share_server_name       varchar(100)  default ''                not null comment 'share server name',
    share_proto             varchar(100)  default ''                not null comment 'share proto',
    security_group_id       varchar(200)  default ''                not null comment 'group id',
    elb_id                  varchar(200)  default ''                not null comment 'elb id',
    eip                     varchar(200)  default ''                not null comment 'eip',
    elb_pnetip              varchar(200)  default ''                not null comment 'elb pnet ip',
    status                  tinyint(1)    default 0                 not null comment 'status',
    create_time             timestamp     default CURRENT_TIMESTAMP not null comment 'create time',
    pool_name               varchar(100)  default ''                not null comment 'pool name',
    security_group_rules_id varchar(200)  default ''                not null comment 'rule id',
    flavor                  int           default 0                 not null comment 'flavor',
    volume_type             varchar(100)  default ''                not null comment 'volume type',
    domain                  varchar(100)  default ''                not null comment 'domain',
    share_server_show_id    varchar(100)  default ''                not null comment 'share server show id',
    transaction_id          varchar(200)  default ''                not null comment 'transaction id',
    order_id                varchar(200)  default ''                not null comment 'order id',
    version                 int           default 0                 not null comment 'version',
    dest_flavor             int           default 0                 not null comment 'dest flavor',
    availability_zone       varchar(200)  default ''                not null comment 'availability zone',
    subnet_id               varchar(200)  default ''                not null comment 'subnet id',
    vpc_id                  varchar(200)  default ''                not null comment 'vpc id',
    group_show_id           varchar(200)  default ''                not null comment 'group show id',
    share_id                varchar(200)  default ''                not null comment 'share id',
    bos_ak                  varchar(200)  default ''                not null comment 'ak',
    bos_sk                  varchar(200)  default ''                not null comment 'sk',
    export_dir              varchar(200)  default ''                not null comment 'export dir',
    bucket                  varchar(1000) default ''                not null comment 'bucket',
    endpoint                varchar(200)  default ''                not null comment 'endpoint',
    expiration              varchar(200)  default ''                not null comment 'expiration',
    session_token           varchar(1000) default ''                not null comment 'session token',
    bandwidth               int           default 0                 not null comment 'bandwidth',
    subnet_short_id         varchar(200)  default ''                not null comment 'subnet short id',
    vpc_short_id            varchar(200)  default ''                not null comment 'vpc short id'
)
    comment 'share service info' engine = InnoDB;

drop table if exists subnet_list;
create table if not exists subnet_list
(
    id         bigint(11) auto_increment comment 'id'
        primary key,
    cluster_id int                     not null comment 'cluster id',
    subnet_id  varchar(200) default '' not null comment 'subnet id',
    group_id   int          default 0  not null comment 'group id',
    flags      int          default 0  not null comment 'others'
)
    comment 'subnet list' engine = InnoDB;

create index cluster_id
    on subnet_list (cluster_id);

drop table if exists target_list;
create table if not exists target_list
(
    id              int auto_increment comment 'id'
        primary key,
    target_show_id  varchar(200) default ''                not null comment 'target id',
    share_server_id int          default 0                 not null comment 'share server id',
    bucket          varchar(200) default ''                not null comment 'bucket',
    endpoint        varchar(200) default ''                not null comment 'endpoint',
    group_show_id   varchar(200) default ''                not null comment 'group show id',
    create_time     timestamp    default CURRENT_TIMESTAMP not null comment 'create time',
    share_proto     varchar(200) default ''                not null comment 'share proto',
    volume_type     varchar(200) default ''                not null comment 'volume type'
)
    comment 'target list' engine = InnoDB;

drop table if exists template_record;
create table if not exists template_record
(
    id              int auto_increment comment 'id'
        primary key,
    template_id     int          default 0                     not null comment 'template_id',
    cluster_id      int          default 0                     not null comment 'cluster_id',
    user_id         int          default 0                     not null comment 'user_id',
    create_time     datetime     default '0000-00-00 00:00:00' not null,
    update_time     datetime     default '0000-00-00 00:00:00' not null,
    delete_time     datetime     default '0000-00-00 00:00:00' not null,
    cluster_show_id varchar(200) default ''                    not null comment 'cluster_show_id'
)
    comment 'template_record' engine = InnoDB;

create index index_cluster
    on template_record (cluster_id);

drop table if exists time_window_task;
create table if not exists time_window_task
(
    id             int auto_increment comment 'id'
        primary key,
    cluster_id     int                                    not null comment 'cluster id',
    task_status    varchar(200) default 'waiting'         not null comment 'task type',
    create_time    timestamp    default CURRENT_TIMESTAMP not null comment 'time',
    task_type      varchar(50)                            not null comment 'task type',
    task_params    varchar(2000)                          null comment 'task params',
    transaction_id varchar(50)                            not null comment 'transaction id',
    execute_time   timestamp    default CURRENT_TIMESTAMP null comment 'execute_time'
)
    comment 'time window task' engine = InnoDB;

drop table if exists user_conf_list;
create table if not exists user_conf_list
(
    id                 bigint(11) auto_increment comment 'id'
        primary key,
    conf_name          varchar(200)  default ''  not null comment 'conf_name',
    conf_module        int           default 0   not null comment 'conf_module',
    conf_desc          varchar(1024) default ''  not null comment 'conf_desc',
    conf_type          tinyint(11)   default 0   not null comment 'conf_type',
    conf_range         varchar(200)  default ''  not null comment 'conf_range',
    conf_default       varchar(128)  default ''  not null comment 'conf_default',
    conf_cache_version int           default 0   not null comment 'conf_cache_version',
    conf_redis_version varchar(32)   default '0' not null comment 'conf_redis_version',
    conf_user_visible  int           default 0   not null comment 'conf_user_visible',
    need_reboot        int           default 0   not null comment 'need_reboot'
)
    comment 'user_conf_list' engine = InnoDB;

drop table if exists user_white_list;
create table if not exists user_white_list
(
    id          int auto_increment comment 'id'
        primary key,
    iam_user_id varchar(200) default '' not null comment 'iam user id',
    cluster_id  int                     null comment 'cluster id',
    user_email  varchar(100) default '' not null comment 'user email',
    version     int          default 0  not null comment 'version',
    quota       int          default 0  not null comment 'quota',
    tags        varchar(200) default '' not null comment 'tags',
    tag_type    varchar(200) default '' not null comment 'tag_type',
    flavor_type varchar(200) default '' not null comment 'flavor_type'
)
    comment 'user white list for docker' engine = InnoDB;

drop table if exists userinfo;
create table if not exists userinfo
(
    id                           int auto_increment comment 'id'
        primary key,
    iam_user_id                  varchar(50)                            not null comment 'iam user id',
    security_group_id            varchar(200) default ''                not null comment 'security group id',
    icmp_security_group_rules_id varchar(200) default ''                not null comment 'icmp rules id',
    ssh_security_group_rules_id  varchar(200) default ''                not null comment 'ssh rules id',
    security_group_rules_id      varchar(200) default ''                not null comment 'security rules id',
    create_time                  timestamp    default CURRENT_TIMESTAMP not null comment 'create time',
    type                         int          default 0                 not null comment 'user type 0external 1internal 2spinoff',
    constraint iam_user_id
        unique (iam_user_id)
)
    comment 'user info' engine = InnoDB;

INSERT INTO master_ha (id, master_id, time) VALUES (1, '1', 1660889945145);

INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (1, 'appendonly', 1, '5piv5ZCm5ZCv55SoIEFPRiDmjIHkuYXljJY=', 1, 'yes|no|partial', 'yes', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (2, 'maxmemory-policy', 1, '5b2T5YaF5a2Y6LaF6YeP5pe255qE6ZSu5reY5rGw562W55Wl44CC5Y+v6YCJOjxici8+dm9sYXRpbGUtbHJ177ya5Y+q5LuO5bey6K6+572u6L+H5pyf5pe26Ze055qE5pWw5o2u6ZuG5Lit5oyR6YCJ5pyA6L+R5pyA5bCR5L2/55So55qE5pWw5o2u5reY5rGwPGJyLz5hbGxrZXlzLWxyde+8muaMkemAieacgOi/keacgOWwkeS9v+eUqOeahOaVsOaNrua3mOaxsDxici8+dm9sYXRpbGUtcmFuZG9t77ya5Y+q5LuO5bey6K6+572u6L+H5pyf5pe26Ze055qE5pWw5o2u6ZuG5Lit5Lu75oSP6YCJ5oup5pWw5o2u5reY5rGwPGJyLz5hbGxrZXlzLXJhbmRvbe+8muS7u+aEj+mAieaLqeaVsOaNrua3mOaxsDxici8+dm9sYXRpbGUtdHRs77ya5Y+q5LuO5bey6K6+572u6L+H5pyf5pe26Ze055qE5pWw5o2u6ZuG5Lit5oyR6YCJ5bCG6KaB6L+H5pyf55qE5pWw5o2u5reY5rGwPGJyLz5ub2V2aWN0aW9u77ya5LiN6L+b6KGM5pWw5o2u5reY5rGw', 1, 'volatile-lru|allkeys-lru|volatile-random|allkeys-random|volatile-ttl|noeviction', 'volatile-ttl', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (3, 'hash-max-ziplist-entries', 1, '5b2TIGhhc2gg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMIGhhc2gg5a+56LGh5L2/55SoIHppcGxpc3Qg57yW56CB77yaPGJyLz4xLiBoYXNoIOWvueixoeS/neWtmOeahOmUruWAvOWvueaVsOmHj+Wwj+S6juaIluiAheetieS6jiBoYXNoLW1heC16aXBsaXN0LWVudHJpZXMg5Y+C5pWw5YC8PGJyLz4yLiBoYXNoIOWvueixoeS/neWtmOeahOaJgOaciemUruWAvOWvueeahOmUruWSjOWAvOeahOWtl+espuS4sumVv+W6pumDveWwj+S6juaIluiAheetieS6jiBoYXNoLW1heC16aXBsaXN0LXZhbHVlIOWPguaVsOWAvDxici8+', 2, '0-999999999999999', '512', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (4, 'hash-max-ziplist-value', 1, '5b2TIGhhc2gg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMIGhhc2gg5a+56LGh5L2/55SoIHppcGxpc3Qg57yW56CB77yaPGJyLz4xLiBoYXNoIOWvueixoeS/neWtmOeahOmUruWAvOWvueaVsOmHj+Wwj+S6juaIluiAheetieS6jiBoYXNoLW1heC16aXBsaXN0LWVudHJpZXMg5Y+C5pWw5YC8PGJyLz4yLiBoYXNoIOWvueixoeS/neWtmOeahOaJgOaciemUruWAvOWvueeahOmUruWSjOWAvOeahOWtl+espuS4sumVv+W6pumDveWwj+S6juaIluiAheetieS6jiBoYXNoLW1heC16aXBsaXN0LXZhbHVlIOWPguaVsOWAvDxici8+', 2, '0-999999999999999', '64', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (5, 'set-max-intset-entries', 1, '5b2TIHNldCDlr7nosaHlkIzml7bmu6HotrPku6XkuIvkuKTkuKrmnaHku7bml7bvvIwgc2V0IOWvueixoeS9v+eUqCBpbnRzZXQg57yW56CB77yaPGJyLz4xLiBzZXQg5a+56LGh5Lit55qE5YWD57Sg5pWw6YeP5bCP5LqO5oiW6ICF562J5LqOIHNldC1tYXgtaW50c2V0LWVudHJpZXMg5Y+C5pWw5YC8PGJyLz4yLiBzZXQg5a+56LGh5Lit55qE5omA5pyJ5YWD57Sg6YO95pivIDY0IOS9jeacieespuWPt+WNgei/m+WItuaVtOaVsDxici8+', 2, '0-999999999999999', '512', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (6, 'zset-max-ziplist-entries', 1, '5b2TIHpzZXQg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMenNldCDlr7nosaHkvb/nlKggemlwbGlzdCDnvJbnoIHvvJo8YnIvPjEuIHpzZXQg5a+56LGh5L+d5a2Y55qE6ZSu5YC85a+55pWw6YeP5bCP5LqO5oiW6ICF562J5LqOIHpzZXQtbWF4LXppcGxpc3QtZW50cmllcyDlj4LmlbDlgLw8YnIvPjIuIHpzZXQg5a+56LGh5L+d5a2Y55qE5omA5pyJ6ZSu5YC85a+555qE6ZSu5ZKM5YC855qE5a2X56ym5Liy6ZW/5bqm6YO95bCP5LqO5oiW6ICF562J5LqOIHpzZXQtbWF4LXppcGxpc3QtdmFsdWUg5Y+C5pWw5YC8PGJyLz4=', 2, '0-999999999999999', '128', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (7, 'zset-max-ziplist-value', 1, '5b2TIHpzZXQg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMenNldCDlr7nosaHkvb/nlKggemlwbGlzdCDnvJbnoIHvvJo8YnIvPjEuIHpzZXQg5a+5ICDkv53lrZjnmoTplK7lgLzlr7nmlbDph4/lsI/kuo7miJbogIXnrYnkuo4genNldC1tYXgtemlwbGlzdC1lbnRyaWVzIOWPguaVsOWAvDxici8+Mi4genNldCDlr7nosaHkv53lrZjnmoTmiYDmnInplK7lgLzlr7nnmoTplK7lkozlgLznmoTlrZfnrKbkuLLplb/luqbpg73lsI/kuo7miJbogIXnrYnkuo4genNldC1tYXgtemlwbGlzdC12YWx1ZSDlj4LmlbDlgLw8YnIvPg==', 2, '0-999999999999999', '64', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (8, 'notify-keyspace-events', 1, '5a6i5oi356uv5Y+v5Lul5Yip55So6ZSu56m66Ze06YCa55+l5p2l5o6l5pS26YKj5Lqb5b2x5ZONIFJlZGlzIOaVsOaNrumbhueahOS6i+S7tumAmuefpe+8jG5vdGlmeS1rZXlzcGFjZS1ldmVudHMg5Y+C5pWw5oyH5a6a5LqG5o6l5pS26YCa55+l55qE57G75Z6L44CC5Y+C5pWw5YC85Y+v55Sx5aSa5Liq5a2X56ym57uE5oiQ77yM56m65YC86KGo56S656aB55So6YCa55+l44CC5ZCE5a2X56ym5ZCr5LmJ5aaC5LiL77yaPGJyLz5L77ya6ZSu56m66Ze05LqL5Lu2PGJyLz5F77ya6ZSu5LqL5Lu25LqL5Lu2PGJyLz5n77ya6YCa55So5ZG95Luk77yI6Z2e54m55a6a57G75Z6L77yJ77yM5L6L5aaCIERFTOOAgSBFWFBJUkUg44CBIFJFTkFNRSDnrYk8YnIvPiTvvJrlrZfnrKbkuLLlkb3ku6Q8YnIvPmzvvJrliJfooajlkb3ku6Q8YnIvPnPvvJrpm4blkIjlkb3ku6Q8YnIvPmjvvJrlk4jluIzlkb3ku6Q8YnIvPnrvvJrmnInluo/pm4blkIjlkb3ku6Q8YnIvPnjvvJrplK7ov4fmnJ/kuovku7Y8YnIvPmXvvJrplK7mt5jmsbDkuovku7Y8YnIvPkHvvJrlj4LmlbAgZyRsc2h6eGUg55qE5Yir5ZCN77yM5Zug5q2kICJBS0UiIOWtl+espiDkuLLooajnpLrmiYDmnInnmoTkuovku7Y8YnIvPg==', 4, 'K|E|g|$|l|s|h|z|x|e|A', '', 7001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (9, 'timeout', 1, '5pat5byA56m66Zey5a6i5oi356uv5LmL5YmN562J5b6F55qE56eS5pWw44CC6Zu25YC86KGo56S65LuO5LiN5pat5byA56m66Zey5a6i5oi356uvPGJyLz4=', 2, '0-100000', '0', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (10, 'hz', 1, '6K6+572uIFJlZGlzIOWQjuWPsOS7u+WKoeaJp+ihjOmikeeOh++8jOavlOWmgua4hemZpOi/h+acn+mUruS7u+WKoeOAguWPguaVsOWAvOi2iuWkp++8jENQVSDmtojogJfotorlpKfvvIzlu7bov5/otorlsI/vvIzlu7rorq7kuI3opoHotoXov4cgMTAwPGJyLz4=', 2, '1-500', '10', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (11, 'disable_commands', 1, '55So5oi36Ieq5a6a5LmJ56aB55So5ZG95Luk', 3, 'flushall|flushdb|keys|hgetall', 'flushall,flushdb', 10001, 'all', 0, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (12, 'support_multi_active', 1, 'bXVsdGkgYWN0aXZlIG9wdGlvbg==', 1, 'yes|no', 'no', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (13, 'user_filter_cmds', 1, 'dXNlciBmaWx0ZXIgY21kcw==', 4, 'set|del', '', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (14, 'qpsquota', 2, 'cmVzdHJpY3Rpb25zIG9uIGVhY2ggcHJveHkgcXBz', 2, '0-500000', '100000', 5001, 'all', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (15, 'print_access_log_control', 2, 'YWNjZXNzIGxvZyBvcHRpb24=', 1, 'true|false', 'false', 5001, 'all', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (16, 'select_cmd_enable', 3, 'c2VsZWN0IGNtZCBvcHRpb24=', 1, 'yes|no', 'no', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (17, 'hashtag_enable', 3, 'aGFzaHRhZyBvcHRpb24=', 1, 'yes|no', 'no', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (18, 'support_scan', 2, 'c3VwcG9ydCBzY2FuIG9wdGlvbg==', 1, 'true|false', 'false', 5001, 'all', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (19, 'scsproxy_timeout', 2, 'cHJveHkgdGltZW91dA==', 2, '0-100000', '2000', 5001, 'all', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (20, 'support_second_exec', 1, 'bXVsdGkgYWN0aXZlIHNlY29uZCBleGVjIG9wdGlvbg==', 1, 'true|false', 'false', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (21, 'proxy_mbuf_size', 2, 'cHJveHkgbWJ1ZiBzaXpl', 2, '512-16777216', '16384', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (22, 'always-propagate-del', 1, 'ZGVs5oyH5Luk6JC9YW9m5paH5Lu25bm25Lyg5pKt', 1, 'yes|no', 'no', 10001, 'all', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (23, 'database-enable-on-loading', 1, '5L2/55So5aSaZGI=', 1, 'yes|no', 'no', 10001, 'all', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (24, 'audit-log', 1, 'YXVkaXQgbG9n', 2, 'yes|no', 'no', 10001, '4.0', 1, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (25, 'rqpsquota', 2, 'cmVzdHJpY3Rpb25zIG9uIGVhY2ggcHJveHkgcnFwcw==', 2, '0-20000', '20000', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (26, 'wqpsquota', 2, 'cmVzdHJpY3Rpb25zIG9uIGVhY2ggcHJveHkgd3Fwcw==', 2, '0-10000', '10000', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (27, 'stale_slave_readable', 2, 'c3RhbGUgc2xhdmUgcmVhZGFibGU=', 2, 'true|false', 'false', 5001, 'all', 1, 1);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (28, 'lazyfree-lazy-eviction', 1, '5piv5ZCm5byA5ZCv5Z+65LqObGF6eWZyZWXnmoTpqbHpgJDlip/og70=', 1, 'yes|no', 'no', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (29, 'lazyfree-lazy-expire', 1, '5piv5ZCm5byA5ZCv5Z+65LqObGF6eWZyZWXnmoTov4fmnJ9LZXnliKDpmaTlip/og70=', 1, 'yes|no', 'yes', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (30, 'lazyfree-lazy-server-del', 1, 'REVM5ZG95Luk5piv5ZCm5Z+65LqObGF6eWZyZWXlvILmraXliKDpmaTmlbDmja4=', 1, 'yes|no', 'yes', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (31, 'lazyfree-lazy-user-del', 1, '5omn6KGMREVM5ZG95Luk5pe25piv5ZCm5Z+65LqObGF6eWZyZWXlvILmraXliKDpmaTmlbDmja4=', 1, 'yes|no', 'yes', 10001, 'all', 0, 0);
INSERT INTO user_conf_list (id, conf_name, conf_module, conf_desc, conf_type, conf_range, conf_default, conf_cache_version, conf_redis_version, conf_user_visible, need_reboot) VALUES (32, 'forbid-rewrite-set-command', 1, '5piv5ZCmcmV3cml0ZSBzZXRleA==', 1, 'yes|no', 'no', 5001, '6.0', 1, 0);
