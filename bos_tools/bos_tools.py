#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# Init bos setting.

作者: cuiyi01(<EMAIL>)
日期: 2018年8月17日 上午11:12:55
"""

from baidubce.services.bos.bos_client import BosClient
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.auth.bce_credentials import BceCredentials
bos_host = "su.bcebos.com"
access_key_id = "91884b9e79a442f29f8b41106d060e72"
secret_access_key = "c9379def1b3f4a0ea9dbaca23c74c55b"
config = BceClientConfiguration(credentials=BceCredentials(access_key_id, secret_access_key), endpoint=bos_host)
bos_client = BosClient(config)
bos_client.list_buckets()

url = bos_client.generate_pre_signed_url("scs-pkgs-for-op-tools-sz", "BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz", expiration_in_seconds = -1)

print url

