#!/usr/bin/env bash

cd /root

if [ $(lsmod | grep bcettm | wc -l) -gt 0 ]; then
    echo "BCETTM is already installed."
    exit 0
fi

[[ -d BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64 ]] && rm -rf BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64
[[ -f BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz ]] && rm -f BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz
wget -O 'BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz' 'http://************:35004/scs-pkgs-for-op-tools-sz/BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz?authorization=bce-auth-v1%2F91884b9e79a442f29f8b41106d060e72%2F2025-05-29T04%3A19%3A19Z%2F-1%2F%2F644c46dde9f3beaadd08431caa88cfae8f6aeaa3b408ac1e0b8c3d32161ebf18'
mkdir -p BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64
tar -xzf BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz -C BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64 --strip-components=1

mv ./BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64/bcettm.ko /lib/modules/$(uname -r)/kernel/net/ipv4/
insmod /lib/modules/$(uname -r)/kernel/net/ipv4/bcettm.ko

if [ $(grep -c "bcettm.ko" /etc/rc.local) -eq 0 ]; then
    echo "insmod /lib/modules/$(uname -r)/kernel/net/ipv4/bcettm.ko" >> /etc/rc.local
fi

sleep 1

if [ $(lsmod | grep bcettm | wc -l) -gt 0 ]; then
    echo "BCETTM installed successfully."
    exit 0
else
    echo "BCETTM installation failed."
    exit 1
fi
