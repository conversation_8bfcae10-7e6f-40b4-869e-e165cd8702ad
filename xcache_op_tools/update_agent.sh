#!/usr/bin/env bash
set -x
UUID=$1
NEW_AGENT_PATH=/home/<USER>/opdir/agent
DB_INFO=/home/<USER>/opdir/agent/dbinfo
CONTAINER_PATH=/home/<USER>/$UUID
XCACHE_PATH_NAME=`ls -l $CONTAINER_PATH | grep xcache | awk '{print $9}'`
AGENT_PATH=$CONTAINER_PATH/${XCACHE_PATH_NAME}/agent
ID_PATH=$AGENT_PATH/recover/id.txt

NOW_TIME=`date +%Y%m%d`
mv  $AGENT_PATH/src $AGENT_PATH/src_bak_$NOW_TIME
mv  $AGENT_PATH/configure $AGENT_PATH/configure_bak_$NOW_TIME
cp -rf $AGENT_PATH/recover $AGENT_PATH/recover_bak_$NOW_TIME

cp -r $NEW_AGENT_PATH/src $AGENT_PATH/src
cp -r $NEW_AGENT_PATH/configure $AGENT_PATH/configure

CLUSTER_SHOW_ID=`cat $DB_INFO | grep $UUID | awk '{print $2}'`

python -c """
import json; import sys
id_path = sys.argv[1]
cluster_show_id = sys.argv[2]
f = open(id_path, 'rt')
id_json = json.loads(f.read())
id_json['show_id'] = cluster_show_id
f.close()
f = open(id_path, 'wt')
f.write(json.dumps(id_json, sort_keys=True, indent=4))
""" $ID_PATH $CLUSTER_SHOW_ID

PWD=`pwd`
cd $AGENT_PATH

AGENT_PID_NUM=`ps -ef | grep $AGENT_PATH/src/agent.py | grep -v grep | awk '{print $2}' | wc -l`
AGENT_PID=`ps -ef | grep $AGENT_PATH/src/agent.py | grep -v grep | awk '{print $2}'`

[[ x$AGENT_PID_NUM == x1 ]] && kill -9 $AGENT_PID
[[ $AGENT_PID_NUM -gt 1 ]] && exit 1
sleep 1
nohup python $AGENT_PATH/src/agent.py start &
pwd
cd $PWD