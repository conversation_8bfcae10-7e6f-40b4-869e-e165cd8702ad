#!/usr/bin/env bash

set -x

CONTAINER_PATH=/home/<USER>

TIMESTAMP=`date "+%Y%m%d%H%M"`
BACKUP_DATE=`date +%Y%m%d -d '7 day ago'`

for node_path in `ls ${CONTAINER_PATH}`
do
    agent_path=
    if [ -d ${CONTAINER_PATH}/${node_path} ]; then
        agent_path=${CONTAINER_PATH}/${node_path}/`ls ${CONTAINER_PATH}/${node_path}`/agent
    fi
    if [ ! -z ${agent_path} ]; then
        if [ x$1 == "xsplit_log" ]; then
            log_path=${agent_path}/log
            for i in `ls ${log_path}/*.log`
            do
                if test -L $i
                    then echo ""
                else
                    mv ${i} ${i}.${TIMESTAMP}
                    touch ${i}
                fi
            done
            for i in `ls ${log_path}/*.log.wf`
            do
                if test -L ${i}
                    then echo ""
                else
                    mv ${i} ${i}.${TIMESTAMP}
                    touch ${i}
                fi
            done
        fi
        if [ x$1 == "xtar_log" ]; then
            log_path=${agent_path}/log
            pwd=`pwd`
            cd $log_path
            find . -mtime +7 -name "*.log.*"  -exec tar -zcvf backup.tar.gz.$BACKUP_DATE {} +
            cd $pwd
        fi
        if [ x$1 == "xclear_log" ]; then
            log_path=${agent_path}/log
            find $log_path -mtime +7 -name "*.log.*"  -exec rm -rf {} \;
            find $log_path -mtime +30 -name "backup.tar.gz.*"  -exec rm -rf {} \;
        fi
    fi
done
