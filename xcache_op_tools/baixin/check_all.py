#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# inspect redis ttl

作者: cuiyi01(<EMAIL>)
日期: 2025年05月18日 上午11:12:55
"""
import commands
import re
import sys

try:
    import pymysql
except ImportError:
    import MySQLdb as pymysql
import redis


TRACE_FLAG = False
PASSWORD = ""


def get_password(cluster_show_id, redis_auth):
    """get password
    """
    if redis_auth is None or redis_auth == "":
        print "%s redis auth is None or empty" % cluster_show_id
        return ""

    stauts, output = commands.getstatusoutput("./octopus aes_decode sk %s" % redis_auth)
    if stauts != 0:
        print "%s get password failed, status is %d, output is %s" % (cluster_show_id, stauts, output)
        return ""
    return output.strip().split(":")[1].strip()


def get_mysql_config(db=None):
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["passwd"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    if db is not None:
        ret["db"] = db
    return ret


def get_cluster_info(cluster_show_id):
    """get cluster info
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    ret = {}
    cursor = sql_conn.cursor()
    try:
        cursor.execute("select cache_cluster.id, iam_user_id, status, vpc_id, elb_id, second_elb_id, "
                       "migration_status, version, redis_auth, metaserver_id "
                       "from bce_scs.cache_cluster, bce_scs.userinfo "
                       "where user_id = userinfo.id and cluster_show_id = '%s'" % cluster_show_id)
        rows = cursor.fetchall()
        if len(rows) != 1:
            print "%s get cluster info failed, rows is %d" % (cluster_show_id, len(rows))
            return ret
        for row in rows:
            ret["cluster_show_id"] = cluster_show_id
            ret["id"] = int(row[0])
            ret["iam_user_id"] = row[1]
            ret["status"] = int(row[2])
            ret["vpc_id"] = row[3]
            ret["second_vpc_id"] = row[3]
            ret["elb_id"] = row[4]
            ret["second_elb_id"] = row[5]
            ret["migration_status"] = int(row[6])
            ret["version"] = int(row[7])
            ret["redis_auth"] = row[8]
            ret["metaserver_id"] = row[9] if row[9] != "" else "Metaserver_01"
            ret["password"] = PASSWORD if PASSWORD != "" else get_password(cluster_show_id, row[8])
    finally:
        cursor.close()
        sql_conn.close()
    if TRACE_FLAG:
        print "[TRACE]cluster info is %s" % ret
    return ret


def get_instance_info(cluster_id):
    """get instance info
    """
    sql_conn = pymysql.connect(**get_mysql_config())
    ret = []
    cursor = sql_conn.cursor()
    try:
        cursor.execute("SELECT id, floating_ip, port, password, migrate_status, cache_instance_type, "
                       "master_redis, slaver_redis, uuid, shard_id, fix_ip "
                       "from cache_instance where cluster_id = %d" % cluster_id)
        rows = cursor.fetchall()
        for row in rows:
            sub_ret = dict()
            sub_ret["id"] = int(row[0])
            sub_ret["floating_ip"] = row[1]
            sub_ret["port"] = int(row[2])
            sub_ret["password"] = row[3]
            sub_ret["migrate_status"] = int(row[4])
            sub_ret["cache_instance_type"] = int(row[5])
            sub_ret["master_redis"] = row[6]
            sub_ret["slaver_redis"] = row[7]
            sub_ret["uuid"] = row[8]
            sub_ret["shard_id"] = int(row[9])
            sub_ret["fix_ip"] = row[10]
            ret.append(sub_ret)
    finally:
        cursor.close()
        sql_conn.close()
    if TRACE_FLAG:
        print "[TRACE]instance info is %s" % ret
    return ret


def get_shards(instances):
    """get shards
    """
    shards = dict()
    for instance in instances:
        if instance["cache_instance_type"] == 0:
            continue
        if instance["shard_id"] not in shards:
            shards[instance["shard_id"]] = []
        shards[instance["shard_id"]].append(instance)
    return shards


def check_status(cluster, instances):
    """check status
    """
    if cluster["status"] != 5:
        print "cluster status is not 5, current status is %d" % cluster["status"]
    if cluster["migration_status"] != 0 and cluster["migration_status"] != 2:
        print "cluster migration status is not 0 or 2, current status is %d" % cluster["migration_status"]
    for instance in instances:
        if instance["migrate_status"] != 0:
            print ("instance %s migrate status is not 0, current status is %d"
                   % (instance["uuid"], instance["migrate_status"]))


def check_blb(cluster, instances):
    """check blb
    """
    blb_info = get_blb_info(cluster["cluster_show_id"], cluster["iam_user_id"], cluster["elb_id"])
    if blb_info is None:
        return
    if cluster["second_elb_id"] != "":
        second_blb_info = get_blb_info(cluster["cluster_show_id"], cluster["iam_user_id"], cluster["second_elb_id"])
        if second_blb_info is None:
            return
        blb_info.extend(second_blb_info)
    if TRACE_FLAG:
        print "[TRACE] blb info is %s" % blb_info
    if cluster["version"] == 5001:
        for instance in instances:
            if instance["cache_instance_type"] != 0:
                continue
            for blb in blb_info:
                if instance["uuid"] == blb["uuid"]:
                    if TRACE_FLAG:
                        print "[TRACE] instance uuid is in blb, instance uuid is %s" % instance["uuid"]
                    break
            else:
                print "instance uuid is not in blb, instance uuid is %s" % instance["uuid"]
        for blb in blb_info:
            for instance in instances:
                if instance["uuid"] == blb["uuid"]:
                    if instance["cache_instance_type"] != 0:
                        print "blb uuid should be proxy but not, blb id is %s" % blb["uuid"]
                    else:
                        if TRACE_FLAG:
                            print "[TRACE] blb uuid is in instance, blb id is %s" % blb["uuid"]
                    break
            else:
                print "blb uuid is not in instance, blb id is %s" % blb["uuid"]
    elif cluster["version"] == 7001:
        if len(blb_info) != 1:
            print "blb info is not 1, blb info is %s" % blb_info
        for instance in instances:
            if instance["cache_instance_type"] != 3:
                continue
            for blb in blb_info:
                if instance["uuid"] == blb["uuid"]:
                    if TRACE_FLAG:
                        print "[TRACE] instance uuid is in blb, instance id is %s" % instance["uuid"]
                    break
            else:
                print "instance uuid is not in blb, instance id is %s" % instance["uuid"]


def check_shards(cluster, instances):
    """check shards
    """
    shards = get_shards(instances)
    metaserver_config = get_metaserver_config()[cluster["metaserver_id"]]
    if TRACE_FLAG:
        print "[TRACE] metaserver config is %s" % metaserver_config
    for shard, shard_insts in shards.items():
        master_inst = None
        for instance in shard_insts:
            if instance["cache_instance_type"] == 3:
                master_inst = instance
                break
        if master_inst is None:
            print "shard %d master instance is None" % shard
            continue
        for instance in shard_insts:
            redis_info = get_redis_info(instance["floating_ip"], instance["port"], cluster["password"])
            if TRACE_FLAG:
                print "[TRACE] instance %s, shard %d redis info is %s" % (instance, shard, redis_info)
            if len(redis_info) == 0:
                print "instance %s redis info is None" % instance["uuid"]
                continue
            if instance["cache_instance_type"] == 3:
                if redis_info["role"] != "master":
                    print "instance %s role is not master, current role is %s" % (instance["uuid"], redis_info["role"])
                if instance["master_redis"] != "":
                    print "instance %s master redis is not empty, current master redis is %s" % (
                        instance["uuid"], instance["master_redis"])
            if instance["cache_instance_type"] == 2:
                if redis_info["role"] != "slave":
                    print "instance %s role is not slave, current role is %s" % (instance["uuid"], redis_info["role"])
                if redis_info["master_link_status"] != "up":
                    print "instance %s master link status is not up, current status is %s" % (
                        instance["uuid"], redis_info["master_link_status"])
                if (redis_info["master_host"] != master_inst["fix_ip"] or
                        redis_info["master_port"] != master_inst["port"]):
                    print "instance %s master host is not %s:%d, current host is %s:%d" % (
                        instance["uuid"], master_inst["fip_ip"], master_inst["port"],
                        redis_info["master_host"], redis_info["master_port"])
                if instance["master_redis"] != master_inst["fix_ip"]:
                    print "instance %s master redis is not %s, current master redis is %s" % (
                        instance["uuid"], master_inst["fix_ip"], instance["master_redis"])
            if cluster["version"] == 5001:
                mriget_info = get_metaserver_redis_inst_info(
                    metaserver_config["host"], metaserver_config["port"],
                    metaserver_config["password"], instance["id"])
                if TRACE_FLAG:
                    print "[TRACE] instance %s mriget info is %s" % (instance["uuid"], mriget_info)
                if mriget_info is None or len(mriget_info) == 0:
                    print "instance %s mriget info is None" % instance["uuid"]
                    continue
                if mriget_info["shard"] != shard:
                    print "instance %s shard is not %d, current shard is %d" % (
                        instance["uuid"], shard, mriget_info["shard"])
                if mriget_info["type"] != instance["cache_instance_type"]:
                    print "instance %s type is not %d, current type is %d" % (
                        instance["uuid"], instance["cache_instance_type"], mriget_info["type"])
                if instance["floating_ip"] + "," + instance["fix_ip"] != mriget_info["ip"]:
                    print "instance %s ip is not %s, current ip is %s" % (
                        instance["uuid"], instance["floating_ip"] + "," + instance["fix_ip"], mriget_info["ip"])


def check_proxy(cluster, instances):
    if cluster["version"] != 5001:
        return
    metaserver_config = get_metaserver_config()[cluster["metaserver_id"]]
    for instance in instances:
        if instance["cache_instance_type"] != 0:
            continue
        mpiget_info = get_metaserver_proxy_inst_info(
            metaserver_config["host"], metaserver_config["port"],
            metaserver_config["password"], instance["id"])
        if TRACE_FLAG:
            print "[TRACE] instance %s mpiget info is %s" % (instance, mpiget_info)
        if mpiget_info is None or len(mpiget_info) == 0:
            print "instance %s mpiget info is None" % instance["uuid"]
            continue
        if instance["floating_ip"] + "," + instance["fix_ip"] != mpiget_info["ip"]:
            print "instance %s ip is not %s, current ip is %s" % (
                instance["uuid"], instance["floating_ip"] + "," + instance["fix_ip"], mpiget_info["ip"])


def check_all(cluster_show_id):
    """check all
    """
    cluster = get_cluster_info(cluster_show_id)
    if len(cluster) == 0:
        return
    instances = get_instance_info(cluster["id"])
    if len(instances) == 0:
        print "%s get instance info failed" % cluster_show_id
        return
    print "-------------check status--------------"
    check_status(cluster, instances)
    print "-------------check blb--------------"
    check_blb(cluster, instances)
    print "-------------check shards--------------"
    check_shards(cluster, instances)
    print "-------------check proxy--------------"
    check_proxy(cluster, instances)


def get_metaserver_config():
    """get metaserver config
    """
    metaserver_conf = "/home/<USER>/scs/csmaster/conf/metaserver_sdk.conf"
    with open(metaserver_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    cur_session = ""
    for line in lines:
        if line.startswith("["):
            if cur_session == "Metaserver_01":
                ret[""] = ret[cur_session]
            cur_session = line.strip().split("[")[1].strip().split("]")[0].strip()
            ret[cur_session] = dict()
        elif cur_session != "":
            if line.startswith("Host"):
                ret[cur_session]["host"] = line.strip().split(":")[1].strip()
            elif line.startswith("Port"):
                ret[cur_session]["port"] = int(line.strip().split(":")[1].strip())
            elif line.startswith("MetaOpAuth"):
                ret[cur_session]["password"] = line.strip().split(":")[1].strip()
    return ret


def get_metaserver_redis_inst_info(meta_host, meta_port, meta_password, inst_id):
    """get metaserver init info
    """
    result = dict()
    try:
        r = redis.Redis(meta_host, meta_port, password=meta_password,
                        socket_connect_timeout=1, socket_timeout=1)
        ret = r.execute_command("mriget", inst_id)
        if isinstance(ret, list):
            for i in xrange(0, len(ret), 2):
                key = ret[i]
                value = ret[i + 1]
                if key == "shard":
                    value = int(value)
                elif key == "type":
                    value = 3 if value == "1" else 2
                result[key] = value
    except Exception as e:
        print "get metaserver redis info failed, %s" % e
        pass
    return result


def get_metaserver_proxy_inst_info(meta_host, meta_port, meta_password, inst_id):
    """get metaserver init info
    """
    result = dict()
    try:
        r = redis.Redis(meta_host, meta_port, password=meta_password,
                        socket_connect_timeout=1, socket_timeout=1)
        ret = r.execute_command("mpiget", inst_id)
        if isinstance(ret, list):
            for i in xrange(0, len(ret), 2):
                key = ret[i]
                value = ret[i + 1]
                result[key] = value
    except Exception as e:
        print "get metaserver proxy info failed, %s" % e
        pass
    return result



def get_redis_info(floating_ip, port, password):
    """get redis info
    """
    try:
        r = redis.Redis(floating_ip, port, password=password,
                        socket_connect_timeout=1, socket_timeout=1)
        return r.info("replication")
    except Exception as e:
        return {}


def get_blb_info(cluster_show_id, iam_user_id, elb_id):
    """get blb info
    """
    status, output = commands.getstatusoutput("./octopus list_backend_server %s %s" % (iam_user_id, elb_id))
    if status != 0:
        print "%s get blb info failed, status is %d, output is %s" % (cluster_show_id, status, output)
        return None
    return parse_blb_string(output)


def parse_blb_string(s):
    """
    Parses the status string containing UUIDs and port information.

    Args:
        s (str): The input status string

    Returns:
        list: List of dictionaries, each containing UUID, weight, and port information
    """

    # Split the input string into lines
    lines = s.strip().split('\n')

    # Initialize result list
    result = []

    # UUID pattern for matching
    uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')

    i = 0
    while i < len(lines):
        # Check if the current line is a UUID
        if uuid_pattern.match(lines[i]):
            uuid = lines[i]

            # Next line should be the weight
            i += 1
            weight = lines[i]

            # Create entry for this UUID
            entry = {
                'uuid': uuid,
                'weight': weight,
                'ports': []
            }

            # Move to the next line
            i += 1

            # Process port information (grouped in sets of 3 lines)
            while i < len(lines) and lines[i].startswith('  '):
                listener_port = lines[i].split(':', 1)[1].strip()
                health_port = lines[i + 1].split(':', 1)[1].strip()
                status = lines[i + 2].split(':', 1)[1].strip()

                port_info = {
                    'listenerport': listener_port,
                    'healthcheckport': health_port,
                    'status': status
                }

                entry['ports'].append(port_info)
                i += 3

            result.append(entry)
        else:
            i += 1

    return result

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print "Usage: %s cluster_show_id password" % sys.argv[0]
        sys.exit(1)
    if len(sys.argv) > 3 and sys.argv[3] == "trace":
        TRACE_FLAG = True
    PASSWORD = sys.argv[2]
    check_all(sys.argv[1])
