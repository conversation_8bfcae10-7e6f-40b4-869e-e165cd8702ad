#!/bin/bash

################################################################################
# Nutcracker Status Update Script
################################################################################
#
# Description:
#   This script scans all nutcracker deployments on the local machine,
#   extracts configuration and process information, and updates the database
#   with current status information.
#
# Author: cuiyi01
# Version: 2.0
# Last Modified: 2025-06-27
#
################################################################################

# ==============================================================================
# CONFIGURATION SECTION
# ==============================================================================

# System configuration
readonly LOCAL_IP=$(hostname -i | awk '{print $1}')
readonly REDIS_ROOT="/home/<USER>"

# Database connection information
readonly DB_HOST="*************"
readonly DB_PORT="6203"
readonly DB_USER="paas_x1_w"
readonly DB_PASS="paas_x1_w_pwd"
readonly DB_NAME="bce_scs_x1_task"

# Color definitions for console output
readonly COLOR_RESET='\033[0m'
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[0;33m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_BOLD='\033[1m'

# ==============================================================================
# UTILITY FUNCTIONS
# ==============================================================================

# Function: Print colored log message with timestamp
log_message() {
    local level="$1"
    local message="$2"
    local color="$3"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    if [ -n "$color" ]; then
        printf "${color}[%s] [%s] %s${COLOR_RESET}\n" "$timestamp" "$level" "$message"
    else
        printf "[%s] [%s] %s\n" "$timestamp" "$level" "$message"
    fi
}

# Function: Log info message
log_info() {
    log_message "INFO" "$1" "$COLOR_CYAN"
}

# Function: Log success message
log_success() {
    log_message "SUCCESS" "$1" "$COLOR_GREEN"
}

# Function: Log warning message
log_warning() {
    log_message "WARNING" "$1" "$COLOR_YELLOW"
}

# Function: Log error message
log_error() {
    log_message "ERROR" "$1" "$COLOR_RED"
}

# Function: Print section header
print_header() {
    local title="$1"
    local length=${#title}
    local padding=$((50 - length))
    local left_pad=$((padding / 2))
    local right_pad=$((padding - left_pad))

    printf "\n${COLOR_BOLD}${COLOR_BLUE}"
    printf "="%.0s {1..60}
    printf "\n"
    printf "%*s%s%*s\n" $left_pad "" "$title" $right_pad ""
    printf "="%.0s {1..60}
    printf "${COLOR_RESET}\n\n"
}

# Function: Execute MySQL command with error handling
mysql_command() {
    local sql="$1"
    local result

    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$sql" 2>&1)
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        return 0
    else
        log_error "Database operation failed: $result"
        return 1
    fi
}

# Function: Parse nutcracker configuration file
parse_nutcracker_config() {
    local conf_file="$1"
    local servers=""
    local in_servers_section=false

    while IFS= read -r line; do
        # Remove leading and trailing whitespace
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

        # Check if reached servers section
        if [[ "$line" == "servers:" ]]; then
            in_servers_section=true
            continue
        fi

        # If in servers section
        if [ "$in_servers_section" = true ]; then
            # Skip empty lines
            if [[ "$line" =~ ^[[:space:]]*$ ]]; then
                continue
            fi

            # Check if encountering new configuration section
            if [[ ! "$line" =~ ^[[:space:]] ]] && [[ ! "$line" =~ ^- ]] && [[ "$line" =~ : ]] && [[ ! "$line" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+: ]]; then
                break
            fi

            # Skip server name lines (starting with - and containing letters)
            if [[ "$line" =~ ^-[[:space:]]*[a-zA-Z] ]]; then
                continue
            fi

            # Extract server IP:PORT information
            if [[ "$line" =~ [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+:[0-9]+ ]]; then
                server_info=$(echo "$line" | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+\.[0-9]\+:[0-9]\+')
                if [ -n "$server_info" ]; then
                    if [ -z "$servers" ]; then
                        servers="$server_info"
                    else
                        servers="$servers, $server_info"
                    fi
                fi
            fi
        fi
    done < "$conf_file"

    echo "$servers"
}

# Function: Get nutcracker process information
get_nutcracker_process_info() {
    local conf_file="$1"
    local meta_ip=""
    local meta_port=""

    # Find running nutcracker process through ps command
    local process_info=$(ps aux | grep "nutcracker.*${conf_file}" | grep -v super | grep -v grep)
    if [ -n "$process_info" ]; then
        # Extract -A parameter (meta IP)
        meta_ip=$(echo "$process_info" | grep -o '\-A [0-9.]*' | awk '{print $2}')
        # Extract -S parameter (meta port)
        meta_port=$(echo "$process_info" | grep -o '\-S [0-9]*' | awk '{print $2}')
    fi

    echo "${meta_ip:-N/A}|${meta_port:-N/A}"
}

# Function: Update nutcracker information in database
update_nutcracker_database() {
    local uid="$1"
    local name="$2"
    local port="$3"
    local meta_ip="$4"
    local meta_port="$5"
    local servers="$6"
    local conf_file="$7"

    # Build SQL statement
    local sql="INSERT INTO all_infos (type, uid, ip, port, name, nut_meta_ip, nut_meta_port, nut_servers, last_update_time)
VALUES ('nutcracker', '${uid}', '${LOCAL_IP}', ${port}, '${name}', '${meta_ip}', '${meta_port}', '${servers}', NOW())
ON DUPLICATE KEY UPDATE
    ip = VALUES(ip),
    port = VALUES(port),
    name = VALUES(name),
    nut_meta_ip = VALUES(nut_meta_ip),
    nut_meta_port = VALUES(nut_meta_port),
    nut_servers = VALUES(nut_servers),
    last_update_time = VALUES(last_update_time);"

    log_info "Updating database for UID: $uid"
    if mysql_command "$sql"; then
        log_success "Database update successful for $uid"
    else
        log_error "Database update failed for $uid"
    fi
}

# ==============================================================================
# MAIN LOGIC
# ==============================================================================

print_header "NUTCRACKER STATUS UPDATE"
log_info "Starting nutcracker deployment scan"
log_info "Local IP: $LOCAL_IP"

# Traverse all directories under /home/<USER>
for redis_dir in ${REDIS_ROOT}/*/; do
    # Check if nutcracker directory exists
    if [ ! -d "${redis_dir}nutcracker" ]; then
        continue
    fi

    # Get name (directory name)
    name=$(basename "$redis_dir")
    nutcracker_path="${redis_dir}nutcracker"
    conf_path="${nutcracker_path}/conf"

    # Check if conf directory exists
    if [ ! -d "$conf_path" ]; then
        log_warning "Configuration directory not found: $conf_path"
        continue
    fi

    log_info "Processing nutcracker instance: $name"

    # Traverse all configuration files
    for conf_file in "${conf_path}"/nutcracker_*.yml; do
        if [ ! -f "$conf_file" ]; then
            log_warning "Configuration file not found: $conf_file"
            continue
        fi

        # Extract port number from filename
        port=$(basename "$conf_file" | sed 's/nutcracker_\(.*\)\.yml/\1/')

        # Generate uid
        uid="nutcracker_${name}_${LOCAL_IP}_${port}"

        log_info "Processing configuration: $(basename "$conf_file")"

        # Get process information
        process_info=$(get_nutcracker_process_info "$conf_file")
        meta_ip=$(echo "$process_info" | cut -d'|' -f1)
        meta_port=$(echo "$process_info" | cut -d'|' -f2)

        # Parse configuration file to get servers information
        servers=$(parse_nutcracker_config "$conf_file")

        # Display instance information
        printf "\n${COLOR_BOLD}Nutcracker Instance Details:${COLOR_RESET}\n"
        printf "  %-20s %s\n" "UID:" "$uid"
        printf "  %-20s %s\n" "Local IP:" "$LOCAL_IP"
        printf "  %-20s %s\n" "Port:" "$port"
        printf "  %-20s %s\n" "Name:" "$name"
        printf "  %-20s %s\n" "Meta IP:" "${meta_ip}"
        printf "  %-20s %s\n" "Meta Port:" "${meta_port}"
        printf "  %-20s %s\n" "Servers:" "${servers:-Not found}"
        printf "  %-20s %s\n" "Config File:" "$conf_file"

        # Update database
        update_nutcracker_database "$uid" "$name" "$port" "$meta_ip" "$meta_port" "$servers" "$conf_file"

        printf "\n${COLOR_CYAN}%s${COLOR_RESET}\n" "$(printf '%.0s-' {1..60})"
    done
done

print_header "SCAN COMPLETED"
log_success "Nutcracker deployment scan completed successfully"