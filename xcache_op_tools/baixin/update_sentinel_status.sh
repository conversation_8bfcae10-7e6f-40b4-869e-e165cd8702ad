#!/bin/bash

################################################################################
# Redis Sentinel Status Update Script
################################################################################
#
# Description:
#   This script scans all Redis Sentinel instances on the local machine,
#   extracts configuration and runtime information, and updates the database
#   with current status information.
#
# Author: SCS Operations Team
# Version: 2.0
# Last Modified: 2025-06-27
#
################################################################################

# ==============================================================================
# CONFIGURATION SECTION
# ==============================================================================

# System configuration
readonly LOCAL_IP=$(hostname -i)
readonly REDIS_ROOT="/home/<USER>"

# Database connection parameters
readonly DB_HOST="*************"
readonly DB_PORT="6203"
readonly DB_USER="paas_x1_w"
readonly DB_PASSWORD="paas_x1_w_pwd"
readonly DB_NAME="bce_scs_x1_task"

# Color definitions for console output
readonly COLOR_RESET='\033[0m'
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[0;33m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_BOLD='\033[1m'

# ==============================================================================
# UTILITY FUNCTIONS
# ==============================================================================

# Function: Print colored log message with timestamp
log_message() {
    local level="$1"
    local message="$2"
    local color="$3"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    if [ -n "$color" ]; then
        printf "${color}[%s] [%s] %s${COLOR_RESET}\n" "$timestamp" "$level" "$message"
    else
        printf "[%s] [%s] %s\n" "$timestamp" "$level" "$message"
    fi
}

# Function: Log info message
log_info() {
    log_message "INFO" "$1" "$COLOR_CYAN"
}

# Function: Log success message
log_success() {
    log_message "SUCCESS" "$1" "$COLOR_GREEN"
}

# Function: Log warning message
log_warning() {
    log_message "WARNING" "$1" "$COLOR_YELLOW"
}

# Function: Log error message
log_error() {
    log_message "ERROR" "$1" "$COLOR_RED"
}

# Function: Print section header
print_header() {
    local title="$1"
    local length=${#title}
    local padding=$((50 - length))
    local left_pad=$((padding / 2))
    local right_pad=$((padding - left_pad))

    printf "\n${COLOR_BOLD}${COLOR_BLUE}"
    printf "="%.0s {1..60}
    printf "\n"
    printf "%*s%s%*s\n" $left_pad "" "$title" $right_pad ""
    printf "="%.0s {1..60}
    printf "${COLOR_RESET}\n\n"
}

# Function: Execute MySQL command with error handling
mysql_command() {
    local sql="$1"
    local result

    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "$sql" 2>&1)
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        return 0
    else
        log_error "Database operation failed: $result"
        return 1
    fi
}

# Function: Update sentinel information in database
update_sentinel_database() {
    local uid="$1"
    local name="$2"
    local port="$3"
    local master_name="$4"
    local master_address="$5"
    local master_status="$6"
    local master_slaves="$7"
    local master_sentinels="$8"

    # Build SQL statement
    local sql="INSERT INTO all_infos (
        type,
        uid,
        ip,
        port,
        name,
        sentinel_master_name,
        sentinel_master_address,
        sentinel_master_status,
        sentinel_master_slaves,
        sentinel_master_sentinels,
        last_update_time
    ) VALUES (
        'sentinel',
        '$uid',
        '$LOCAL_IP',
        $port,
        '$name',
        '$master_name',
        '$master_address',
        '$master_status',
        '$master_slaves',
        '$master_sentinels',
        NOW()
    ) ON DUPLICATE KEY UPDATE
        type = VALUES(type),
        ip = VALUES(ip),
        port = VALUES(port),
        name = VALUES(name),
        sentinel_master_name = VALUES(sentinel_master_name),
        sentinel_master_address = VALUES(sentinel_master_address),
        sentinel_master_status = VALUES(sentinel_master_status),
        sentinel_master_slaves = VALUES(sentinel_master_slaves),
        sentinel_master_sentinels = VALUES(sentinel_master_sentinels),
        last_update_time = NOW();"

    log_info "Updating database for UID: $uid"
    if mysql_command "$sql"; then
        log_success "Database update successful for $uid"
    else
        log_error "Database update failed for $uid"
    fi
}

# ==============================================================================
# MAIN LOGIC
# ==============================================================================

print_header "SENTINEL STATUS UPDATE"
log_info "Starting Redis Sentinel scan"
log_info "Local IP: $LOCAL_IP"

# Iterate through all directories under /home/<USER>/
for redis_dir in ${REDIS_ROOT}/*/; do
    if [ -d "$redis_dir" ]; then
        name=$(basename "$redis_dir")
        sentinel_dir="${redis_dir}sentinel"
        conf_dir="${sentinel_dir}/conf"

        # Check if sentinel directory and conf directory exist
        if [ -d "$sentinel_dir" ] && [ -d "$conf_dir" ]; then
            log_info "Processing Redis instance: $name"

            # Find all sentinel_*.conf files
            for conf_file in "$conf_dir"/sentinel_*.conf; do
                if [ -f "$conf_file" ]; then
                    # Extract port number from filename
                    port=$(basename "$conf_file" | sed 's/sentinel_\([0-9]*\)\.conf/\1/')

                    log_info "Processing Sentinel configuration: $(basename "$conf_file")"

                    # Display instance information
                    printf "\n${COLOR_BOLD}Sentinel Instance Details:${COLOR_RESET}\n"
                    printf "  %-20s %s\n" "Instance Name:" "$name"
                    printf "  %-20s %s\n" "Port:" "$port"

                    # Test connection
                    if redis-cli -h "$LOCAL_IP" -p "$port" ping >/dev/null 2>&1; then
                        printf "  %-20s %s\n" "Connection Status:" "${COLOR_GREEN}Success${COLOR_RESET}"

                        # Get sentinel information
                        sentinel_info=$(redis-cli -h "$LOCAL_IP" -p "$port" info sentinel 2>/dev/null)

                        if [ -n "$sentinel_info" ]; then
                            # Parse information for each master
                            echo "$sentinel_info" | grep "^master[0-9]*:" | while IFS= read -r line; do
                                printf "\n${COLOR_BOLD}Master Information:${COLOR_RESET}\n"

                                # Parse master information line
                                # Format: master0:name=mymaster2,status=ok,address=*************:7003,slaves=1,sentinels=1

                                # Extract fields
                                master_name=$(echo "$line" | sed 's/.*name=\([^,]*\).*/\1/')
                                master_address=$(echo "$line" | sed 's/.*address=\([^,]*\).*/\1/')
                                master_status=$(echo "$line" | sed 's/.*status=\([^,]*\).*/\1/')
                                master_slaves=$(echo "$line" | sed 's/.*slaves=\([^,]*\).*/\1/')
                                master_sentinels=$(echo "$line" | sed 's/.*sentinels=\([^,]*\).*/\1/')

                                # Generate uid with sentinel prefix
                                uid="sentinel_${name}_${LOCAL_IP}_${port}_${master_name}"

                                printf "  %-20s %s\n" "UID:" "$uid"
                                printf "  %-20s %s\n" "Master Name:" "$master_name"
                                printf "  %-20s %s\n" "Master Address:" "$master_address"
                                printf "  %-20s %s\n" "Master Status:" "$master_status"
                                printf "  %-20s %s\n" "Master Slaves:" "$master_slaves"
                                printf "  %-20s %s\n" "Master Sentinels:" "$master_sentinels"

                                # Update database
                                update_sentinel_database "$uid" "$name" "$port" "$master_name" \
                                                        "$master_address" "$master_status" \
                                                        "$master_slaves" "$master_sentinels"
                            done
                        else
                            log_warning "Unable to get sentinel information for $name:$port"
                        fi
                    else
                        printf "  %-20s %s\n" "Connection Status:" "${COLOR_RED}Failed${COLOR_RESET}"
                        log_warning "Failed to connect to Sentinel instance: $name:$port"
                    fi

                    printf "\n${COLOR_CYAN}%s${COLOR_RESET}\n" "$(printf '%.0s-' {1..60})"
                else
                    log_warning "Configuration file not found: $conf_file"
                fi
            done
        else
            log_warning "Sentinel directory or configuration directory not found for instance: $name"
        fi
    fi
done

print_header "SCAN COMPLETED"
log_success "Redis Sentinel scan completed successfully"