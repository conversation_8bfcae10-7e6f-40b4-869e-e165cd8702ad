#!/bin/bash

################################################################################
# Redis Status Update Script
################################################################################
#
# Description:
#   This script scans all Redis instances on the local machine,
#   extracts configuration and runtime information, and updates the database
#   with current status information.
#
# Author: cuiyi01
# Version: 2.0
# Last Modified: 2025-06-27
#
################################################################################

# ==============================================================================
# CONFIGURATION SECTION
# ==============================================================================

# System configuration
readonly HOST_IP=$(hostname -i)
readonly REDIS_ROOT="/home/<USER>"
readonly REDIS_CLI="/home/<USER>/baixin-product-test/redis/bin/redis-cli"

# Database connection information
readonly DB_HOST="*************"
readonly DB_PORT="6203"
readonly DB_USER="paas_x1_w"
readonly DB_PASS="paas_x1_w_pwd"
readonly DB_NAME="bce_scs_x1_task"

# Color definitions for console output
readonly COLOR_RESET='\033[0m'
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[0;33m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_BOLD='\033[1m'

# ==============================================================================
# VALIDATION AND INITIALIZATION
# ==============================================================================

# Check if redis-cli exists
if [ ! -f "$REDIS_CLI" ]; then
    printf "${COLOR_RED}ERROR: redis-cli not found at $REDIS_CLI${COLOR_RESET}\n" >&2
    exit 1
fi

# ==============================================================================
# UTILITY FUNCTIONS
# ==============================================================================

# Function: Print colored log message with timestamp
log_message() {
    local level="$1"
    local message="$2"
    local color="$3"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    if [ -n "$color" ]; then
        printf "${color}[%s] [%s] %s${COLOR_RESET}\n" "$timestamp" "$level" "$message"
    else
        printf "[%s] [%s] %s\n" "$timestamp" "$level" "$message"
    fi
}

# Function: Log info message
log_info() {
    log_message "INFO" "$1" "$COLOR_CYAN"
}

# Function: Log success message
log_success() {
    log_message "SUCCESS" "$1" "$COLOR_GREEN"
}

# Function: Log warning message
log_warning() {
    log_message "WARNING" "$1" "$COLOR_YELLOW"
}

# Function: Log error message
log_error() {
    log_message "ERROR" "$1" "$COLOR_RED"
}

# Function: Print section header
print_header() {
    local title="$1"
    local length=${#title}
    local padding=$((50 - length))
    local left_pad=$((padding / 2))
    local right_pad=$((padding - left_pad))

    printf "\n${COLOR_BOLD}${COLOR_BLUE}"
    printf "="%.0s {1..60}
    printf "\n"
    printf "%*s%s%*s\n" $left_pad "" "$title" $right_pad ""
    printf "="%.0s {1..60}
    printf "${COLOR_RESET}\n\n"
}

# Function: Execute MySQL command with error handling
mysql_command() {
    local sql="$1"
    local result

    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$sql" 2>&1)
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        return 0
    else
        log_error "Database operation failed: $result"
        return 1
    fi
}

# Function: Get password from configuration file
get_password() {
    local conf_file=$1
    grep -E "^requirepass|^auth-pass" "$conf_file" 2>/dev/null | head -1 | awk '{print $2}' | tr -d '"'
}

# Function: Execute Redis command
redis_command() {
    local host=$1
    local port=$2
    local password=$3
    local command=$4

    if [ -n "$password" ]; then
        $REDIS_CLI -h $host -p $port -a "$password" --no-auth-warning $command 2>/dev/null
    else
        $REDIS_CLI -h $host -p $port $command 2>/dev/null
    fi
}

# Function: Get Redis configuration item
get_redis_config() {
    local host=$1
    local port=$2
    local password=$3
    local config_key=$4

    local result=$(redis_command "$host" "$port" "$password" "CONFIG GET $config_key")
    if [ $? -eq 0 ] && [ -n "$result" ]; then
        echo "$result" | tail -1
    else
        echo "N/A"
    fi
}

# Function: Set Redis configuration item
set_redis_config() {
    local host=$1
    local port=$2
    local password=$3
    local config_key=$4
    local config_value=$5

    redis_command "$host" "$port" "$password" "CONFIG SET $config_key $config_value"
}

# Function: Check if directory exists
check_dir_exists() {
    local dir=$1
    if [ -d "$dir" ]; then
        echo "true"
    else
        echo "false"
    fi
}

# Function: Format memory size
format_memory() {
    local bytes=$1
    if [ -n "$bytes" ] && [ "$bytes" -gt 0 ]; then
        if [ $bytes -gt 1073741824 ]; then
            echo "$(($bytes / 1073741824))GB"
        elif [ $bytes -gt 1048576 ]; then
            echo "$(($bytes / 1048576))MB"
        elif [ $bytes -gt 1024 ]; then
            echo "$(($bytes / 1024))KB"
        else
            echo "${bytes}B"
        fi
    else
        echo "N/A"
    fi
}

# Function: Escape SQL string
escape_sql() {
    local str="$1"
    echo "$str" | sed "s/'/''/g"
}

# Function: Update database
update_database() {
    local uid="$1"
    local type="$2"
    local ip="$3"
    local port="$4"
    local name="$5"
    local redis_role="$6"
    local redis_master_host="$7"
    local redis_master_status="$8"
    local redis_dir="$9"
    local redis_dir_exist="${10}"
    local redis_appendonly="${11}"
    local redis_save="${12}"
    local redis_slave_priority="${13}"
    local redis_used_memory="${14}"

    # Escape special characters
    uid=$(escape_sql "$uid")
    redis_dir=$(escape_sql "$redis_dir")
    redis_save=$(escape_sql "$redis_save")
    redis_used_memory=$(escape_sql "$redis_used_memory")

    local sql="INSERT INTO all_infos (
        uid, type, ip, port, name,
        redis_role, redis_master_host, redis_master_status,
        redis_dir, redis_dir_exist, redis_appendonly,
        redis_save, redis_slave_priority, redis_used_memory,
        last_update_time
    ) VALUES (
        '$uid', '$type', '$ip', $port, '$name',
        '$redis_role', '$redis_master_host', '$redis_master_status',
        '$redis_dir', '$redis_dir_exist', '$redis_appendonly',
        '$redis_save', '$redis_slave_priority', '$redis_used_memory',
        NOW()
    ) ON DUPLICATE KEY UPDATE
        type='$type',
        ip='$ip',
        port=$port,
        name='$name',
        redis_role='$redis_role',
        redis_master_host='$redis_master_host',
        redis_master_status='$redis_master_status',
        redis_dir='$redis_dir',
        redis_dir_exist='$redis_dir_exist',
        redis_appendonly='$redis_appendonly',
        redis_save='$redis_save',
        redis_slave_priority='$redis_slave_priority',
        redis_used_memory='$redis_used_memory',
        last_update_time=NOW();"

    log_info "Updating database for UID: $uid"
    if mysql_command "$sql"; then
        log_success "Database update successful for $uid"
    else
        log_error "Database update failed for $uid"
    fi
}

# Function: Check and update slave-priority
check_and_update_priority() {
    local uid="$1"
    local host="$2"
    local port="$3"
    local password="$4"

    # Query expected slave-priority value
    local query_sql="SELECT redis_slave_priority_expect FROM all_infos WHERE uid='$uid' AND redis_slave_priority_expect != '' AND redis_slave_priority_expect != redis_slave_priority;"
    local expected_priority=$(mysql_command "$query_sql" | tail -1)

    if [ -n "$expected_priority" ] && [ "$expected_priority" != "redis_slave_priority_expect" ]; then
        log_info "Detected slave-priority needs update: $uid"
        log_info "Expected value: $expected_priority"

        # Update Redis configuration
        local result=$(set_redis_config "$host" "$port" "$password" "slave-priority" "$expected_priority")
        if [ $? -eq 0 ]; then
            log_success "Redis slave-priority update successful: $uid -> $expected_priority"

            # Update actual value in database
            local update_sql="UPDATE all_infos SET redis_slave_priority='$expected_priority' WHERE uid='$uid';"
            mysql_command "$update_sql"
            log_success "Database slave-priority sync completed: $uid"
        else
            log_error "Redis slave-priority update failed: $uid"
        fi
    fi
}

# ==============================================================================
# MAIN LOGIC
# ==============================================================================

print_header "REDIS STATUS UPDATE"
log_info "Starting Redis instance scan"
log_info "Host IP: $HOST_IP"

# Find all Redis instances
for redis_dir in $REDIS_ROOT/*/redis; do
    if [ -d "$redis_dir" ]; then
        name=$(basename $(dirname $redis_dir))
        conf_dir="$redis_dir/conf"

        if [ -d "$conf_dir" ]; then
            # Find configuration files
            for conf_file in $conf_dir/redis_*.conf; do
                if [ -f "$conf_file" ]; then
                    # Extract port from filename
                    port=$(basename "$conf_file" | sed 's/redis_\([0-9]*\)\.conf/\1/')

                    # Generate UID
                    uid="redis_${name}_${HOST_IP}_${port}"

                    # Get password
                    password=$(get_password "$conf_file")

                    log_info "Processing Redis instance: $name (Port: $port)"

                    # Display instance information
                    printf "\n${COLOR_BOLD}Redis Instance Details:${COLOR_RESET}\n"
                    printf "  %-20s %s\n" "UID:" "$uid"
                    printf "  %-20s %s\n" "Instance Name:" "$name"
                    printf "  %-20s %s\n" "Host IP:" "$HOST_IP"
                    printf "  %-20s %s\n" "Port:" "$port"
                    printf "  %-20s %s\n" "Config File:" "$conf_file"

                    # Try to connect to Redis and get information
                    info_replication=$(redis_command "127.0.0.1" "$port" "$password" "INFO replication")

                    if [ $? -eq 0 ] && [ -n "$info_replication" ]; then
                        # Parse replication information
                        role=$(echo "$info_replication" | grep "^role:" | cut -d: -f2 | tr -d '\r')
                        master_host=$(echo "$info_replication" | grep "^master_host:" | cut -d: -f2 | tr -d '\r')
                        master_link_status=$(echo "$info_replication" | grep "^master_link_status:" | cut -d: -f2 | tr -d '\r')

                        # Get configuration information separately
                        dir=$(get_redis_config "127.0.0.1" "$port" "$password" "dir")
                        appendonly=$(get_redis_config "127.0.0.1" "$port" "$password" "appendonly")
                        save=$(get_redis_config "127.0.0.1" "$port" "$password" "save")
                        slave_priority=$(get_redis_config "127.0.0.1" "$port" "$password" "slave-priority")

                        # Get memory usage information
                        memory_info=$(redis_command "127.0.0.1" "$port" "$password" "INFO memory")
                        used_memory_bytes=$(echo "$memory_info" | grep "^used_memory:" | cut -d: -f2 | tr -d '\r')
                        used_memory=$(format_memory $used_memory_bytes)

                        # Check if data directory exists
                        if [ "$dir" != "N/A" ]; then
                            dir_exists=$(check_dir_exists "$dir")
                        else
                            dir_exists="N/A"
                        fi

                        # Handle empty values
                        role=${role:-"N/A"}
                        master_host=${master_host:-"N/A"}
                        master_link_status=${master_link_status:-"N/A"}
                        dir=${dir:-"N/A"}
                        appendonly=${appendonly:-"N/A"}
                        save=${save:-"N/A"}
                        slave_priority=${slave_priority:-"N/A"}
                        used_memory=${used_memory:-"N/A"}

                        # Output information
                        printf "  %-20s %s\n" "Role:" "$role"
                        printf "  %-20s %s\n" "Master Host:" "$master_host"
                        printf "  %-20s %s\n" "Master Link Status:" "$master_link_status"
                        printf "  %-20s %s\n" "Data Directory:" "$dir"
                        printf "  %-20s %s\n" "Directory Exists:" "$dir_exists"
                        printf "  %-20s %s\n" "AOF Persistence:" "$appendonly"
                        printf "  %-20s %s\n" "RDB Save Policy:" "$save"
                        printf "  %-20s %s\n" "Slave Priority:" "$slave_priority"
                        printf "  %-20s %s\n" "Memory Usage:" "$used_memory"
                        printf "  %-20s %s\n" "Connection Status:" "${COLOR_GREEN}Connected${COLOR_RESET}"

                        # Update database
                        update_database "$uid" "redis" "$HOST_IP" "$port" "$name" \
                                       "$role" "$master_host" "$master_link_status" \
                                       "$dir" "$dir_exists" "$appendonly" \
                                       "$save" "$slave_priority" "$used_memory"

                        # Check and update slave-priority
                        check_and_update_priority "$uid" "127.0.0.1" "$port" "$password"

                    else
                        printf "  %-20s %s\n" "Connection Status:" "${COLOR_RED}Connection Failed${COLOR_RESET}"
                        log_warning "Failed to connect to Redis instance: $uid"

                        # Update database with basic information even if connection fails
                        update_database "$uid" "redis" "$HOST_IP" "$port" "$name" \
                                       "N/A" "N/A" "N/A" "N/A" "N/A" "N/A" "N/A" "N/A" "N/A"
                    fi

                    printf "\n${COLOR_CYAN}%s${COLOR_RESET}\n" "$(printf '%.0s-' {1..60})"
                fi
            done
        else
            log_warning "Configuration directory not found for instance: $name ($conf_dir)"
        fi
    fi
done

print_header "SCAN COMPLETED"
log_success "Redis instance scan completed successfully"