#!/usr/bin/env bash

################################################################################
# Redis Sentinel Failover Script
################################################################################
#
# Description:
#   This script performs Redis master failover operations through Redis Sentinel.
#   It supports both single task execution and batch processing mode.
#   The script includes comprehensive pre-checks, execution, and verification phases.
#
# Author: <EMAIL>
# Version: 1.0
# Last Modified: 2025-06-27
#
# Usage Examples:
#   Single task:
#     ./redis_sentinel_failover.sh --sentinel-address 127.0.0.1:26379 \
#                                  --master-name mymaster \
#                                  --new-master ************:6379 \
#                                  --old-master ************:6379
#
#   Batch mode:
#     ./redis_sentinel_failover.sh -f batch_tasks.csv --dry-run
#
#   With authentication:
#     ./redis_sentinel_failover.sh --sentinel-address 127.0.0.1:26379 \
#                                  --master-name mymaster \
#                                  --new-master ************:6379 \
#                                  --old-master ************:6379 \
#                                  --sentinel-password sentinel_pass \
#                                  --redis-password redis_pass
#
################################################################################

# ==============================================================================
# CONFIGURATION SECTION
# ==============================================================================

# Verification timeout settings
readonly VERIFICATION_TIMEOUT=60        # Maximum time to wait for failover completion (seconds)
readonly VERIFICATION_INTERVAL=5        # Interval between verification checks (seconds)

# Default redis-cli path (can be overridden by --redis-cli-path parameter)
DEFAULT_REDIS_CLI="redis-cli"

# Default Redis password (can be overridden by --redis-password parameter)
DEFAULT_REDIS_PASSWORD="your_default_redis_password_here"

# Color definitions for console output
readonly COLOR_RESET='\033[0m'
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[0;33m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_BLUE='\033[0;34m'

# Global variables for logging
LOG_FILE=""
ENABLE_FILE_LOGGING="false"

# ==============================================================================
# LOGGING FUNCTIONS
# ==============================================================================

# Base logging function with timestamp and color support
log() {
    local level_color="$1"
    local level_text="$2"
    local message="$3"
    local timestamp="$(date '+%Y-%m-%d %H:%M:%S')"
    local log_line="${timestamp} [${level_text}] ${message}"
    local colored_log_line="${timestamp} ${level_color}[${level_text}]${COLOR_RESET} ${message}"

    # Always output to stdout with colors
    echo -e "${colored_log_line}"

    # Also output to log file if file logging is enabled
    if [[ "${ENABLE_FILE_LOGGING}" == "true" && -n "${LOG_FILE}" ]]; then
        echo -e "${log_line}" >> "${LOG_FILE}"
    fi
}

# Specific log level functions
log_info() { log "${COLOR_CYAN}" "INFO" "$1"; }
log_ok() { log "${COLOR_GREEN}" "OK" "$1"; }
log_warn() { log "${COLOR_YELLOW}" "WARN" "$1"; }
log_error() { log "${COLOR_RED}" "ERROR" "$1"; }
log_dryrun() { log "${COLOR_BLUE}" "DRY-RUN" "$1"; }

# ==============================================================================
# HELP AND USAGE FUNCTIONS
# ==============================================================================

# Display usage information and exit
usage() {
    cat << EOF
Usage: $0 [options]

MODES:
  Single Task Mode:
    --sentinel-address <host:port>    Redis Sentinel address
    --master-name <name>              Master name in Sentinel configuration
    --new-master <host:port>          Target new master address
    --old-master <host:port>          Current master address (for verification)

  Batch Processing Mode:
    -f, --batch-file <path>           CSV file with batch tasks
                                      Format: sentinel_addr,master_name,new_master,old_master

COMMON OPTIONS:
    --sentinel-password <password>    Password for Sentinel authentication
    --redis-password <password>       Password for Redis node authentication (default: uses built-in password)
    --redis-cli-path <path>           Path to redis-cli executable (default: redis-cli)
    --dry-run                         Perform checks only, don't execute failover
    -h, --help                        Show this help message

EXAMPLES:
    # Single task with dry-run
    $0 --sentinel-address 127.0.0.1:26379 --master-name mymaster \\
       --new-master ************:6379 --old-master ************:6379 --dry-run

    # Batch processing
    $0 -f failover_tasks.csv --redis-password mypass

    # With authentication
    $0 --sentinel-address 127.0.0.1:26379 --master-name mymaster \\
       --new-master ************:6379 --old-master ************:6379 \\
       --sentinel-password sentinelpass --redis-password redispass

    # With custom redis-cli path
    $0 --sentinel-address 127.0.0.1:26379 --master-name mymaster \\
       --new-master ************:6379 --old-master ************:6379 \\
       --redis-cli-path /usr/local/bin/redis-cli

EOF
    exit 1
}

# ==============================================================================
# REDIS SENTINEL UTILITY FUNCTIONS
# ==============================================================================

# Get current master address from Sentinel
# Args: redis_cli_path, sentinel_host, sentinel_port, sentinel_auth_arg, master_name
# Returns: master_address in format "host:port" or empty string on error
get_current_master_addr() {
    local redis_cli_path="$1"
    local sentinel_host="$2"
    local sentinel_port="$3"
    local sentinel_auth_arg="$4"
    local master_name="$5"

    # shellcheck disable=SC2086
    local master_info
    master_info=$("$redis_cli_path" -h "$sentinel_host" -p "$sentinel_port" ${sentinel_auth_arg} \
                  SENTINEL get-master-addr-by-name "$master_name" 2>/dev/null)

    if [[ -n "$master_info" ]]; then
        echo "$master_info" | tr '\n' ':' | sed 's/:$//'
    fi
}

# Get list of slave addresses from Sentinel
# Args: redis_cli_path, sentinel_host, sentinel_port, sentinel_auth_arg, master_name
# Returns: list of slave addresses in format "host:port", one per line
get_slave_list() {
    local redis_cli_path="$1"
    local sentinel_host="$2"
    local sentinel_port="$3"
    local sentinel_auth_arg="$4"
    local master_name="$5"

    # shellcheck disable=SC2086
    "$redis_cli_path" -h "$sentinel_host" -p "$sentinel_port" ${sentinel_auth_arg} \
                      SENTINEL slaves "$master_name" 2>/dev/null | \
                      grep -w -E 'ip|port' -A 1 | \
                      paste -d ' ' - - | \
                      awk '{if ($1 == "ip") ip=$2; if ($1 == "port") print ip ":" $2}'
}

# Get slave synchronization status
# Args: redis_cli_path, slave_host, slave_port, redis_node_auth_arg
# Returns: "up", "down", or "connection_error"
get_slave_sync_status() {
    local redis_cli_path="$1"
    local slave_host="$2"
    local slave_port="$3"
    local redis_node_auth_arg="$4"

    # shellcheck disable=SC2086
    local replication_info
    replication_info=$("$redis_cli_path" -h "$slave_host" -p "$slave_port" ${redis_node_auth_arg} \
                       INFO replication 2>/dev/null)

    if [[ -n "$replication_info" ]]; then
        echo "$replication_info" | grep "master_link_status" | cut -d':' -f2 | tr -d '\r'
    else
        echo "connection_error"
    fi
}

# Get slave priority configuration
# Args: redis_cli_path, slave_host, slave_port, redis_node_auth_arg
# Returns: priority number or "connection_error"
get_slave_priority() {
    local redis_cli_path="$1"
    local slave_host="$2"
    local slave_port="$3"
    local redis_node_auth_arg="$4"

    # shellcheck disable=SC2086
    local config_info
    config_info=$("$redis_cli_path" -h "$slave_host" -p "$slave_port" ${redis_node_auth_arg} \
                  CONFIG GET slave-priority 2>/dev/null)

    # redis-cli CONFIG GET output is 2 lines: the key, then the value. We need the value.
    if [[ -n "$config_info" ]]; then
        echo "$config_info" | awk 'NR==2' | tr -d '\r'
    else
        echo "connection_error"
    fi
}

# ==============================================================================
# MAIN FAILOVER LOGIC FUNCTIONS
# ==============================================================================

# Perform Redis master failover operation
# Args: redis_cli_path, sentinel_address, master_name, new_master_address, old_master_address,
#       dry_run_mode, redis_node_password, sentinel_password
# Returns: 0 on success, 1 on failure
perform_failover() {
    local redis_cli_path="$1"
    local sentinel_address="$2"
    local master_name="$3"
    local new_master_address="$4"
    local old_master_address="$5"
    local dry_run_mode="$6"
    local redis_node_password="$7"
    local sentinel_password="$8"

    # Log operation start
    if [[ "${dry_run_mode}" == "true" ]]; then
        log_info "--- [Dry Run] Starting checks for Master '${master_name}' ---"
    else
        log_info "--- Starting failover task for Master '${master_name}' ---"
    fi
    log_info "Planning to switch Master from '${old_master_address}' to '${new_master_address}'"

    # Parse sentinel address and prepare authentication arguments
    local sentinel_host="${sentinel_address%:*}"
    local sentinel_port="${sentinel_address#*:}"
    local sentinel_auth_arg=""
    local redis_node_auth_arg=""

    if [[ -n "${sentinel_password}" ]]; then
        sentinel_auth_arg="-a ${sentinel_password}"
    fi
    if [[ -n "${redis_node_password}" ]]; then
        redis_node_auth_arg="-a ${redis_node_password}"
    fi

    # -------------------------------------------------------------------------
    # PHASE 1: PRE-FLIGHT CHECKS
    # -------------------------------------------------------------------------
    log_info "--- [Phase 1/4] Starting pre-flight checks ---"

    # Check 1: Verify current master address
    log_info "Check: Verifying current Master address..."
    local current_master_addr
    current_master_addr=$(get_current_master_addr "${redis_cli_path}" "${sentinel_host}" "${sentinel_port}" \
                          "${sentinel_auth_arg}" "${master_name}")

    if [[ -z "$current_master_addr" ]]; then
        log_error "Unable to get Master '${master_name}' information from Sentinel."
        return 1
    fi

    if [[ "${current_master_addr}" != "${old_master_address}" ]]; then
        log_error "Specified old Master '${old_master_address}' does not match discovered '${current_master_addr}'."
        return 1
    fi
    log_ok "Current Master address '${old_master_address}' verified successfully."

    # Check 2: Get slave list and verify new master is a known slave
    log_info "Check: Getting Slave node information from Sentinel..."
    local slave_list
    slave_list=$(get_slave_list "${redis_cli_path}" "${sentinel_host}" "${sentinel_port}" \
                 "${sentinel_auth_arg}" "${master_name}")

    if [[ -z "$slave_list" ]]; then
        log_error "Sentinel reports no Slave nodes for Master '${master_name}', cannot perform failover."
        return 1
    fi

    if ! echo "${slave_list}" | grep -qF "${new_master_address}"; then
        log_error "Specified new Master '${new_master_address}' is not a known Slave."
        return 1
    fi
    log_ok "New Master '${new_master_address}' confirmed as known Slave."

    # Check 3: Verify new master synchronization status
    log_info "Check: Verifying new Master node synchronization status..."
    local sync_status
    sync_status=$(get_slave_sync_status "${redis_cli_path}" "${new_master_address%:*}" "${new_master_address#*:}" \
                  "${redis_node_auth_arg}")

    if [[ "$sync_status" == "connection_error" ]]; then
        log_error "Unable to connect to new Master '${new_master_address}' or Redis password error."
        return 1
    fi

    if [[ "$sync_status" != "up" ]]; then
        log_error "New Master '${new_master_address}' sync status abnormal: '${sync_status}' (should be 'up')"
        return 1
    fi
    log_ok "New Master '${new_master_address}' synchronization status normal ('up')."

    # Check 4: Verify slave priority configuration
    log_info "Check: Verifying slave-priority configuration for all Slave nodes..."
    local priority_check_ok=true

    for slave_addr in ${slave_list}; do
        local slave_host="${slave_addr%:*}"
        local slave_port="${slave_addr#*:}"
        local priority
        priority=$(get_slave_priority "${redis_cli_path}" "$slave_host" "$slave_port" "${redis_node_auth_arg}")

        if [[ "$priority" == "connection_error" ]]; then
            log_error "Unable to get slave-priority for Slave '${slave_addr}', connection failed or password error."
            priority_check_ok=false
            break
        fi

        if [[ "$slave_addr" == "$new_master_address" ]]; then
            if [[ "$priority" != "1" ]]; then
                log_error "New Master '${slave_addr}' slave-priority is '${priority}', must be '1'."
                priority_check_ok=false
                break
            else
                log_ok "New Master '${slave_addr}' slave-priority is correct ('1')."
            fi
        else
            if [[ "$priority" != "100" ]]; then
                log_error "Other Slave '${slave_addr}' slave-priority is '${priority}', must be '100'."
                priority_check_ok=false
                break
            else
                log_ok "Other Slave '${slave_addr}' slave-priority is correct ('100')."
            fi
        fi
    done

    if [[ "$priority_check_ok" != "true" ]]; then
        log_error "Slave-priority configuration check failed, terminating operation."
        return 1
    fi

    # If dry-run mode, exit here after successful checks
    if [[ "${dry_run_mode}" == "true" ]]; then
        log_dryrun "All pre-flight checks passed. Safe to execute failover."
        return 0
    fi

    # -------------------------------------------------------------------------
    # PHASE 2: EXECUTE FAILOVER
    # -------------------------------------------------------------------------
    log_info "--- [Phase 2/4] All pre-flight checks passed, preparing to execute failover ---"

    # Execute failover command
    # shellcheck disable=SC2086
    local failover_result
    failover_result=$("$redis_cli_path" -h "${sentinel_host}" -p "${sentinel_port}" ${sentinel_auth_arg} \
                      SENTINEL failover "${master_name}")

    if [[ "$?" -ne 0 ]] || [[ "${failover_result}" != "OK" ]]; then
        log_error "Failed to send failover command: ${failover_result}"
        return 1
    fi
    log_info "Sent 'SENTINEL failover ${master_name}' command to Sentinel, waiting for completion..."

    # -------------------------------------------------------------------------
    # PHASE 3: VERIFY FAILOVER COMPLETION
    # -------------------------------------------------------------------------
    log_info "--- [Phase 3/4] Starting failover result verification (max wait ${VERIFICATION_TIMEOUT} seconds) ---"

    local elapsed_time=0
    while [[ ${elapsed_time} -lt ${VERIFICATION_TIMEOUT} ]]; do
        local final_master_addr
        final_master_addr=$(get_current_master_addr "${redis_cli_path}" "${sentinel_host}" "${sentinel_port}" \
                            "${sentinel_auth_arg}" "${master_name}")

        if [[ "${final_master_addr}" == "${new_master_address}" ]]; then
            log_ok "Verification successful! Master has switched to '${new_master_address}'."
            log_info "--- [Phase 4/4] Task '${master_name}' completed successfully ---"
            return 0
        fi

        log_info "Waiting... Current Master is '${final_master_addr}'. (${elapsed_time}s / ${VERIFICATION_TIMEOUT}s)"
        sleep ${VERIFICATION_INTERVAL}
        elapsed_time=$((elapsed_time + VERIFICATION_INTERVAL))
    done

    # -------------------------------------------------------------------------
    # PHASE 4: HANDLE TIMEOUT/FAILURE
    # -------------------------------------------------------------------------
    local current_master_after_timeout
    current_master_after_timeout=$(get_current_master_addr "${redis_cli_path}" "${sentinel_host}" "${sentinel_port}" \
                                   "${sentinel_auth_arg}" "${master_name}")

    log_error "Verification failed! Timeout reached. Current Master is still '${current_master_after_timeout}'."
    log_info "--- [Phase 4/4] Task processing failed ---"
    return 1
}

# ==============================================================================
# MAIN PROGRAM ENTRY POINT
# ==============================================================================

# Main function - program entry point
main() {
    if [[ $# -eq 0 ]]; then
        usage
    fi

    # Initialize variables
    local batch_file=""
    local sentinel_address=""
    local master_name=""
    local new_master=""
    local old_master=""
    local redis_password="${DEFAULT_REDIS_PASSWORD}"
    local sentinel_password=""
    local redis_cli_path="${DEFAULT_REDIS_CLI}"
    local dry_run_mode="false"
    # Parse command line arguments
    while [[ "$#" -gt 0 ]]; do
        case "$1" in
            --sentinel-address)
                sentinel_address="$2"
                shift 2
                ;;
            --master-name)
                master_name="$2"
                shift 2
                ;;
            --new-master)
                new_master="$2"
                shift 2
                ;;
            --old-master)
                old_master="$2"
                shift 2
                ;;
            --redis-password)
                redis_password="$2"
                shift 2
                ;;
            --sentinel-password)
                sentinel_password="$2"
                shift 2
                ;;
            --redis-cli-path)
                redis_cli_path="$2"
                shift 2
                ;;
            -f|--batch-file)
                batch_file="$2"
                shift 2
                ;;
            --dry-run)
                dry_run_mode="true"
                shift 1
                ;;
            -h|--help)
                usage
                ;;
            *)
                log_error "Unknown parameter: $1"
                usage
                ;;
        esac
    done

    # Execute based on mode (batch or single task)
    if [[ -n "${batch_file}" ]]; then
        # =====================================================================
        # BATCH PROCESSING MODE
        # =====================================================================
        if [[ ! -f "${batch_file}" ]]; then
            log_error "Batch file not found: ${batch_file}"
            exit 1
        fi

        # Initialize log file for batch mode
        LOG_FILE="${batch_file}.log"
        ENABLE_FILE_LOGGING="true"

        # Create/clear the log file
        > "${LOG_FILE}"

        log_info "--- Entering batch processing mode, reading file: ${batch_file} ---"
        log_info "Log output will be written to: ${LOG_FILE}"
        if [[ "${dry_run_mode}" == "true" ]]; then
            log_dryrun "Dry Run mode activated"
        fi

        # Check if redis-cli is accessible
        if [[ "$redis_cli_path" == /* ]]; then
            # Absolute path - check if file exists and is executable
            if [[ ! -x "$redis_cli_path" ]]; then
                log_error "Redis CLI not found or not executable: '$redis_cli_path'. Please check path and permissions."
                exit 1
            fi
        else
            # Relative path or command name - use command -v
            if ! command -v "$redis_cli_path" &> /dev/null; then
                log_error "Redis CLI not found: '$redis_cli_path'. Please check path or ensure Redis CLI is installed."
                exit 1
            fi
        fi

        local total_tasks=0
        local success_count=0

        # Process each line in the batch file
        while IFS=, read -r s_addr m_name n_master o_master || [[ -n "${s_addr}" ]]; do
            # Skip empty lines and comments
            [[ -z "${s_addr}" || "${s_addr}" =~ ^# ]] && continue

            # Trim whitespace from all fields
            s_addr=$(echo "${s_addr}" | tr -d '[:space:]')
            m_name=$(echo "${m_name}" | tr -d '[:space:]')
            n_master=$(echo "${n_master}" | tr -d '[:space:]')
            o_master=$(echo "${o_master}" | tr -d '[:space:]')

            total_tasks=$((total_tasks + 1))

            if perform_failover "${redis_cli_path}" "${s_addr}" "${m_name}" "${n_master}" "${o_master}" \
                               "${dry_run_mode}" "${redis_password}" "${sentinel_password}"; then
                success_count=$((success_count + 1))
            fi

            echo "--------------------------------------------------------------------------------"
        done < "${batch_file}"

        # Report batch processing results
        log_info "--- Batch task processing completed ---"
        local failed_count=$((total_tasks - success_count))
        log_info "Total tasks: ${total_tasks}, Success/Checks passed: ${success_count}, Failed/Checks failed: ${failed_count}"

        if [[ ${failed_count} -gt 0 ]]; then
            exit 1
        else
            exit 0
        fi
    else
        # =====================================================================
        # SINGLE TASK MODE
        # =====================================================================
        if [[ -z "${sentinel_address}" ]] || [[ -z "${master_name}" ]] || \
           [[ -z "${new_master}" ]] || [[ -z "${old_master}" ]]; then
            log_error "In single task mode, all four address/name parameters are required."
            usage
        fi

        if [[ "${dry_run_mode}" == "true" ]]; then
            log_dryrun "Dry Run mode activated"
        fi

        # Check if redis-cli is accessible
        if [[ "$redis_cli_path" == /* ]]; then
            # Absolute path - check if file exists and is executable
            if [[ ! -x "$redis_cli_path" ]]; then
                log_error "Redis CLI not found or not executable: '$redis_cli_path'. Please check path and permissions."
                exit 1
            fi
        else
            # Relative path or command name - use command -v
            if ! command -v "$redis_cli_path" &> /dev/null; then
                log_error "Redis CLI not found: '$redis_cli_path'. Please check path or ensure Redis CLI is installed."
                exit 1
            fi
        fi

        if perform_failover "${redis_cli_path}" "${sentinel_address}" "${master_name}" "${new_master}" "${old_master}" \
                           "${dry_run_mode}" "${redis_password}" "${sentinel_password}"; then
            exit 0
        else
            exit 1
        fi
    fi
}

# ==============================================================================
# SCRIPT EXECUTION
# ==============================================================================

# Execute main function with all command line arguments
main "$@"
