#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# inspect redis ttl

作者: cuiyi01(<EMAIL>)
日期: 2025年05月18日 上午11:12:55
"""
import commands
import re
import sys

try:
    import pymysql
except ImportError:
    import MySQLdb as pymysql
import redis

def get_mysql_config(db=None):
    """get mysql config
    """
    mysql_conf = "/home/<USER>/scs/csmaster/conf/mysql.conf"
    with open(mysql_conf, "rt") as f:
        lines = f.readlines()
    ret = dict()
    for line in lines:
        line_sp = line.strip().split(":")
        if len(line_sp) > 1:
            if line_sp[0].strip() == "User":
                ret["user"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Password":
                ret["passwd"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Database":
                ret["db"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Ip":
                ret["host"] = line_sp[1].strip()
            elif line_sp[0].strip() == "Port":
                ret["port"] = int(line_sp[1].strip())
                break
    if db is not None:
        ret["db"] = db
    return ret

def get_redis_meta_infos():
    """get redis meta infos
    """
    mysql_config = get_mysql_config()
    conn = pymysql.connect(host=mysql_config["host"], port=mysql_config["port"],
                           user=mysql_config["user"], passwd=mysql_config["passwd"],
                           db=mysql_config["db"], charset='utf8')
    cursor = conn.cursor()
    cursor.execute("SELECT ip, port FROM redis_meta")
    redis_metas = cursor.fetchall()
    cursor.close()
    conn.close()
    return redis_metas
