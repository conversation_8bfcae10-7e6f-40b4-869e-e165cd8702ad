#!/usr/bin/env bash

set -x
UUID=$1
CONTAINER_PATH=/home/<USER>/$UUID
XCACHE_PATH_NAME=`ls -l $CONTAINER_PATH | grep xcache | awk '{print $9}'`
AGENT_PATH=$CONTAINER_PATH/${XCACHE_PATH_NAME}/agent


NOW_TIME=`date +%Y%m%s%H%M%S`
cp -r $AGENT_PATH/src/agent.py $AGENT_PATH/src/agent.py_$NOW_TIME

PWD=`pwd`
cd $AGENT_PATH

AGENT_PID=`ps -ef | grep $AGENT_PATH/src/agent.py | grep -v grep | awk '{print $2}'`

#kill -9 $AGENT_PID
#sleep 1
#nohup python $$AGENT_PATH/src/agent.py start &
pwd
cd $PWD