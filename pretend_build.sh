mkdir -p output/
cp -fr  scs_monitor output/scs_monitor
cp -fr deploy_tools output/deploy_tools
cp -fr res_statistics_tools output/res_statistics_tools
cp -fr bns_tools output/bns_tools
cp -fr scs-third-party-tools output/scs-third-party-tools
cp -fr bcm_tools output/bcm_tools
cp -fr newbins output/newbins
cp -fr monitor_tools output/monitor_tools
cp -fr sla_tools output/sla_tools


cp -fr remote_tools output/remote_tools
mv output/remote_tools/remote_tools.py output/remote_tools/remote_tools
mkdir -p output/remote_tools/data
chmod +x output/remote_tools/remote_tools
chmod +x output/remote_tools/tools/sshpass

echo "success"
